<!DOCTYPE html>
<html>
<head>
    <title>测试幻灯片功能</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 10px; border: 1px solid #ccc; }
        .result { background: #f0f0f0; padding: 10px; margin: 10px 0; }
        button { margin: 5px; padding: 10px; }
    </style>
</head>
<body>
    <h1>幻灯片功能测试</h1>
    
    <div class="test-section">
        <h2>测试 getLineNumberFromSlideIndex 函数</h2>
        <button onclick="testGetLineNumber()">运行测试</button>
        <div id="lineNumberResults" class="result"></div>
    </div>

    <div class="test-section">
        <h2>模拟幻灯片切换消息</h2>
        <button onclick="simulateSlideChange(0, 0)">切换到 (0,0)</button>
        <button onclick="simulateSlideChange(1, 0)">切换到 (1,0)</button>
        <button onclick="simulateSlideChange(1, 1)">切换到 (1,1)</button>
        <button onclick="simulateSlideChange(2, 0)">切换到 (2,0)</button>
        <div id="slideChangeResults" class="result"></div>
    </div>

    <script>
        // 复制 getLineNumberFromSlideIndex 函数
        function getLineNumberFromSlideIndex(content, indexh, indexv) {
            const lines = content.split('\n');
            let currentH = 0;
            let currentV = 0;

            for (let i = 0; i < lines.length; i++) {
                const line = lines[i];
                
                if (/^--- *$/.test(line)) {
                    currentH++;
                    currentV = 0;
                    
                    if (currentH === indexh && currentV === indexv) {
                        return i + 2;
                    }
                } else if (/^-- *$/.test(line)) {
                    currentV++;
                    
                    if (currentH === indexh && currentV === indexv) {
                        return i + 2;
                    }
                }
            }

            if (indexh === 0 && indexv === 0) {
                return 1;
            }

            return 1;
        }

        // 测试数据
        const sampleMarkdown = `# 第一张幻灯片
这是第一张幻灯片的内容

---

# 第二张幻灯片
这是第二张幻灯片的内容

--

# 第二张幻灯片的子页面
这是第二张幻灯片的子页面内容

---

# 第三张幻灯片
这是第三张幻灯片的内容`;

        function testGetLineNumber() {
            const results = [];
            
            // 测试用例
            const testCases = [
                { indexh: 0, indexv: 0, expected: 1 },
                { indexh: 1, indexv: 0, expected: 6 },
                { indexh: 1, indexv: 1, expected: 11 },
                { indexh: 2, indexv: 0, expected: 16 }
            ];

            testCases.forEach(testCase => {
                const result = getLineNumberFromSlideIndex(sampleMarkdown, testCase.indexh, testCase.indexv);
                const passed = result === testCase.expected;
                results.push(`测试 (${testCase.indexh},${testCase.indexv}): 期望 ${testCase.expected}, 实际 ${result} ${passed ? '✅' : '❌'}`);
            });

            // 显示每行内容
            results.push('\n每行内容:');
            sampleMarkdown.split('\n').forEach((line, index) => {
                results.push(`${index + 1}: ${line}`);
            });

            document.getElementById('lineNumberResults').innerHTML = results.join('<br>');
        }

        function simulateSlideChange(indexh, indexv) {
            const lineNumber = getLineNumberFromSlideIndex(sampleMarkdown, indexh, indexv);
            const message = `幻灯片切换到 (${indexh}, ${indexv}), 应该滚动到第 ${lineNumber} 行`;
            
            // 模拟 handleSlideChange 函数
            console.log('模拟滚动到行:', lineNumber);
            
            document.getElementById('slideChangeResults').innerHTML = message;
        }

        // 页面加载时运行测试
        window.onload = function() {
            testGetLineNumber();
        };
    </script>
</body>
</html>
