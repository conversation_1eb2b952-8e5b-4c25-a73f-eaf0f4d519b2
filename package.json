{"name": "funblocks", "version": "700", "private": true, "dependencies": {"@codemirror/commands": "^6.2.4", "@codemirror/lang-markdown": "^6.1.1", "@codemirror/language-data": "^6.3.1", "@codemirror/state": "^6.2.1", "@codemirror/theme-one-dark": "^6.1.2", "@codemirror/view": "^6.12.0", "@emotion/react": "^11.7.1", "@emotion/styled": "^11.6.0", "@faker-js/faker": "^6.3.1", "@lexical/react": "^0.15.0", "@mui/icons-material": "^6.1.2", "@mui/lab": "^5.0.0-alpha.119", "@mui/material": "^5.11.8", "@mui/styles": "^6.1.2", "@popperjs/core": "^2.11.3", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-dialog": "1.0.4", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-toggle": "^1.0.3", "@radix-ui/react-toolbar": "^1.0.4", "@radix-ui/react-tooltip": "^1.0.7", "@rajesh896/broprint.js": "^2.1.1", "@react-oauth/google": "^0.12.1", "@stripe/react-stripe-js": "^2.2.0", "@stripe/stripe-js": "^2.1.2", "@styled-icons/bootstrap": "^10.47.0", "@styled-icons/boxicons-regular": "10.23.0", "@styled-icons/entypo": "^10.34.0", "@styled-icons/fluentui-system-filled": "^10.35.0", "@styled-icons/fluentui-system-regular": "^10.46.0", "@styled-icons/foundation": "10.28.0", "@styled-icons/ionicons-outline": "^10.34.0", "@styled-icons/ionicons-sharp": "^10.34.0", "@styled-icons/ionicons-solid": "^10.46.0", "@styled-icons/material": "10.28.0", "@styled-icons/material-outlined": "^10.34.0", "@styled-icons/remix-editor": "^10.33.0", "@styled-icons/remix-fill": "^10.46.0", "@styled-icons/remix-line": "^10.46.0", "@styled-icons/zondicons": "^10.46.0", "@tanstack/react-table": "8.0.0-alpha.72", "@testing-library/jest-dom": "^5.14.1", "@testing-library/react": "^13.0.0", "@testing-library/user-event": "^13.2.1", "@tippyjs/react": "4.2.5", "@types/jest": "^27.0.1", "@types/node": "^16.7.13", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@udecode/plate": "^25.0.1", "@udecode/plate-alignment": "^25.0.1", "@udecode/plate-autoformat": "^25.0.1", "@udecode/plate-basic-marks": "^25.0.1", "@udecode/plate-block-quote": "^25.0.1", "@udecode/plate-break": "^25.0.1", "@udecode/plate-caption": "^25.0.1", "@udecode/plate-code-block": "^25.0.1", "@udecode/plate-combobox": "^25.0.1", "@udecode/plate-comments": "^26.0.0", "@udecode/plate-common": "^25.0.1", "@udecode/plate-cursor": "^25.0.1", "@udecode/plate-dnd": "^25.0.1", "@udecode/plate-emoji": "^25.0.1", "@udecode/plate-excalidraw": "^25.0.1", "@udecode/plate-floating": "^25.0.1", "@udecode/plate-font": "^25.0.1", "@udecode/plate-heading": "^25.0.1", "@udecode/plate-highlight": "^25.0.1", "@udecode/plate-horizontal-rule": "^25.0.1", "@udecode/plate-indent": "^25.0.1", "@udecode/plate-indent-list": "^25.0.1", "@udecode/plate-juice": "^25.0.1", "@udecode/plate-kbd": "^25.0.1", "@udecode/plate-line-height": "^25.0.1", "@udecode/plate-link": "^25.0.1", "@udecode/plate-list": "^25.0.1", "@udecode/plate-media": "^25.0.1", "@udecode/plate-mention": "^25.0.1", "@udecode/plate-node-id": "^25.0.1", "@udecode/plate-paragraph": "^25.0.1", "@udecode/plate-reset-node": "^25.0.1", "@udecode/plate-select": "^25.0.1", "@udecode/plate-selection": "^25.0.1", "@udecode/plate-serializer-csv": "^26.0.3", "@udecode/plate-serializer-docx": "^26.0.3", "@udecode/plate-serializer-html": "^25.0.1", "@udecode/plate-serializer-md": "^25.0.1", "@udecode/plate-tabbable": "^25.0.1", "@udecode/plate-table": "^26.0.3", "@udecode/plate-trailing-block": "^25.0.1", "@uiw/codemirror-extensions-events": "^4.21.7", "@wojtekmaj/enzyme-adapter-react-17": "^0.6.5", "@xyflow/react": "^12.3.4", "autoprefixer": "^10.0.2", "bootstrap": "^5.1.3", "chart.js": "3.8.0", "class-variance-authority": "^0.7.0", "classnames": "^2.3.1", "clsx": "^2.0.0", "cmdk": "^0.2.0", "codemirror": "^6.0.1", "core-js": "^3.19.1", "create-react-context": "^0.3.0", "d3-force": "^3.0.0", "d3-quadtree": "^3.0.1", "date-fns": "^2.28.0", "elkjs": "^0.9.3", "enzyme": "^3.11.0", "fast-deep-equal": "^3.1.3", "framer-motion": "^11.3.19", "html-to-image": "^1.11.11", "http-proxy-middleware": "^2.0.6", "immutability-helper": "^3.1.1", "interactjs": "^1.10.11", "json5": "^2.2.3", "lexical": "^0.15.0", "lodash": "^4.17.21", "lucide-react": "^0.284.0", "marked": "^12.0.2", "memoize-one": "^6.0.0", "mermaid": "^11.2.0", "moment": "^2.29.1", "papaparse": "^5.4.1", "postcss": "^8.0.9", "prop-types": "^15.7.2", "qrcode": "^1.5.3", "react": "^18.2.0", "react-app-polyfill": "^2.0.0", "react-chartjs-2": "4.3.0", "react-contenteditable": "^3.3.7", "react-contextmenu": "^2.14.0", "react-date-range": "^1.4.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.2.0", "react-draggable": "^4.4.6", "react-error-boundary": "^3.1.4", "react-ga4": "^2.1.0", "react-icons": "^4.3.1", "react-intl": "^5.24.4", "react-is": "^17.0.2", "react-joyride": "^2.8.2", "react-katex": "^3.0.1", "react-lite-youtube-embed": "^2.3.52", "react-markdown": "^9.0.1", "react-papaparse": "^4.1.0", "react-pro-sidebar": "^0.7.1", "react-redux": "^7.2.6", "react-responsive": "^10.0.0", "react-router-dom": "5.3.0", "react-scripts": "5.0.0", "react-share": "^5.1.0", "react-syntax-highlighter": "^15.5.0", "react-trello": "^2.2.11", "react-tweet": "^3.1.1", "redux": "^4.1.2", "redux-form": "^8.1.0", "redux-logger": "^3.0.6", "redux-persist": "^6.0.0", "redux-thunk": "^2.2.0", "rehype-katex": "^7.0.0", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.0", "remark-html": "^16.0.1", "remark-math": "^6.0.0", "simplebar-react": "^2.3.6", "slate": "^0.101.1", "slate-history": "^0.100.0", "slate-hyperscript": "^0.100.0", "slate-react": "^0.101.1", "sortablejs": "^1.14.0", "tailwind-merge": "^1.14.0", "tailwindcss-animate": "^1.0.7", "turndown": "^7.1.3", "twin.macro": "2.6.2", "typescript": "^4.4.2", "underscore": "^1.13.2", "unified": "^11.0.4", "url-regex": "^5.0.0", "use-file-picker": "^1.4.2", "web-share-shim": "^1.0.4", "web-vitals": "^2.1.3", "xlsx": "^0.18.5", "zundo": "^2.1.0", "zustand": "^4.5.2"}, "scripts": {"eject": "react-scripts eject", "start": "craco start --host 0.0.0.0", "build": "craco build && gzipper compress --verbose ./build/static", "test": "craco test"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/core": "^7.17.5", "@babel/preset-react": "^7.16.7", "@craco/craco": "^6.4.3", "@ianvs/prettier-plugin-sort-imports": "^4.1.0", "@testing-library/jest-dom": "^5.16.1", "@testing-library/react": "^12.1.2", "@testing-library/user-event": "^13.5.0", "@types/faker": "^5.5.9", "@types/jest": "^27.4.0", "@types/node": "^17.0.8", "@types/react": "^17.0.38", "@types/react-dom": "^17.0.11", "@types/sortablejs": "^1.10.7", "@types/styled-components": "^5.1.20", "@typescript-eslint/parser": "^6.7.0", "autoprefixer": "^10.4.15", "babel-plugin-macros": "3.1.0", "babel-plugin-styled-components": "1.10.5", "babel-plugin-twin": "1.0.2", "encoding": "^0.1.13", "eslint": "^8.49.0", "eslint-config-next": "13.4.19", "eslint-config-prettier": "^9.0.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-tailwindcss": "^3.13.0", "eslint-plugin-unused-imports": "^3.0.0", "mini-css-extract-plugin": "^2.4.5", "postcss": "^8.4.29", "prettier": "^3.0.3", "sass": "^1.43.5", "styled-components": "5.3.3", "tailwindcss": "^3.3.3", "typescript": "^4.5.4"}, "babelMacros": {"twin": {"preset": "styled-components"}}, "config": {"coreui_library_short_version": "4.1"}}