import React from 'react'
import FlowEditor from './components/flow/FlowEditor';
import Editor from './views/pages/Editor';
import SlidesViewer from './views/pages/SlidesViewer';
import SlidesEditor from './views/pages/SlidesEditor';
import DBEditor from './views/pages/DBEditor';
import DataEditor from './views/pages/DataEditor';
import DBView from './components/dbeditor/DBView';
import ArticleReader from './views/pages/ArticleReader';
import WebPager from './views/pages/WebPager';
import PageNoAccess from './views/pages/PageNoAccess';
import SharedPrompt from './views/pages/SharedPrompt';

const routes = [
  { path: '/home', exact: true, name: 'Home' },

  { path: '/editor', name: 'Doc', component: Editor },
  { path: '/embed/editor', name: 'Doc', component: Editor },
  { path: '/db', name: '<PERSON>', component: DBEditor },
  { path: '/data', name: 'Data', component: DataEditor },
  { path: '/embed/db', name: 'DB', component: DBEditor },
  { path: '/embed/dbview', name: 'DBView', component: DBView },
  { path: '/article', name: 'Article', component: ArticleReader },
  { path: '/slidesViewer', name: 'SlidesViewer', component: SlidesViewer },
  { path: '/slidesEditor', name: 'SlidesEditor', component: SlidesEditor },
  { path: '/flow', name: 'Flow', component: FlowEditor },
  { path: '/webpager', name: 'WebPage', component: WebPager },
  { path: '/noaccess', name: 'NoAccess', component: PageNoAccess },
  { path: '/sharedPrompt', name: 'SharedPrompt', component: SharedPrompt },
]

export default routes
