import { DB_VIEW_ACTIONS } from '../constants/actionTypes'

export function byId(state = {}, action) {
    switch (action.type) {
        case DB_VIEW_ACTIONS.updated:
        case DB_VIEW_ACTIONS.received:
            return Object.assign({}, state, { [action.item._id]: action.item });

        case DB_VIEW_ACTIONS.invalidated:
            return Object.keys(state).reduce((result, key) => {
                if (key !== action.item._id) {
                    result[key] = state[key];
                }
                return result;
            }, {})
        default:
            return state;
    }
}


