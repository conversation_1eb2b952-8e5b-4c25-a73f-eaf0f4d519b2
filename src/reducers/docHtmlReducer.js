import { DOC_HTML_ACTIONS } from '../constants/actionTypes'

export function byId(state = {}, action) {
    switch (action.type) {
        case DOC_HTML_ACTIONS.updated:
        case DOC_HTML_ACTIONS.received:
            return Object.assign({}, state, { [action.item.hid]: action.item });

        case DOC_HTML_ACTIONS.invalidated:
            return Object.keys(state).reduce((result, key) => {
                if (key !== action.item.hid) {
                    result[key] = state[key];
                }
                return result;
            }, {})
        default:
            return state;
    }
}


