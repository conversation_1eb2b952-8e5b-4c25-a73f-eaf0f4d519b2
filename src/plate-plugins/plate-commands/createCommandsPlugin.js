import { createPluginFactory } from '@udecode/plate-common';
import { commandOnKeyDownHandler } from './handlers';
import { isSelectionInCommandInput } from './queries';
import { CommandsPlugin } from './types';
import { withCommands } from './withCommands';

export const ELEMENT_COMMAND = 'command';
export const ELEMENT_COMMAND_INPUT = 'command_input';

/**
 * Enables support for autocompleting @mentions.
 */
export const createCommandsPlugin = createPluginFactory({
  key: ELEMENT_COMMAND,
  isElement: true,
  isInline: true,
  isVoid: true,
  handlers: {
    onKeyDown: commandOnKeyDownHandler({ query: isSelectionInCommandInput }),
  },
  withOverrides: withCommands,
  options: {
    trigger: '/',
  },
  plugins: [
    {
      key: ELEMENT_COMMAND_INPUT,
      isElement: true,
      isInline: true,
    },
  ],
  then: (editor, { key }) => ({
    options: {
      id: key,
    },
  }),
});