import { PlateEditor } from '@udecode/plate-common';
import { findCommandInput } from '../queries';
import { removeCommandInput } from '../transforms';
import { KeyboardEventHandler } from './KeyboardEventHandler';
import {
  moveSelectionByOffset,
  MoveSelectionByOffsetOptions,
} from './moveSelectionByOffset';

export const commandOnKeyDownHandler: (
  options?: MoveSelectionByOffsetOptions
) => (editor: PlateEditor) => KeyboardEventHandler = (options) => (editor) => (
  event
) => {
  if (event.key === 'Escape') {
    event.preventDefault();
    const currentMentionInput = findCommandInput(editor)!;
    if (currentMentionInput) {
      removeCommandInput(editor, currentMentionInput[1]);
    }
    return true;
  }

  return moveSelectionByOffset(editor, options)(event);
};
