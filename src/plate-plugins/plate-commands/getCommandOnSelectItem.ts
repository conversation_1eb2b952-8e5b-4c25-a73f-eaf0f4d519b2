import {
  comboboxActions,
  comboboxSelectors,
} from '@udecode/plate-combobox';
import {
  getBlockAbove,
  getPlugin,
  PlatePluginKey,
} from '@udecode/plate-common';
import { Editor, Transforms } from 'slate';
import { HistoryEditor } from 'slate-history';
import { ELEMENT_COMMAND, ELEMENT_COMMAND_INPUT } from './createCommandsPlugin';
import { CommandNodeData, CommandsPlugin } from './types';


export const getCommandOnSelectItem = ({
  key = ELEMENT_COMMAND,
}: PlatePluginKey = {}) => (editor, item, context) => {
  const targetRange = comboboxSelectors.targetRange();
  if (!targetRange) return;

  const {
    type,
    options: { },
  } = getPlugin<CommandsPlugin>(editor, key);

  const pathAbove = getBlockAbove(editor)?.[1];
  const isBlockEnd =
    editor.selection &&
    pathAbove &&
    Editor.isEnd(editor, editor.selection.anchor, pathAbove);

  Editor.withoutNormalizing(editor, () => {
    // insert a space to fix the bug
    if (isBlockEnd) {
      Transforms.insertText(editor, ' ');
    }

    // select the text and insert the element
    Transforms.select(editor, targetRange);

    // HistoryEditor.withoutMerging(editor, () =>
    Transforms.removeNodes(editor, {
      // TODO: replace any
      match: (node: any) => node.type === ELEMENT_COMMAND_INPUT,
    })
    // );

    editor && item.data.handler(editor, item.data.args, context)

    // Transforms.insertText(editor, item.text);

    // move the selection after the element
    // Transforms.move(editor);

    // delete the inserted space
    if (isBlockEnd) {
      Transforms.delete(editor);
    }
  });
  return comboboxActions.reset();
};
