import { comboboxActions } from '@udecode/plate-combobox';
import { getPlugin, insertNodes, WithOverride } from '@udecode/plate-common';
import { Editor, Node, Range, Transforms } from 'slate';
import { HistoryEditor } from 'slate-history';
import { removeCommandInput } from './transforms/removeCommandInput';
import { ELEMENT_COMMAND_INPUT } from './createCommandsPlugin';
import {
  findCommandInput,
  isNodeCommandInput,
  isSelectionInCommandInput,
} from './queries';

export const withCommands = (
  editor,
  { options: { id, trigger } }
) => {
  const { type } = getPlugin(editor, ELEMENT_COMMAND_INPUT);

  const { apply, insertText, deleteBackward } = editor;

  editor.deleteBackward = (unit) => {
    const currentCommandInput = findCommandInput(editor);
    if (currentCommandInput && Node.string(currentCommandInput[0]) === '') {
      return removeCommandInput(editor, currentCommandInput[1]);
    }

    deleteBackward(unit);
  };

  editor.insertText = (text) => {
    if (isSelectionInCommandInput(editor)) {
      return Transforms.insertText(editor, text);
    }

    if (!editor.selection || text !== trigger) {
      return insertText(text);
    }

    // Make sure a mention input is created at the beginning of line or after a whitespace

    insertNodes(editor, {
      type,
      children: [{ text: '' }],
      trigger,
    });
  };

  editor.apply = (operation) => {
    if (HistoryEditor.isHistoryEditor(editor) && findCommandInput(editor)) {
      HistoryEditor.withoutSaving(editor, () => apply(operation));
    } else {
      apply(operation);
    }

    if (operation.type === 'insert_text' || operation.type === 'remove_text') {
      const currentCommandInput = findCommandInput(editor);
      if (currentCommandInput) {
        comboboxActions.text(Node.string(currentCommandInput[0]));
      }
    } else if (operation.type === 'set_selection') {
      const previousCommandInputPath = Range.isRange(operation.properties)
        ? findCommandInput(editor, { at: operation.properties })?.[1]
        : undefined;

      const currentCommandInputPath = Range.isRange(operation.newProperties)
        ? findCommandInput(editor, { at: operation.newProperties })?.[1]
        : undefined;

      if (previousCommandInputPath && !currentCommandInputPath) {
        removeCommandInput(editor, previousCommandInputPath);
      }

      if (currentCommandInputPath) {
        comboboxActions.targetRange(editor.selection);
      }
    } else if (
      operation.type === 'insert_node' &&
      isNodeCommandInput(editor, operation.node)
    ) {
      if (operation.node.trigger !== trigger) {
        return;
      }

      comboboxActions.open({
        activeId: id!,
        text: '',
        targetRange: editor.selection,
      });
    } else if (
      operation.type === 'remove_node' &&
      isNodeCommandInput(editor, operation.node)
    ) {
      if (operation.node.trigger !== trigger) {
        return;
      }

      comboboxActions.reset();
    }
  };

  return editor;
};
