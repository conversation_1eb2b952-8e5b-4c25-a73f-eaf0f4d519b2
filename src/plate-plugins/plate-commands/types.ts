import { Data, NoData } from '@udecode/plate-combobox';
import { TElement } from '@udecode/plate-common';

export interface CommandNodeData {
  command: string;
}

export interface CommandInputNodeData {
  trigger: string;
}

export type CommandNode = TElement;
export type CommandInputNode = TElement;

export interface CommandsPlugin<TData extends Data = NoData> {
  id?: string;
  trigger?: string;
}
