import { PlateEditor, TDescendant } from '@udecode/plate-common';
import { Editor, Node, Path, Transforms } from 'slate';

export const removeCommandInput = (editor, path) =>
  Editor.withoutNormalizing(editor, () => {
    const { trigger } = Node.get(editor, path);

    Transforms.insertText(editor, trigger, {
      at: { path: [...path, 0], offset: 0 },
    });
    Transforms.unwrapNodes(editor, {
      at: path,
    });
  });
