import { PlateElementProps, Value } from '@udecode/plate-common';
import { TTagElement } from '../../core';

export interface TagInputElementStyleProps<V extends Value>
  extends TagInputElementProps<V> {
  selected?: boolean;
  focused?: boolean;
}

// renderElement props
export interface TagInputElementProps<V extends Value>
  extends PlateElementProps<V, TTagElement> {
  /**
   * Prefix rendered before Tag
   */
  prefix?: string;
  onClick?: (TagNode: any) => void;
  renderLabel?: (Tagable: TTagElement) => string;
}
