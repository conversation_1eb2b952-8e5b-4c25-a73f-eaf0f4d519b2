import React from 'react';
import { getHandler, getRootProps, Value } from '@udecode/plate-common';
import { useFocused, useSelected } from 'slate-react';
import { TagInputElementProps } from './TagInputElement.types';

export const TagInputElement = <V extends Value>(
  props: TagInputElementProps<V>
) => {
  const { attributes, children, nodeProps, element, as, onClick } = props;

  const rootProps = getRootProps(props);

  const selected = useSelected();
  const focused = useFocused();

  // const styles = getTagInputElementStyles({
  //   ...props,
  //   selected,
  //   focused,
  // });

  return (
    <span
      {...attributes}
      as={as}
      data-slate-value={element.value}
      // className={styles.root.className}
      style={{
        padding: '3px 3px 2px',
        borderRadius: '4px',
        backgroundColor: 'aliceblue',
        fontSize: '0.9em'
      }}
      onClick={getHandler(onClick, element)}
      {...rootProps}
      {...nodeProps}
    >
      {children}
    </span>
  );
};
