import React from 'react';
import { getHand<PERSON>, getRootProps, Value } from '@udecode/plate-common';
import { useFocused, useSelected } from 'slate-react';
import { TagElementProps } from './TagElement.types';

export const TagElement = <V extends Value>(
  props: TagElementProps<V>
) => {
  const {
    attributes,
    children,
    nodeProps,
    element,
    prefix,
    onClick,
    renderLabel,
  } = props;

  const rootProps = getRootProps(props);

  const selected = useSelected();
  const focused = useFocused();

  // const styles = getTagElementStyles({ ...props, selected, focused });

  return (
    <span
      {...attributes}
      data-slate-value={element.value}
      // className={styles.root.className}
      // css={styles.root.css}
      contentEditable={false}
      onClick={getHandler(onClick, element)}
      {...rootProps}
      {...nodeProps}
    >
      {prefix}
      {renderLabel ? renderLabel(element) : element.value}
      {children}
    </span>
  );
};
