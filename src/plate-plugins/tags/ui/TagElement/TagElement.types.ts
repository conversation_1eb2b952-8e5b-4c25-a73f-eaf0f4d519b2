import { PlateElementProps, Value } from '@udecode/plate-common';
import { TTagElement } from '../../core';

export interface TagElementStyleProps<V extends Value>
  extends TagElementProps<V> {
  selected?: boolean;
  focused?: boolean;
}

// renderElement props
export interface TagElementProps<V extends Value>
  extends PlateElementProps<V, TTagElement> {
  /**
   * Prefix rendered before Tag
   */
  prefix?: string;
  onClick?: (TagNode: any) => void;
  renderLabel?: (Tagable: TTagElement) => string;
}
