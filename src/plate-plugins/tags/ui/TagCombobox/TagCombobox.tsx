import React from 'react';
import { ComboboxProps, Data, NoData } from '@udecode/plate-combobox';
import { getPluginOptions, useEditorRef } from '@udecode/plate-common';
import {
  ELEMENT_TAG,
  getTagOnSelectItem,
  TagPlugin,
} from '../../core';
import { Combobox } from '@/components/plate-ui/combobox';

export interface TagComboboxProps<TData extends Data = NoData>
  extends Partial<ComboboxProps<TData>> {
  pluginKey?: string;
  onSelectItem?: (item, search) => void;
}

export const TagCombobox = <TData extends Data = NoData>({
  pluginKey = ELEMENT_TAG,
  id = pluginKey,
  onSelectItem,
  ...props
}: TagComboboxProps<TData>) => {
  const editor = useEditorRef()!;

  const { trigger } = getPluginOptions<TagPlugin>(editor, pluginKey);

  return (
    <Combobox
      id={id}
      trigger={trigger!}
      controlled
      onSelectItem={(editor, item) => {
        const search = getTagOnSelectItem({ key: pluginKey })(editor, item);
        onSelectItem && onSelectItem(item, search);
      }}

      {...props}
    />
  );
};
