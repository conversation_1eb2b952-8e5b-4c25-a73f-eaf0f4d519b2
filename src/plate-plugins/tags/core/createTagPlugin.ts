import { createPluginFactory } from '@udecode/plate-common';
import { tagOnKeyDownHandler } from './handlers/tagOnKeyDownHandler';
import { isSelectionInTagInput } from './queries';
import { TagPlugin } from './types';
import { withTag } from './withTag';

export const ELEMENT_TAG = 'tag';
export const ELEMENT_TAG_INPUT = 'tag_input';

/**
 * Enables support for autocompleting #tags.
 */
export const createTagPlugin = createPluginFactory<TagPlugin>({
  key: ELEMENT_TAG,
  isElement: true,
  isInline: true,
  isVoid: true,
  handlers: {
    onKeyDown: tagOnKeyDownHandler({ query: isSelectionInTagInput }),
  },
  withOverrides: withTag,
  options: {
    trigger: '#',
    createTagNode: (item, meta) => { 
      return { value: item.key=='new_tag' ? meta.search : item.text } 
    },
  },
  plugins: [
    {
      key: ELEMENT_TAG_INPUT,
      isElement: true,
      isInline: true,
    },
  ],
  then: (editor, { key }) => ({
    options: {
      id: key,
    },
  }),
});
