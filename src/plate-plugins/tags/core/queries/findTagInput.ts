import {
  findNode,
  FindNodeOptions,
  getPluginType,
  PlateEditor,
  Value,
} from '@udecode/plate-common';
import { ELEMENT_TAG_INPUT } from '../createTagPlugin';
import { TTagInputElement } from '../types';

export const findTagInput = <V extends Value>(
  editor: PlateEditor<V>,
  options?: Omit<FindNodeOptions<V>, 'match'>
) =>
  findNode<TTagInputElement>(editor, {
    ...options,
    match: { type: getPluginType(editor, ELEMENT_TAG_INPUT) },
  });
