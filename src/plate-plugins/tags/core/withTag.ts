import { comboboxActions } from '@udecode/plate-combobox';
import {
  getEditorString,
  getNodeString,
  getPlugin,
  getPointAfter,
  getPointBefore,
  getRange,
  insertNodes,
  insertText,
  PlateEditor,
  setSelection,
  TNode,
  TText,
  Value,
  WithPlatePlugin,
} from '@udecode/plate-common';
import { Range } from 'slate';
import { removeTagInput } from './transforms/removeTagInput';
import { ELEMENT_TAG_INPUT } from './createTagPlugin';
import {
  findTagInput,
  isNodeTagInput,
  isSelectionInTagInput,
} from './queries';
import { TagPlugin, TTagInputElement } from './types';

export const withTag = <
  V extends Value = Value,
  E extends PlateEditor<V> = PlateEditor<V>
>(
  editor: E,
  {
    options: { id, trigger, inputCreation },
  }: WithPlatePlugin<TagPlugin, V, E>
) => {
  const { type } = getPlugin<{}, V>(editor, ELEMENT_TAG_INPUT);

  const {
    apply,
    insertBreak,
    insertText: _insertText,
    deleteBackward,
    insertFragment: _insertFragment,
    insertTextData,
  } = editor;

  const stripNewLineAndTrim: (text: string) => string = (text) => {
    return text
      .split(/\r\n|\r|\n/)
      .map((line) => line.trim())
      .join('');
  };

  editor.insertFragment = (fragment) => {
    const inTagInput = findTagInput(editor) !== undefined;
    if (!inTagInput) {
      return _insertFragment(fragment);
    }

    return insertText(
      editor,
      fragment.map((node) => stripNewLineAndTrim(getNodeString(node))).join('')
    );
  };

  editor.insertTextData = (data) => {
    const inTagInput = findTagInput(editor) !== undefined;
    if (!inTagInput) {
      return insertTextData(data);
    }

    const text = data.getData('text/plain');
    if (!text) {
      return false;
    }

    editor.insertText(stripNewLineAndTrim(text));

    return true;
  };

  editor.deleteBackward = (unit) => {
    const currentTagInput = findTagInput(editor);
    if (currentTagInput && getNodeString(currentTagInput[0]) === '') {
      return removeTagInput(editor, currentTagInput[1]);
    }

    deleteBackward(unit);
  };

  editor.insertBreak = () => {
    if (isSelectionInTagInput(editor)) {
      return;
    }

    insertBreak();
  };

  editor.insertText = (text) => {
    if (
      !editor.selection ||
      text !== trigger ||
      isSelectionInTagInput(editor)
    ) {
      return _insertText(text);
    }

    // Make sure a Tag input is created at the beginning of line or after a whitespace
    const previousChar = getEditorString(
      editor,
      getRange(
        editor,
        editor.selection,
        getPointBefore(editor, editor.selection)
      )
    );

    const nextChar = getEditorString(
      editor,
      getRange(
        editor,
        editor.selection,
        getPointAfter(editor, editor.selection)
      )
    );

    const beginningOfLine = previousChar === '';
    const endOfLine = nextChar === '';
    const precededByWhitespace = previousChar === ' ';
    const followedByWhitespace = nextChar === ' ';

    if (
      (beginningOfLine || precededByWhitespace) &&
      (endOfLine || followedByWhitespace)
    ) {
      const data: TTagInputElement = {
        type,
        children: [{ text: '' }],
        trigger,
      };
      if (inputCreation) {
        data[inputCreation.key] = inputCreation.value;
      }
      return insertNodes<TTagInputElement>(editor, data);
    }

    return _insertText(text);
  };

  editor.apply = (operation) => {
    apply(operation);

    if (operation.type === 'insert_text' || operation.type === 'remove_text') {
      const currentTagInput = findTagInput(editor);
      if (currentTagInput) {
        comboboxActions.text(getNodeString(currentTagInput[0]));
      }
    } else if (operation.type === 'set_selection') {
      const previousTagInputPath = Range.isRange(operation.properties)
        ? findTagInput(editor, { at: operation.properties })?.[1]
        : undefined;

      const currentTagInputPath = Range.isRange(operation.newProperties)
        ? findTagInput(editor, { at: operation.newProperties })?.[1]
        : undefined;

      if (previousTagInputPath && !currentTagInputPath) {
        removeTagInput(editor, previousTagInputPath);
      }

      if (currentTagInputPath) {
        comboboxActions.targetRange(editor.selection);
      }
    } else if (
      operation.type === 'insert_node' &&
      isNodeTagInput(editor, operation.node as TNode)
    ) {
      if ((operation.node as TTagInputElement).trigger !== trigger) {
        return;
      }

      const text =
        ((operation.node as TTagInputElement).children as TText[])[0]
          ?.text ?? '';

      if (
        inputCreation === undefined ||
        operation.node[inputCreation.key] === inputCreation.value
      ) {
        // Needed for undo - after an undo a Tag insert we only receive
        // an insert_node with the Tag input, i.e. nothing indicating that it
        // was an undo.
        setSelection(editor, {
          anchor: { path: operation.path.concat([0]), offset: text.length },
          focus: { path: operation.path.concat([0]), offset: text.length },
        });

        comboboxActions.open({
          activeId: id!,
          text,
          targetRange: editor.selection,
        });
      }
    } else if (
      operation.type === 'remove_node' &&
      isNodeTagInput(editor, operation.node as TNode)
    ) {
      if ((operation.node as TTagInputElement).trigger !== trigger) {
        return;
      }

      comboboxActions.reset();
    }
  };

  return editor;
};
