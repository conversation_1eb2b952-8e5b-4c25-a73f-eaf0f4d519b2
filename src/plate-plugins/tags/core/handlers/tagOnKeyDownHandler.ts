import { PlateEditor, Value } from '@udecode/plate-common';
import isHotkey from 'is-hotkey';
import { findTagInput } from '../queries';
import { removeTagInput } from '../transforms';
import { KeyboardEventHandler } from './KeyboardEventHandler';
import {
  moveSelectionByOffset,
  MoveSelectionByOffsetOptions,
} from './moveSelectionByOffset';

export const tagOnKeyDownHandler: <V extends Value>(
  options?: MoveSelectionByOffsetOptions<V>
) => (editor: PlateEditor<V>) => KeyboardEventHandler = (options) => (
  editor
) => (event) => {
  if (isHotkey('escape', event)) {
    event.preventDefault();
    const currentTagInput = findTagInput(editor)!;
    if (currentTagInput) {
      removeTagInput(editor, currentTagInput[1]);
    }
    return true;
  }

  return moveSelectionByOffset(editor, options)(event);
};
