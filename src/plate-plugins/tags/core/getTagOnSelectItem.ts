import {
  comboboxActions,
  ComboboxOnSelectItem,
  comboboxSelectors,
  Data,
  NoData,
  TComboboxItem,
} from '@udecode/plate-combobox';
import {
  deleteText,
  getBlockAbove,
  getPlugin,
  insertNodes,
  insertText,
  isEndPoint,
  moveSelection,
  PlatePluginKey,
  removeNodes,
  select,
  TNodeProps,
  withoutMergingHistory,
  withoutNormalizing,
} from '@udecode/plate-common';
import { ELEMENT_TAG, ELEMENT_TAG_INPUT } from './createTagPlugin';
import { TagPlugin, TTagElement } from './types';

export interface CreateTagNode<TData extends Data> {
  (
    item: TComboboxItem<TData>,
    meta: CreateTagNodeMeta
  ): TNodeProps<TTagElement>;
}

export interface CreateTagNodeMeta {
  search: string;
}

export const getTagOnSelectItem = <TData extends Data = NoData>({
  key = ELEMENT_TAG,
}: PlatePluginKey = {}): ComboboxOnSelectItem<TData> => (editor, item) => {
  const targetRange = comboboxSelectors.targetRange();
  if (!targetRange) return;

  const {
    type,
    options: { insertSpaceAfterTag, createTagNode },
  } = getPlugin<TagPlugin>(editor as any, key);

  const pathAbove = getBlockAbove(editor)?.[1];
  const isBlockEnd =
    editor.selection &&
    pathAbove &&
    isEndPoint(editor, editor.selection.anchor, pathAbove);

  const search = comboboxSelectors.text() ?? '';

  withoutNormalizing(editor, () => {
    // Selectors are sensitive to operations, it's better to create everything
    // before the editor state is changed. For example, asking for text after
    // removeNodes below will return null.
    const props = createTagNode!(item, {
      search: comboboxSelectors.text() ?? '',
    });

    // insert a space to fix the bug
    if (isBlockEnd) {
      insertText(editor, ' ');
    }

    select(editor, targetRange);

    withoutMergingHistory(editor, () =>
      removeNodes(editor, {
        match: (node) => node.type === ELEMENT_TAG_INPUT,
      })
    );

    insertNodes<TTagElement>(editor, {
      type,
      children: [{ text: props.value }],
      ...props,
    } as TTagElement);

    // move the selection after the element
    moveSelection(editor);

    // delete the inserted space
    if (isBlockEnd && !insertSpaceAfterTag) {
      deleteText(editor);
    }
  });

  comboboxActions.reset();

  return search;
};
