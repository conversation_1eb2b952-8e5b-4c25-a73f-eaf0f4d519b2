import { Data, NoData } from '@udecode/plate-combobox';
import { TElement } from '@udecode/plate-common';
import { CreateTagNode } from './getTagOnSelectItem';

export interface TTagElement extends TElement {
  value: string;
}

export interface TTagInputElement extends TElement {
  trigger: string;
}

export interface TagPlugin<TData extends Data = NoData> {
  createTagNode?: CreateTagNode<TData>;
  id?: string;
  insertSpaceAfterTag?: boolean;
  trigger?: string;
  inputCreation?: { key: string; value: string };
}
