import React, { useCallback, useMemo, useState } from 'react';
import {
  getPlugin,
  getRootProps,
  PlateRenderElementProps,
  setNodes,
} from '@udecode/plate-common';
import { ReactEditor, useReadOnly } from 'slate-react';
import TextareaAutosize from 'react-textarea-autosize';
import { ELEMENT_RIL_HIGHLIGHT } from './createRILHighlightPlugin';

export const RILHighlightElement = (props) => {
  const {
    attributes,
    children,
    element,
    editor,
    nodeProps
  } = props;

  const {
    highlight,
    note = [{ children: [{ text: '' }] }],
  } = element;

  const placeholder = 'add notes to above quote...'

  const onChangeNote = useCallback(
    (e) => {
      const path = ReactEditor.findPath(editor as ReactEditor, element);
      setNodes(editor, { note: [{ text: e.target.value }] }, { at: path });
    },
    [editor, element]
  );

  const rootProps = getRootProps(props);

  const readOnly = useReadOnly();

  const {
    type,
    options,
  } = getPlugin(editor, ELEMENT_RIL_HIGHLIGHT);

  return (
    // Need contentEditable=false or Firefox has issues with certain input types.
    <div
      contentEditable={false}
      {...attributes}
      {...rootProps}
      {...nodeProps}
    >
      <blockquote
        style={{
          borderLeft: "2px solid #ddd",
          padding: "10px 20px 10px 16px",
          marginInlineStart: '0px',
          color: "#aaa"
        }}
      >
        {highlight}
      </blockquote>
      {
        options.readOnly &&
        note[0].text &&
        <div> {note[0].text} </div>
      }
      {
        !options.readOnly &&
        <TextareaAutosize
          style={{
            border: '0px',
            outline: 'none',
            width: '95%',
            overflowY: 'hidden',
            fontSize: 16
          }}
          defaultValue={note[0].text}
          placeholder={placeholder}
          onChange={onChangeNote}
        />
      }
      {/* {children} */}
    </div >
  );
};
