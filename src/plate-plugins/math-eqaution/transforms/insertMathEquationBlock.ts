import {
  getParentNode,
  insertN<PERSON>,
  PlateEditor,
  PlatePluginKey,
  Value,
  select
} from '@udecode/plate-common';
import { ELEMENT_MATH_EQUATION_BLOCK } from '../createMathEquationBlockPlugin';
import { TMathEquationElement } from '../types';

export const insertMathEquationBlock = <V extends Value>(
  editor: PlateEditor<V>,
  {
    expression = '',
    key = ELEMENT_MATH_EQUATION_BLOCK,
  }: Partial<TMathEquationElement> & PlatePluginKey
): void => {
  if (!editor.selection) return;
  const selectionParentEntry = getParentNode(editor, editor.selection);
  
  if (!selectionParentEntry) return;
  const [, path] = selectionParentEntry;

  insertNodes<TMathEquationElement>(
    editor,
    {
      type: key,
      expression,
      children: [{ text: '' }],
    },
    { at: path }
  );

  select(editor, path);
};
