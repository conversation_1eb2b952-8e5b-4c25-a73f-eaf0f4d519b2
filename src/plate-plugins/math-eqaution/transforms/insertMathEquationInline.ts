import {
  getParentN<PERSON>,
  insertN<PERSON>,
  PlateEditor,
  PlatePluginKey,
  Value,
  select
} from '@udecode/plate-common';
import { ELEMENT_MATH_EQUATION_INLINE } from '../createMathEquationInlinePlugin';
import { TMathEquationElement } from '../types';

export const insertMathEquationInline = <V extends Value>(
  editor: PlateEditor<V>,
  {
    expression = '',
    key = ELEMENT_MATH_EQUATION_INLINE,
  }: Partial<TMathEquationElement> & PlatePluginKey
): void => {
  if (!editor.selection) return;
  const selectionParentEntry = getParentNode(editor, editor.selection);

  if (!selectionParentEntry) return;

  insertNodes<TMathEquationElement>(
    editor,
    {
      type: key,
      expression,
      children: [{ text: '' }],
    },
  );
};
