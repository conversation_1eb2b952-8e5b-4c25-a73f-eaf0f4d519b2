import { createPluginFactory } from '@udecode/plate-common';
import { withMathEquationInline } from './withMathEquationInline';

export const ELEMENT_MATH_EQUATION_INLINE = 'math_inline';

/**
 * Enables support for embeddable media such as YouTube
 * or Vimeo videos, Instagram posts and tweets or Google Maps.
 */
export const createMathEquationInlinePlugin = createPluginFactory({
  key: ELEMENT_MATH_EQUATION_INLINE,
  isElement: true,
  isVoid: true,
  isInline: true,
  // withOverrides: withMathEquationInline,
  handlers: {
    onDrop: (editor) => (e) => {
      return true;
    }
  },
});
