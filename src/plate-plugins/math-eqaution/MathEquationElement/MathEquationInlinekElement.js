import React, { useEffect, useRef, useState } from 'react';
import { findNodePath, setNodes, getNextNodeStartPoint, select, getRootProps } from '@udecode/plate-common';

import { useIntl } from 'react-intl';
import 'katex/dist/katex.min.css';
import { BlockMath, InlineMath } from 'react-katex';
import { InputModal } from 'src/components/common/InputModal';

export const MathEquationInlineElement = (
  props
) => {
  const { attributes, children, nodeProps, element, editor } = props;

  const rootProps = getRootProps(props);
  const mathContainerRef = useRef();
  const [anchorEl, setAnchorEl] = useState(null);

  const math = element.expression || '\\TeX \\space \\text{Add mathematic symbols}';

  useEffect(() => {
    const path = findNodePath(editor, element);

    if (!element.expression && !!mathContainerRef?.current && path[0] === editor.selection?.anchor?.path[0]) {
      mathContainerRef.current.click()
    }
  }, [mathContainerRef?.current])

  return (
    <span
      style={{
        cursor: 'pointer'
      }}
    >
      <span
        ref={mathContainerRef}

        onClick={(e) => setAnchorEl(e.currentTarget)}
      >
        <InlineMath math={math} />
        {children}

      </span>
      <InputModal
        initValue={element.expression}
        anchorEl={anchorEl}
        onChange={(event) => {
          const path = findNodePath(editor, element);
          if (!path) return;

          setNodes(editor, { expression: event.target.value }, { at: path });
        }}
        onClose={() => {
          setAnchorEl(null);

          const path = findNodePath(editor, element);
          const newPoint = getNextNodeStartPoint(editor, path);
          select(editor, newPoint);
        }}
        style={{ width: '300px' }}
      />
    </span>
  );
};
