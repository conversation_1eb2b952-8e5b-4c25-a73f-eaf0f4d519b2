import React, { useEffect, useRef, useState } from 'react';
import { findNodePath, setNodes, Value, getRootProps } from '@udecode/plate-common';

import { useIntl } from 'react-intl';
import 'katex/dist/katex.min.css';
import { BlockMath, InlineMath } from 'react-katex';
import { InputModal } from 'src/components/common/InputModal';

export const MathEquationBlockElement = (
  props
) => {
  const { attributes, children, nodeProps, element, editor } = props;

  const rootProps = getRootProps(props);
  const mathContainerRef = useRef();
  const [anchorEl, setAnchorEl] = useState(null);

  const math = element.expression || '\\TeX \\space \\text{Add a TeX equation}';

  useEffect(() => {
    const path = findNodePath(editor, element);

    if (!element.expression && !!mathContainerRef?.current && path[0] === editor.selection?.anchor?.path[0]) {
      mathContainerRef.current.click()
    }
  }, [mathContainerRef?.current])

  return (
    <div
      {...attributes}
      style={{
        width: '-webkit-fill-available',
        paddingLeft: 2,
        // maxWidth: 800,
        borderRadius: '4px',
        background: !element.expression ? '#f9f9f9' : 'white'
      }}
      {...rootProps}
    >
      <div contentEditable={false}>
        <div
          className='hoverStand'
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: !!element.expression ?'center': 'flex-start',
            border: 'none',
            borderRadius: '2px',
            paddingLeft: '8px',
            paddingTop: '0px',
            paddingBottom: '0px',
            left: 'calc(50% - 400px)',
          }}

          ref={mathContainerRef}
          onClick={(e) => setAnchorEl(e.currentTarget)}
        >
          <BlockMath math={math} errorColor={'#cc0000'} />
        </div>
      </div>
      {children}
      <InputModal
        multiline={true}
        initValue={element.expression}
        anchorEl={anchorEl}
        style={{
          width: 720
        }}
        onChange={(event) => {
          const path = findNodePath(editor, element);
          if (!path) return;

          setNodes(editor, { expression: event.target.value }, { at: path });
        }}
        onClose={() => setAnchorEl(null)}
      />
    </div>
  );
};
