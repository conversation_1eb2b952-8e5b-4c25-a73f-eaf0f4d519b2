import {
  collapseSelection,
  getAboveNode,
  getEditorString,
  getNextNodeStartPoint,
  getPluginType,
  getPreviousNodeEndPoint,
  getRangeBefore,
  getRangeFromBlockStart,
  insertNodes,
  isCollapsed,
  isEndPoint,
  isStartPoint,
  mockPlugin,
  PlateEditor,
  select,
  someNode,
  Value,
  withoutNormalizing,
  WithPlatePlugin,
} from '@udecode/plate-common';
import { withRemoveEmptyNodes } from '@udecode/plate-normalizers';
import { Path, Point, Range } from 'slate';
import { ELEMENT_MATH_EQUATION_INLINE } from './createMathEquationInlinePlugin';

/**
 * Insert space after a url to wrap a link.
 * Lookup from the block start to the cursor to check if there is an url.
 * If not found, lookup before the cursor for a space character to check the url.
 *
 * On insert data:
 * Paste a string inside a link element will edit its children text but not its url.
 *
 */

export const withMathEquationInline = <
  V extends Value = Value,
  E extends PlateEditor<V> = PlateEditor<V>
>(
  editor: E,
  {
    type,
    options: {},
  }: WithPlatePlugin<{}, V, E>
) => {
  const { apply, normalizeNode, insertBreak } = editor;


  // TODO: plugin
  editor.apply = (operation) => {
    if (operation.type === 'set_selection') {
      const range = operation.newProperties as Range | null;

      if (range && range.focus && range.anchor && isCollapsed(range)) {
        const entry = getAboveNode(editor, {
          at: range,
          match: { type: getPluginType(editor, ELEMENT_MATH_EQUATION_INLINE) },
        });

        if (entry) {
          const [, path] = entry;

          let newPoint: Point | undefined;

          if (isStartPoint(editor, range.focus, path)) {
            newPoint = getPreviousNodeEndPoint(editor, path);
          }

          if (isEndPoint(editor, range.focus, path)) {
            newPoint = getNextNodeStartPoint(editor, path);
          }

          if (newPoint) {
            operation.newProperties = {
              anchor: newPoint,
              focus: newPoint,
            };
          }
        }
      }
    }

    apply(operation);
  };

  // TODO: plugin
  editor.normalizeNode = ([node, path]) => {
    if (node.type === getPluginType(editor, ELEMENT_MATH_EQUATION_INLINE)) {
      const range = editor.selection as Range | null;

      if (range && isCollapsed(range)) {
        if (isEndPoint(editor, range.focus, path)) {
          const nextPoint = getNextNodeStartPoint(editor, path);

          // select next text node if any
          if (nextPoint) {
            select(editor, nextPoint);
          } else {
            // insert text node then select
            const nextPath = Path.next(path);
            insertNodes(editor, { text: '' } as any, { at: nextPath });
            select(editor, nextPath);
          }
        }
      }
    }

    normalizeNode([node, path]);
  };

  editor = withRemoveEmptyNodes<V, E>(
    editor,
    mockPlugin<{}, V, E>({
      options: { types: type },
    })
  );

  return editor;
};
