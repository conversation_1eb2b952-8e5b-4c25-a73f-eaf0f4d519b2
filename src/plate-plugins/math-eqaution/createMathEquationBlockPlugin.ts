import { createPluginFactory } from '@udecode/plate-common';

export const ELEMENT_MATH_EQUATION_BLOCK = 'math_block';

/**
 * Enables support for embeddable media such as YouTube
 * or Vimeo videos, Instagram posts and tweets or Google Maps.
 */
export const createMathEquationBlockPlugin = createPluginFactory({
  key: ELEMENT_MATH_EQUATION_BLOCK,
  isElement: true,
  isVoid: true,
  handlers: {
    onDrop: (editor) => (e) => {
      return true;
    }
  },
});
