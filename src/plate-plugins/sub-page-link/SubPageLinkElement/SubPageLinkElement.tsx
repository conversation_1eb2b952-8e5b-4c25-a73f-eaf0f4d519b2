import { Link, useHistory } from 'react-router-dom';
import { FileSlides } from '@styled-icons/bootstrap/FileSlides'
import { FileText } from '@styled-icons/bootstrap/FileText'
import { FileSpreadsheet } from '@styled-icons/bootstrap/FileSpreadsheet'
import { findNodePath, getRootProps, setNodes } from '@udecode/plate-common';
import { useEffect, useRef, useState } from 'react';
import { Popover } from '@mui/material';
import { PageChooser } from 'src/components/PageChooser';
import { FileMedical } from '@styled-icons/bootstrap/FileMedical';
import { useIntl } from 'react-intl';

export const SubPageLinkElement = (props) => {
  const {
    attributes,
    children,
    element,
    editor,
    nodeProps
  } = props;

  const history = useHistory();
  const intl = useIntl();
  const eleRef = useRef(null);
  const searchInputRef = useRef(null);
  const [anchorEl, setAnchorEl] = useState(null);

  let eleContent = <div
    ref={eleRef}
  >
    {intl.formatMessage({ id: 'choose_page_to_link' })}
  </div>;

  if (element.hid) {
    let icon = <FileText size={18} />;
    if (element.docType === 'slides') {
      icon = <FileSlides size={18} />;
    } else if (element.docType === 'flow') {
      icon = <FileMedical size={18} />;
    } else if (element.docType === 'db') {
      icon = <FileSpreadsheet size={18} />;
    }

    eleContent = <div style={{
      display: 'flex',
      cursor: 'pointer',
      flexDirection: 'row',
      justifyContent: 'flex-start',
      alignItems: 'center',
      columnGap: '4px',
      color: "#555",
      textDecoration: 'underline',
      textDecorationColor: '#999',
      fontSize: '14px',
      fontWeight: 500,
    }}>
      {icon}
      {
        element.title
      }
    </div>;
  }

  const rootProps = getRootProps(props);

  useEffect(() => {
    if (eleRef && eleRef.current) {
      setAnchorEl(eleRef.current);
    }
  }, [eleRef]);

  return (
    <div
      {...attributes}
      {...rootProps}
      className='hoverStand'
      style={{
        width: '-webkit-fill-available',
        paddingLeft: 2,
        borderRadius: '4px',
      }}
      onClick={(e) => {
        if (element.hid) {
          let editorRoute = '/editor';
          if (element.docType === 'slides') {
            editorRoute = '/slidesViewer';
          } else if (element.docType === 'flow') {
            editorRoute = '/flow';
          } else if (element.docType === 'db') {
            editorRoute = '/db';
          }

          let url = `${editorRoute}?hid=${element.hid}`;

          history.push(url);
        } else {
          if (!anchorEl) {
            setAnchorEl(eleRef.current);
          }
        }
      }}
    >
      {
        eleContent
      }
      {
        children
      }
      <Popover
        open={Boolean(anchorEl)}
        onClose={(e) => {
          setAnchorEl(null);
        }}

        anchorEl={anchorEl}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'left',
        }}

        onKeyDown={(e) => {
          searchInputRef.current.focus();
        }}
      >
        <div
          style={{
            height: 500,
            width: 600
          }}>
          <PageChooser
            accepts={['slides', 'doc', 'db', 'flow']}
            searchInputRef={searchInputRef}
            onSelect={(page) => {
              const path = findNodePath(editor, element);
              if (!path) return;

              setNodes(editor, { hid: page.hid, docType: page.type, title: page.title }, { at: path });

              setAnchorEl(null);
            }}
            onClose={(e) => {
              setAnchorEl(null);
            }}
          />
        </div>
      </Popover>
    </div>
  );
};
