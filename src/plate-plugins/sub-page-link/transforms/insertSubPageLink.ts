import {
  getParentNode,
  insertN<PERSON>,
  PlateEditor,
  PlatePluginKey,
  Value,
} from '@udecode/plate-common';
import { ELEMENT_SUB_PAGE } from '../createSubPageLinkPlugin';
import { TSubPageLinkElement } from '../types';

export const insertSubPageLink = <V extends Value>(
  editor: PlateEditor<V>,
  {
    docType = '',
    hid = '',
    title = '',
    key = ELEMENT_SUB_PAGE,
  }: Partial<TSubPageLinkElement> & PlatePluginKey
): void => {
  if (!editor.selection) return;
  const selectionParentEntry = getParentNode(editor, editor.selection);
  if (!selectionParentEntry) return;
  const [, path] = selectionParentEntry;
  insertNodes<TSubPageLinkElement>(
    editor,
    {
      type: key,
      hid,
      title,
      docType,
      children: [{ text: title }],
    },
    { at: path }
  );
};
