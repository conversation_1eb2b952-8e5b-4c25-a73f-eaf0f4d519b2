import React from 'react';
import { getHand<PERSON>, getRootProps } from '@udecode/plate-common';
import { useFocused, useSelected } from 'slate-react';
// import { getCommandInputElementStyles } from './CommandInputElement.styles';

export const CommandInputElement = (props) => {
  const { attributes, children, nodeProps, element, as, onClick } = props;

  const rootProps = getRootProps(props);

  const selected = useSelected();
  const focused = useFocused();

  // const styles = getCommandInputElementStyles({
  //   ...props,
  //   selected,
  //   focused,
  // });

  return (
    <span
      {...attributes}
      as={as}
      data-slate-value={element.value}
      // className={styles.root.className}
      // css={styles.root.css}
      onClick={getHandler(onClick, element)}
      {...rootProps}
      {...nodeProps}
    >
      {children}
    </span>
  );
};
