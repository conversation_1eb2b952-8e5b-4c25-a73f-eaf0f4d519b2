import React from 'react';
import { ComboboxProps, Data, NoData } from '@udecode/plate-combobox';
import { getPluginOptions, useEditorRef } from '@udecode/plate-common';
import {
  ELEMENT_COMMAND,
  getCommandOnSelectItem,
  CommandsPlugin,
} from '../../plate-commands';
import { useDispatch } from 'react-redux';
import { useHistory } from 'react-router-dom';
import { Combobox } from '@/components/plate-ui/combobox';

export interface CommandComboboxProps<TData extends Data = NoData>
  extends Partial<ComboboxProps<TData>> {
  pluginKey?: string;
}

export const CommandsCombobox = <TData extends Data = NoData>({
  pluginKey = ELEMENT_COMMAND,
  id = pluginKey,
  ...props
}: CommandComboboxProps<TData>) => {
  const editor = useEditorRef()!;
  const dispatch = useDispatch();
  const history = useHistory();

  const { trigger } = getPluginOptions<CommandsPlugin>(editor, pluginKey);

  return (
    <Combobox
      id={id}
      trigger={trigger}
      controlled
      onSelectItem={(editor, item) => getCommandOnSelectItem({ key: pluginKey })(editor, item, { dispatch, history })}
      filter={(search) => (item) => {
        // return item.key == 'cmd_trigger' || item.text.toLowerCase().includes(search.toLowerCase());
        return item.text.toLowerCase().includes(search.toLowerCase());
      }}
      {...props}
    />
  );
};
