import { createPluginFactory } from '@udecode/plate-common';

export const ELEMENT_DB_EMBED = 'db-embed';

/**
 * Enables support for embeddable media such as YouTube
 * or Vimeo videos, Instagram posts and tweets or Google Maps.
 */
export const createDBEmbedPlugin = createPluginFactory({
  key: ELEMENT_DB_EMBED,
  isElement: true,
  isVoid: true,
  handlers: {
    onDrop: (editor) => (e) => {
      return true;
    }
  },
});
