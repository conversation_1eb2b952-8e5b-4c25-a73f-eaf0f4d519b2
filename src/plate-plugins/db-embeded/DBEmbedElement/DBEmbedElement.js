import React, { useEffect, useState } from 'react';
import { findNodePath, getRootProps, setNodes, Value } from '@udecode/plate-common';

import { useIntl } from 'react-intl';
import { useDispatch } from 'react-redux';
import { createEmbedDoc } from 'src/actions/ticketAction';
import { FileSpreadsheet } from '@styled-icons/bootstrap/FileSpreadsheet';
import DBEditorFrame from 'src/components/dbeditor/DBEditorFrame';

export const DBEmbedElement = (
  props
) => {
  const { attributes, children, nodeProps, element, editor } = props;
  const rootProps = getRootProps(props);

  const [hid, setHid] = useState('');

  const intl = useIntl();
  const dispatch = useDispatch();

  useEffect(() => {
    if (element) {
      setHid(element.hid);
    }

    if(element && !element.hid) {
      createNewDBAndEmbed();
    }
  }, [element]);

  const createNewDBAndEmbed = () => {
    dispatch(createEmbedDoc({
      data: {
        blockId: element.id, 
        doc: {
          type: 'db',
          parent: editor.id
        },
        view: element.view,
        isNewDataSource: element.isNewDataSource
      }
    }, (item) => {
      setHid(item.newChild.hid);

      const path = findNodePath(editor, element);
      if (!path) return;

      setNodes(editor, { hid: item.newChild.hid }, { at: path });
    }));
  }

  let containerStyle = {
    position: 'relative',
    border: 'none',
    padding: 0,
    left: 0,
  }

  if (!hid) {
    containerStyle.border = '1px solid #ccc';
    containerStyle.padding = '50% 0 0 0';
    containerStyle.left = 'calc(50% - 400px)';
    containerStyle.maxWidth = '800px';
    containerStyle.width = '-webkit-fill-available';
  }

  return (
    <div
      {...attributes}
      style={{
        position: 'relative',
        width: '-webkit-fill-available',
        maxWidth: !hid? '800px' : undefined,
        zIndex: 0
      }}
      {...rootProps}
    >
      <div contentEditable={false}>
        <div
          style={{
            position: 'relative',
            border: hid ? 'none' : '1px solid #ccc',
            padding: hid ? '0' : '75% 0 0 0',
            left: hid ? '0px' : 'calc(50% - 400px)',

          }}
        >
          {
            hid &&
            <DBEditorFrame
              hid={hid}
              mode={'embed'}
            />
          }

          {
            !hid &&
            <div style={{
              position: 'absolute',
              top: '0',
              left: '0',
              width: '100%',
              height: '100%',
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center',
              alignItems: 'center',
              alignSelf: 'center',
            }}>
              <div
                className='hoverStand'
                style={{
                  color: '#999',
                  fontSize: '1rem',
                  fontWeight: 'bold',
                  width: 300,
                  columnGap: '6px',
                }}

                onClick={() => {
                  createNewDBAndEmbed();
                }}
              >
                <FileSpreadsheet size={20} />
                {intl.formatMessage({ id: 'create_new_db_view' })}
              </div>
            </div>
          }
        </div>
      </div>
      {children}
    </div>
  );
};
