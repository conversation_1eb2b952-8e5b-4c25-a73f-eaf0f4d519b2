import {
  PlateEditor,
  PlatePluginKey,
} from '@udecode/plate-common';
import { ELEMENT_DB_EMBED } from '../createDBEmbedPlugin';
import { TDBEmbedElement } from '../types';
import { Value, getParentNode, insertNodes } from '@udecode/plate-common';

export const insertDBEmbed = <V extends Value>(
  editor: PlateEditor<V>,
  {
    hid = '',
    view = '',
    isNewDataSource = true,
    key = ELEMENT_DB_EMBED,
  }: Partial<TDBEmbedElement> & PlatePluginKey
): void => {
  if (!editor.selection) return;
  const selectionParentEntry = getParentNode(editor, editor.selection);
  if (!selectionParentEntry) return;
  const [, path] = selectionParentEntry;
  insertNodes<TDBEmbedElement>(
    editor,
    {
      type: key,
      hid,
      view,
      isNewDataSource,
      children: [{ text: '' }],
    },
    { at: path }
  );
};
