import React, { useEffect, useState } from 'react';
import { findNodePath, setNodes, Value, getRootProps } from '@udecode/plate-common';
import { SlidesEmbedElementProps } from './SlidesEmbedElement.types';

import { get_server_host } from '../../../utils/serverAPIUtil';
import { useIntl } from 'react-intl';
import { useDispatch, useSelector } from 'react-redux';
import { createEmbedDoc, getDoc } from 'src/actions/ticketAction';
import { useHistory } from 'react-router-dom';
import SlidesActionBar from 'src/components/slides/SlidesActionBar';
import { PageChooser } from 'src/components/PageChooser';
import { FileSlides } from '@styled-icons/bootstrap/FileSlides';
import { Search } from '@styled-icons/material/Search';
import { useLocation } from 'react-router-dom';

export const SlidesEmbedElement = (
  props
) => {
  const { attributes, children, nodeProps, element, editor } = props;
  const rootProps = getRootProps(props);

  const currentLocation = useLocation();
  const params = new Proxy(new URLSearchParams(currentLocation.search), {
    get: (searchParams, prop) => searchParams.get(prop),
  });

  const [hid, setHid] = useState('');
  const docs = useSelector((state) => state.docs);
  const [doc, setDoc] = useState();
  const [showPageChooser, setShowPageChooser] = useState(false);
  const [url, setUrl] = useState();

  const [server_host, setServer_host] = React.useState();
  const intl = useIntl();
  const dispatch = useDispatch();
  const history = useHistory();

  useEffect(() => {
    get_server_host().then((value) => setServer_host(value));
  }, []);

  useEffect(() => {
    if (element) {
      setHid(element.hid);
    }
  }, [element]);

  useEffect(() => {
    if (hid && !docs.byId[hid]) {
      dispatch(getDoc({ hid: hid }));
    }
  }, [hid]);

  useEffect(() => {
    if (hid) {
      setDoc(docs.byId[hid]);
    }
  }, [hid, docs]);

  useEffect(() => {
    if (!doc || !server_host) {
      return;
    }

    setUrl(server_host + 'present.html?hid=' + hid + (doc.type === 'doc' ? '&doctype=doc' : ''))
  }, [doc, hid, server_host]);

  if (!server_host) {
    return null;
  }

  const createNewSlidesAndEmbed = () => {
    dispatch(createEmbedDoc({
      data: {
        blockId: element.id, 
        doc: {
          type: 'slides',
          parent: editor.id
        }
      }
    }, (item) => {
      setHid(item.newChild.hid);

      history.push({ pathname: '/slidesEditor', state: { hid: item.newChild.hid, hideHeader: true } });
    }));
  }

  return (
    <div
      {...attributes}
      style={{
        position: 'relative',
        width: '-webkit-fill-available'
      }}
      {...rootProps}
    >
      <div contentEditable={false}>
        <div
          style={{
            position: 'relative',
            padding: "75% 0 0 0",
            border: hid ? 'none' : '1px solid #ccc',
          }}
        >
          {
            hid &&
            <iframe
              style={{
                position: 'absolute',
                top: '0',
                left: '0',
                width: '100%',
                height: '100%',
              }}
              title="slides-embed"
              src={url}
              frameBorder="0"
              {...nodeProps}
            />
          }
          {
            hid &&
            doc &&
            <SlidesActionBar item={doc}
              mode={'fixed_at_viewer'}
              style={{ justifyContent: 'flex-start', position: 'absolute', left: 0, top: 9, zIndex: 3 }} />
          }
          {
            !hid &&
            <div style={{
              position: 'absolute',
              top: '0',
              left: '0',
              width: '100%',
              height: '100%',
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center',
              alignItems: 'center',
              alignSelf: 'center',
            }}>
              <div
                className='hoverStand'
                style={{
                  color: '#999',
                  fontSize: '1rem',
                  fontWeight: 'bold',
                  width: 300,
                  columnGap: '6px',
                }}

                onClick={() => {
                  createNewSlidesAndEmbed();
                }}
              >
                <FileSlides size={20} />
                {intl.formatMessage({ id: 'create_new_slides' })}
              </div>

              <div
                className='hoverStand'
                style={{
                  color: '#999',
                  fontSize: '1rem',
                  fontWeight: 'bold',
                  width: 300,
                  columnGap: '4px',
                }}

                onClick={() => {
                  setShowPageChooser(true);
                }}
              >
                <Search size={22} />
                {intl.formatMessage({ id: 'choose_existing_slides' })}
              </div>
            </div>
          }

          {
            showPageChooser &&
            <div style={{
              position: 'absolute',
              right: 0,
              top: 0,
              width: 300,
              height: '100%',
              backgroundColor: '#fff',
              borderLeft: '1px solid #ccc',
            }}>
              <PageChooser
                accepts={['slides']}
                excludes={[params.hid]}
                onSelect={(item) => {
                  setHid(item.hid);
                  setShowPageChooser(false);

                  const path = findNodePath(editor, element);
                  if (!path) return;

                  setNodes(editor, { hid: item.hid }, { at: path });
                }}
                onClose={() => {
                  setShowPageChooser(false);
                }}
              />
            </div>
          }
        </div>
      </div>
      {children}
    </div>
  );
};
