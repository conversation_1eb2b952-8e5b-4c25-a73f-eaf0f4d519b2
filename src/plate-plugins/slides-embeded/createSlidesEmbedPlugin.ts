import { createPluginFactory } from '@udecode/plate-common';

export const ELEMENT_SLIDES_EMBED = 'slides-embed';

/**
 * Enables support for embeddable media such as YouTube
 * or Vimeo videos, Instagram posts and tweets or Google Maps.
 */
export const createSlidesEmbedPlugin = createPluginFactory({
  key: ELEMENT_SLIDES_EMBED,
  isElement: true,
  isVoid: true,
  // then: (editor, { type }) => ({
  //   deserializeHtml: {
  //     rules: [
  //       {
  //         validNodeName: 'IFRAME',
  //       },
  //     ],
  //     getNode: (el: HTMLElement) => {
  //       const hid = el.getAttribute('hid');
  //       if (hid) {
  //         return {
  //           type,
  //           hid,
  //         };
  //       }
  //     },
  //   },
  // }),
});
