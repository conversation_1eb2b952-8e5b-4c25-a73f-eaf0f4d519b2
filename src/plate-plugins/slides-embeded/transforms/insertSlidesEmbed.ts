import {
  getParentN<PERSON>,
  insertN<PERSON>,
  PlateEditor,
  PlatePluginKey,
  Value,
} from '@udecode/plate-common';
import { ELEMENT_SLIDES_EMBED } from '../createSlidesEmbedPlugin';
import { TSlidesEmbedElement } from '../types';

export const insertSlidesEmbed = <V extends Value>(
  editor: PlateEditor<V>,
  {
    url = '',
    hid = '',
    key = ELEMENT_SLIDES_EMBED,
  }: Partial<TSlidesEmbedElement> & PlatePluginKey
): void => {
  if (!editor.selection) return;
  const selectionParentEntry = getParentNode(editor, editor.selection);
  if (!selectionParentEntry) return;
  const [, path] = selectionParentEntry;
  insertNodes<TSlidesEmbedElement>(
    editor,
    {
      type: key,
      url,
      hid,
      children: [{ text: '' }],
    },
    { at: path }
  );
};
