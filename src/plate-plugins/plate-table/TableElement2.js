
import { TableElement, TableToolbarButton, Popover, deleteRow, deleteTable, insertTableRow, insertTableColumn, deleteColumn, RemoveNodeButton } from '@udecode/plate-common';
import { TableInsertColumn } from '@styled-icons/fluentui-system-regular/TableInsertColumn'
import { TableInsertRow } from '@styled-icons/fluentui-system-regular/TableInsertRow'
import { TableDeleteColumn } from '@styled-icons/fluentui-system-regular/TableDeleteColumn'
import { TableDeleteRow } from '@styled-icons/fluentui-system-regular/TableDeleteRow'
import { Trash } from '@styled-icons/bootstrap/Trash'

import { useIntl } from "react-intl";
import { Tooltip } from "@mui/material";

const iconStyle = {
    width: '24px',
    height: '24px',
    padding: '4px',
    marginRight: '0.2rem',
    color: '#999'
}

export const TablePopover = ({
    element,
    popoverProps,
    children,
}) => {
    const intl = useIntl();
    console.log('TablePopover......................', element, popoverProps, children);
    return <Popover content={
        <>
            {/* <TableToolbarButton icon={<BorderClear style={iconStyle} />} transform={deleteTable} /> */}
            <Tooltip title={intl.formatMessage({ id: 'cmd_table_row' })} arrow placement="top">
                <div>
                    <TableToolbarButton icon={<TableInsertRow style={iconStyle} />} transform={insertTableRow} />
                </div>
            </Tooltip>
            <Tooltip title={intl.formatMessage({ id: 'cmd_table_column' })} arrow placement="top">
                <div>
                    <TableToolbarButton icon={<TableInsertColumn style={iconStyle} />} transform={insertTableColumn} />
                </div>
            </Tooltip>
            <Tooltip title={intl.formatMessage({ id: 'cmd_table_delete_row' })} arrow placement="top">
                <div>
                    <TableToolbarButton icon={<TableDeleteRow style={iconStyle} />} transform={deleteRow} />
                </div>
            </Tooltip>
            <Tooltip title={intl.formatMessage({ id: 'cmd_table_delete_column' })} arrow placement="top">
                <div>
                    <TableToolbarButton icon={<TableDeleteColumn style={iconStyle} />} transform={deleteColumn} />
                </div>
            </Tooltip>
            <Tooltip title={intl.formatMessage({ id: 'cmd_table_delete' })} arrow placement="top">
                <div>
                <TableToolbarButton icon={<Trash style={iconStyle} />} transform={deleteTable} />
                </div>
            </Tooltip>
        </>
    } {...popoverProps}>
        {children}
    </Popover>
}


export const TableElement2 = props => <TableElement {...props} onRenderContainer={TablePopover} />