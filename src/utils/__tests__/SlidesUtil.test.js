import { getLineNumberFromSlideIndex } from '../SlidesUtil';

describe('getLineNumberFromSlideIndex', () => {
  const sampleMarkdown = `# 第一张幻灯片
这是第一张幻灯片的内容

---

# 第二张幻灯片
这是第二张幻灯片的内容

--

# 第二张幻灯片的子页面
这是第二张幻灯片的子页面内容

---

# 第三张幻灯片
这是第三张幻灯片的内容`;

  test('应该返回第一张幻灯片的行号 (0,0)', () => {
    const lineNumber = getLineNumberFromSlideIndex(sampleMarkdown, 0, 0);
    expect(lineNumber).toBe(1);
  });

  test('应该返回第二张幻灯片的行号 (1,0)', () => {
    const lineNumber = getLineNumberFromSlideIndex(sampleMarkdown, 1, 0);
    expect(lineNumber).toBe(6); // "---" 后的第一行
  });

  test('应该返回第二张幻灯片子页面的行号 (1,1)', () => {
    const lineNumber = getLineNumberFromSlideIndex(sampleMarkdown, 1, 1);
    expect(lineNumber).toBe(11); // "--" 后的第一行
  });

  test('应该返回第三张幻灯片的行号 (2,0)', () => {
    const lineNumber = getLineNumberFromSlideIndex(sampleMarkdown, 2, 0);
    expect(lineNumber).toBe(16); // 最后一个 "---" 后的第一行
  });

  test('对于不存在的幻灯片索引应该返回第1行', () => {
    const lineNumber = getLineNumberFromSlideIndex(sampleMarkdown, 10, 0);
    expect(lineNumber).toBe(1);
  });
});
