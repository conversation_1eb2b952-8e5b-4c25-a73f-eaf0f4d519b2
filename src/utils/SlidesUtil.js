export const getTargetSlide = (content, cursorLine) => {
  var lines = content.split('\n');
  var line = "";
  var slide = 0;
  var subSlide = 0;
  var page = 0;
  let prevSeperatorAt = -1;
  
  for (let i = 0; i < cursorLine; i++) {
    line = lines[i];
    if (/^--- *$/.test(line)) {
      prevSeperatorAt = i;
      slide = slide + 1;
      subSlide = 0;
      page++;
    } else if (/^-- *$/.test(line)) {
      prevSeperatorAt = i;
      subSlide = subSlide + 1;
      page++;
    }
  }
  
  // 按页面分割线分割内容
  const pages = content.split(/\n---? *\n/);
  
  // 优化的 hasNotes 判断逻辑
  const hasNotes = checkHasNotes(pages);
  
  var slideNumber = {
    "h": slide,
    "v": subSlide,
    "currentPage": pages[page] || '',
    "currentPageToCurrentLine": {
      content: lines.slice(prevSeperatorAt + 1, cursorLine).join('\n'),
      start: prevSeperatorAt === -1 ? 0 : lines.slice(0, prevSeperatorAt + 1).join('\n').length + 1,
      end: lines.slice(0, cursorLine).join('\n').length,
    },
    lines,
    "totalPages": {
      h: (content.match(/\n--- *\n/g) || []).length,
      v: (content.match(/\n-- *\n/g) || []).length
    },
    hasNotes,
    content
  };
  return slideNumber;
};

// 检查是否有有效的Notes内容
function checkHasNotes(pages) {
  for (const page of pages) {
    const lines = page.split('\n');
    let notesStartIndex = -1;
    
    // 查找 Notes: 或 Note: 开头的行
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      if (/^Notes?:\s*/.test(line)) {
        notesStartIndex = i;
        break;
      }
    }
    
    if (notesStartIndex !== -1) {
      // 获取Notes部分的内容
      const notesLines = lines.slice(notesStartIndex);
      const notesContent = extractNotesContent(notesLines);
      
      // 检查Notes内容是否包含可见字符（非空白字符）
      if (hasVisibleContent(notesContent)) {
        return true;
      }
    }
  }
  return false;
}

// 提取Notes内容（去除Notes:标题行，获取实际内容）
function extractNotesContent(notesLines) {
  if (notesLines.length === 0) return '';
  
  // 第一行可能包含 "Notes:" 标题，需要提取其后的内容
  const firstLine = notesLines[0];
  const notesMatch = firstLine.match(/^Notes?:\s*(.*)$/);
  
  let content = '';
  if (notesMatch && notesMatch[1]) {
    // 如果Notes:同行有内容，加入到内容中
    content += notesMatch[1];
  }
  
  // 添加后续行的内容
  if (notesLines.length > 1) {
    const remainingLines = notesLines.slice(1).join('\n');
    content += (content ? '\n' : '') + remainingLines;
  }
  
  return content;
}

// 检查内容是否包含可见字符
function hasVisibleContent(content) {
  // 去除空白字符后检查是否还有内容
  return content.trim().length > 0 && /[^\s]/.test(content);
}

export const getLineNumberFromSlideIndex = (content, indexh, indexv) => {
  const lines = content.split('\n');
  let currentH = 0;
  let currentV = 0;

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];

    if (/^--- *$/.test(line)) {
      // 遇到水平分隔符
      currentH++;
      currentV = 0;

      // 如果找到了目标幻灯片，返回下一行的行号（分隔符后的第一行）
      if (currentH === indexh && currentV === indexv) {
        return i + 2; // +2 因为要跳过分隔符行，并且行号从1开始
      }
    } else if (/^-- *$/.test(line)) {
      // 遇到垂直分隔符
      currentV++;

      // 如果找到了目标幻灯片，返回下一行的行号（分隔符后的第一行）
      if (currentH === indexh && currentV === indexv) {
        return i + 2; // +2 因为要跳过分隔符行，并且行号从1开始
      }
    }
  }

  // 如果是第一张幻灯片 (0,0)，返回第1行
  if (indexh === 0 && indexv === 0) {
    return 1;
  }

  // 如果没找到，返回第1行
  return 1;
};

export const  sendMessageToPreview = (server_host, method, params, args) => {
  document.getElementById('slides-frame').contentWindow.postMessage(JSON.stringify({
    method,
    params,
    args
  }), server_host);
};