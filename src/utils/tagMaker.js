/**
 * Created by Administrator on 2017/2/28 0028.
 */

var cheerio = require('react-native-cheerio');
var _ = require('underscore');
var md5 = require('md5');


const tagStaticCount = 1;//起始的count
var tagIdCount = 1;
var tagIdPrefix = '';
var createELightTag = 'CreateTag';
//可保留的文本标签
var keepTagArray = ['h1', 'h2', 'h3', 'ul', 'li', 'ol', 'dl', 'dt', 'dd', 'blockquote', 'strong', 'p']

function createTag(htmlContent, url) {
    console.log(createELightTag, 'will start create tag, url: ' + url);
    var $;
    tagIdPrefix = md5(url);
    try {
        $ = cheerio.load(htmlContent, {decodeEntities: false});

        let bodyText = $('body').text();
        if(!bodyText || bodyText.length<50){
            return null;
        }

        let elights = $('elight');
        if(elights && elights.length>0) {
            console.log(createELightTag, 'elight tag existed for this article, url: ' + url);
            return htmlContent;
        }
        
        createHighLightTag($, $.root());
        init();
        return $.html();
    } catch (err) {
        console.log(createELightTag, 'cheerio load html failed, return original content, error: ' + err);
        init()
    }
    return htmlContent;
}

function createHighLightTag($, node) {
    const reg = /[.。！!？?;；]+["”]?/g;
    _.each($(node).contents(), function (item){
        if($(node).is('script') || $(node).is('style') || $(node).is('head')) {
            return;
        }
        
        if (item && item.type === 'text' && item.data.replace(/\s/g, "") !== ''){
            $(item).wrap('<elight id=' + tagIdPrefix + '_' + tagIdCount++ + '></elight>');
            if (item.data.match(reg)) {
                item.data = item.data.replace(reg, function (replaced, index, alltext) {
                    //若存在英文引号，则通过前文引号的奇偶判断引号的左右
                    if (replaced.match('"')) {
                        var matchArray = (alltext.substr(0, index)).match(/"/g);
                        matchArray = matchArray ? matchArray : [];
                        //引号为右时，标签在引号右侧，引号为左时，标签在引号左侧
                        if (matchArray){
                            return matchArray.length % 2 ? replaced + '</elight><elight id="' + tagIdPrefix + '_' + tagIdCount++ + '">'
                                : replaced.slice(0, -1) + '</elight><elight id="' + tagIdPrefix + '_' + tagIdCount++ + '">' + replaced.slice(-1);
                        }
                    }
                    return replaced + '</elight><elight id="' + tagIdPrefix + '_' + tagIdCount++ + '">';
                });
            }
        }
        if ($(node).children().length > 0) {
            createHighLightTag($, item);
        }
    });
    return $;
}

function createTemplate ($, node) {
    //nodeType为1时，即该节点下存在==文本TAG文本==的格式
    var nodeType;

    _.each($(node).contents(), function(item){
        if (item && item.type == 'text' && item.data.trim()) {
            nodeType = true;
        }
    })

    //当存在==文本TAG文本==的格式且TAG不属于可接受的TAG时，删去TAG
    _.each($(node).contents(), function(item) {
        if (nodeType && item && item.type != 'text' && !_.contains(keepTagArray, item.name.trim())) {
            $(item).replaceWith($(item).text());
        } else if (!nodeType && item && item.type == 'text' && item.data.trim() && !_.contains(keepTagArray, node.name.trim())) {
            $(item).wrap('<p></p>');
        }

        if ($(node).children().length > 0) {
            createTemplate($, item);
        }
    })
    return $;
}

function init(){
    tagIdCount = tagStaticCount;
    tagIdPrefix = '';
}

exports.createTag = createTag;
