import { jsx } from 'slate-hyperscript'
import escapeHtml from 'escape-html'
import { Text as TextNode } from 'slate'
// import { parseVideoUrl } from '@udecode/plate-common';
import { cloneDeep } from 'lodash';

const getId = (el, idSet) => {
    let id = el.getAttribute && el.getAttribute('id');
    if (id) {
        id = parseInt(id);
    }
    if (!id || isNaN(id) || idSet.has(id)) {
        while (true) {
            id = new Date().getTime() + Math.floor(Math.random() * 1000)
            if (!idSet.has(id)) {
                break
            }
        }
    }

    idSet.add(id);

    return id;
}

const deserialize = async (el, markAttributes = {}, idSet) => {
    if (el.nodeType === 3) {
        if (el.textContent?.trim()) {
            return jsx('text', markAttributes, el.textContent.replace(/^\s+/g, ''));
        }

        return null;
    } else if (el.nodeType !== 1) {
        return null;
    }

    const nodeAttributes = { ...markAttributes }

    // define attributes for text nodes
    switch (el.nodeName?.toLowerCase()) {
        case 'strong':
        case 'b':
            nodeAttributes.bold = true;
            break;
        case 'i':
        case 'em':
            nodeAttributes.italic = true;
            break;
        case 'u':
            nodeAttributes.underline = true;
            break;
        case 'strike':
        case 'del':
            nodeAttributes.strikethrough = true;
            break;
        case 'sup':
            nodeAttributes.superscript = true;
            break;
        case 'sub':
            nodeAttributes.subscript = true;
            break;
        case 'code':
            nodeAttributes.code = true;
            nodeAttributes.lang = el.getAttribute('class')?.replace('language-', '');
            break;
    }

    const children = (await Promise.all(
        Array.from(el.childNodes)
            .map(async node => {
                // console.log('child............', {parent: el.nodeName, child: node.nodeName, deserialized: JSON.stringify(await deserialize(node, nodeAttributes, idSet, req))})
                return await deserialize(node, nodeAttributes, idSet);
            })
    )).flat().filter(n => !!n)

    if (children.length === 0) {
        children.push(jsx('text', nodeAttributes, ''))
    }

    const id = getId(el, idSet);

    // console.log('el & id....................', id, el.nodeName, JSON.stringify(el));

    switch (el.nodeName?.toLowerCase()) {
        case 'br':
            return { text: '\n' }
        case 'h1':
        case 'h2':
        case 'h3':
        case 'h4':
        case 'h5':
        case 'h6':
        case 'hr':
        case 'td':
        case 'tr':
        case 'th':
        case 'table':
        case 'blockquote':
        case 'ol':
        case 'ul':
            let eleAttr = { type: el.nodeName.toLowerCase(), id };
            if (el.style.textAlign) {
                eleAttr.align = el.style.textAlign;
            }

            return jsx('element', eleAttr, children)
        case 'li':
            if (children[0].type === 'action_item') {
                return children[0];
            }

            return jsx('element', { type: 'li', id }, children)
        case 'input':
            if (el.getAttribute('type')?.toLowerCase() === 'checkbox') {
                return jsx('element', { type: 'action_item', checked: el.hasAttribute('checked'), id }, children)
            }

            return null;
        case 'pre':
            if (children[0].code) {
                let code_block = children[0].text;
                let lines = code_block.split('\n');
                let code_lines = lines.filter((l, i) => i < lines.length - 1 || l.trim()).map(line => {
                    return { type: 'code_line', children: [{ text: line }] };
                })

                return jsx('element', { type: 'code_block', id, lang: children[0].lang }, code_lines)
            }

            return null;

        case 'span':
            children[0].color = el.style.color;
            children[0].backgroundColor = el.style.backgroundColor;
            children[0].fontSize = el.style.fontSize;
            children[0].fontWeight = el.style.fontWeight;

            return children;
        case 'p':
            if (children[0].type === 'action_item') {
                let item = children.shift();
                item.children = children;
                return item;
            }

            if (children.length === 1 && children[0].type && !['a', 'code_line'].includes(children[0].type)) {
                return children[0];
            }

            if (children.length === 1 && children[0].text === '\n') {
                return jsx('element', { type: 'p', id }, [{ text: '' }])
            }

            let textGroup = [];
            let newChildren = [];
            children.forEach(child => {
                if (!child.type || ['a'].includes(child.type)) {
                    textGroup.push(child);
                } else {
                    if (textGroup && textGroup.length) {
                        newChildren.push({
                            type: 'p',
                            id: getId(null, idSet),
                            children: textGroup
                        });
                        textGroup = [];
                    }
                    newChildren.push(child);
                }
            });

            if (newChildren.length === 0) {
                newChildren = children;
            }

            let elementAttr = { type: el.nodeName.toLowerCase(), id };
            if (el.style.textAlign) {
                elementAttr.align = el.style.textAlign;
            }

            return jsx('element', elementAttr, newChildren)
        case 'a':
            if (!el.getAttribute('href')) {
                return children
            }

            return jsx('element', { type: 'a', url: el.getAttribute('href'), id }, children)
        case 'img':
            const width = el.style.width;
            let src = el.getAttribute('src');

            let element = { type: 'img', url: src || el.getAttribute('src'), id };
            if (width) {
                element.width = width;
            }

            return jsx('element', element, [{ text: '' }])

        case 'div':
            // if (el.style.textAlign) {
            //     return jsx('element', { type: 'p', align: el.style.textAlign, id }, children)
            // }
            if (el.classList.contains('action_item_icon')) {
                return null;
            }

            if (el.classList.contains('action_item') || el.classList.contains('action_item_done')) {
                return jsx('element', { type: 'action_item', checked: el.classList.contains('action_item_done'), id }, children)
            }
            return children

        default:
            return children
    }
}

export const htmlToSlate = async (html) => {
    const document = new DOMParser().parseFromString(html, 'text/html');

    const idSet = new Set();
    let children = await deserialize(document.body, {}, idSet);
    children = children.map(child => {
        if (child.type) {
            return child;
        }

        return jsx('element', { type: 'p', id: getId(null, idSet) }, child)
    });

    return children;
}

const composeTextNodeStyle = (node) => {
    let style = '';

    if(node.color) {
        style = `color: ${node.color};`;
    }

    if(node.backgroundColor) {
        style += `background-color: ${node.backgroundColor};`;
    }

    if(node.fontSize) {
        style += `font-size: ${node.fontSize};`;
    }

    if(node.fontWeight) {
        style += `font-weight: ${node.fontWeight};`;
    }

    return style;
}

const serialize = (server_host, node, mode) => {
    if (TextNode.isText(node)) {
        let string = escapeHtml(node.text)
        if (node.bold) {
            string = `<b>${string}</b>`
        }

        if (node.italic) {
            string = `<i>${string}</i>`
        }

        if (node.underline) {
            string = `<u>${string}</u>`
        }

        if (node.code) {
            string = `<code>${string}</code>`
        }

        if (node.strikethrough) {
            string = `<strike>${string}</strike>`
        }

        if (node.superscript) {
            string = `<sup>${string}</sup>`
        }

        if (node.subscript) {
            string = `<sub>${string}</sub>`
        }

        if (node.highlight) {
            node.backgroundColor = '#FEF3B7';
        }

        let style = composeTextNodeStyle(node);
        if(style) {
            string = `<span style="${style}">${string}</span>`
        }

        return string
    }

    const children = node.children.map(n => serialize(server_host, n, mode)).join('')

    switch (node.type) {
        case 'blockquote':
            return `<blockquote><p>${children}</p></blockquote>`
        case 'ol':
            return `<ol>${children}</ol>`
        case 'ul':
            return `<ul>${children}</ul>`
        case 'li':
            return `<li>${children}</li>`
        case 'action_item':
            if (mode === 'view') {
                if (node.checked) {
                    return `<div style="display: flex; flex-direction:row; align-items: center;"><div style="font-size: 22px; color: dodgerblue">&#9745;&nbsp;</div><div>${children}</div></div>`
                } else {
                    return `<div style="display: flex; flex-direction:row; align-items: center;"><div style="font-size: 22px; color: #666">&#9744;&nbsp;</div>${children}</div>`
                }
            }
            return `<div><ol class="x-todo"><li><span class="x-todo-box"><input type="checkbox" ${node.checked ? 'checked' : ''}></span>${children}</li></ol></div>`

        case 'p':
            if (['disc', 'decimal'].includes(node.listStyleType)) {
                const listTag = node.listStyleType === 'disc' ? 'ul' : 'ol';
                return `<div style="margin-left: ${(node.indent - 1) * 24}px"><${listTag}><li>${children || '<br>'}</li></${listTag}></div>`
            }

            if (node.children && node.children[0] && node.children[0].code) {
                return `<pre>${children || '<br>'}</pre>`
            }

            if (mode === 'view') {
                return `<p>${children || '<br>'}</p>`
            }

            return `<div>${children || '<br>'}</div>`
        case 'a':
            return `<a href="${escapeHtml(node.url)}">${children}</a>`
        case 'img':
            if (node.width && !isNaN(node.width)) {
                node.width = `${node.width}px`
            }

            if (!node.width) {
                node.width = '80%'
            }

            return `<img style="width: ${node.width};  margin: 12px;" src="${escapeHtml(node.url)}">`
        case 'code_block':
            return `<pre><code class="language-${node.lang || 'plaintext'}">${children}</code></pre>`
        case 'code_line':
            return `${children}\n`
        case 'h1':
            return `<h1>${children}</h1>`
        case 'h2':
            return `<h2>${children}</h2>`
        case 'h3':
            return `<h3>${children}</h3>`
        case 'h4':
            return `<h4>${children}</h4>`
        case 'h5':
            return `<h5>${children}</h5>`
        case 'h6':
            return `<h6>${children}</h6>`
        case 'hr':
            return `<hr>`

        case 'mention':
            return `<span class="mention">${node.value.includes('@') ? '' : '@'}${node.value}</span>`
        case 'tag':
            return `<span class="tag">#${node.value}</span>`

        case 'subpage':
            let url = server_host + '#/';
            if (node.docType === 'doc') {
                url += 'editor';
            } else if (node.docType === 'db') {
                url += 'db';
            } else if (node.docType === 'flow') {
                url += 'flow';
            } else {
                url += 'slidesViewer';
            }
            url += `?hid=${node.hid}`;

            return `<a href="${url}" target="_blank">${node.title}</a>`;

        case 'slides-embed':
        case 'db-embed':
            const embed_url = node.type === 'db-embed' ?
                `${server_host}#/embed/db?hid=${node.hid}` :
                `${server_host}present.html?hid=${node.hid}`
            return `<iframe src="${embed_url}" frameBorder="0"></iframe>`

        // case 'media_embed':
        //     const parsedUrl = parseVideoUrl(node.url);

        //     if (parsedUrl?.url) {
        //         return `<iframe src="${parsedUrl?.url}" frameBorder="0"></iframe>`
        //     }

        //     return `<video src="${node.url}" />`

        case 'rilmark':
            return `<blockquote ><p>${node.highlight}</p></blockquote> ${node.note ? (`<p>${'Notes: ' + node.note[0]?.text || '<br>'}</p>`) : ''}`

        case 'math_inline':
            return `$$${node.expression}$$`
        case 'math_block':
            return `<div>\n\\[${node.expression} \\]\n</div>`
        case 'table':
            return `<table>${children}</table>`
        case 'tr':
            return `<tr>${children}</tr>`
        case 'td':
            return `<td>${children}</td>`
        case 'th':
            return `<th>${children}</th>`

        default:
            return children
    }
}

const extractTagsFromBlock = (block, tags) => {
    if (block.type == 'tag') {
        if (!tags.includes(block.value)) {
            tags.push(block.value);
        }
    } else if (block.children) {
        block.children.forEach(child => {
            extractTagsFromBlock(child, tags);
        });
    }
}

export const extractTags = (blocks) => {
    let tags = [];
    blocks && blocks.forEach(block => {
        extractTagsFromBlock(block, tags);
    });
    return tags;
}

const breakSlateNode = (node) => {
    if (node.type === 'p' && node.children) {
        node.children = node.children.map(child => {
            if (child.text) {
                const children = [];
                const ms = child.text.split(/([@|#][\p{L}]+)/gui);

                console.log('ms................', ms);
                let preText;
                ms.forEach(m => {
                    if (!m.startsWith('@') && !m.startsWith('#')) {
                        preText = m;
                    } else {
                        children.push({
                            ...child,
                            text: preText || '',
                        });
                        preText = null;
                        children.push({
                            type: m.startsWith('@') ? 'mention' : 'tag',
                            id: new Date().getTime(),
                            value: m.substring(1),
                            children: [{ text: '' }]
                        });
                    }
                });

                if (preText) {
                    children.push({
                        ...child,
                        text: preText,
                    });
                }

                return children;
            }

            return [child];
        }).flat();

        if (['mention', 'tag'].includes(node.children[node.children.length - 1].type)) {
            node.children.push({
                text: '',
            });
        }

    } else if (node.children) {
        node.children.forEach(child => {
            breakSlateNode(child);
        });
    }
}

// export const htmlToSlate = (html, originalBlocks) => {
//     const document = new DOMParser().parseFromString(html, 'text/html');

//     const idSet = new Set();
//     const children = deserialize(document, {}, idSet, originalBlocks);

//     children.forEach(child => {
//         breakSlateNode(child);
//     });

//     const tags = extractTags(children);

//     return { slateContent: jsx('fragment', {}, children), tags };
// }

const makeIndentListWrapper = (prevChild, child, list) => {
    if (!prevChild || prevChild.type != 'p' || !['disc', 'decimal'].includes(prevChild.listStyleType)
        || !findListItemParent(list, child)
    ) {
        let current_list = makeChildList(1, child);

        return current_list
    } else if (prevChild && ['disc', 'decimal'].includes(prevChild.listStyleType)) {
        let parentList = findListItemParent(list, child);

        let childList = makeChildList(child.indent - parentList.indent, child);

        parentList.children.push(childList);

        // console.log('list added child', { list: JSON.stringify(list, null, 4) });
    }

    return null;
}

const makeChildList = (levels, child) => {

    if (isNaN(levels)) {
        return;
    }

    if (levels < 0) {
        return;
    }

    if (levels === 0) {
        return {
            type: 'li',
            children: [{
                type: 'lic',
                children: child.children
            }],
            indent: child.indent
        }
    } else {
        return {
            type: child.listStyleType === 'disc' ? 'ul' : 'ol',
            children: [makeChildList(0, child)],
            indent: child.indent - levels + 1
        }

    }
}

const findListItemParent = (list, child) => {
    if (list.indent > child.indent) {
        return null;
    }

    if (list.indent == child.indent && ['ol', 'ul'].includes(list.type)) {
        return list;
    }

    let lastChild = list.children && list.children[list.children.length - 1];
    let lastChildType = lastChild.children && lastChild.children[lastChild.children.length - 1].type;


    if (!lastChild || !['ol', 'ul'].includes(lastChildType)) {
        return lastChild;
    }

    return findListItemParent(lastChild, child);
}


export const slateToHtml = (server_host, slate, mode) => {
    let blocks = [];

    let current_list = null;
    cloneDeep(slate.children).forEach((child, index) => {
        if (child.type === 'p' && ['disc', 'decimal'].includes(child.listStyleType)) {
            let prevChild = index > 0 && slate.children[index - 1];

            let childlist = makeIndentListWrapper(prevChild, child, current_list);
            if (!!childlist) {
                current_list = childlist;
                blocks.push(childlist);
            }
        } else {
            blocks.push(child);
        }
    });

    // console.log('blocks..........', blocks)

    const children = blocks.map(node => {
        return serialize(server_host, node, mode);
    })

    return `
<!DOCTYPE html>
<html>
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    
        <style>
            body {
                display: flex;
                flex-direction: column;
                align-items: center;
            }

            .content {
                width: 800px;
            }

            p {
                padding-top: 4px; 
                padding-bottom: 4px;
                color: #333333;
                font-size: 1em;
            }

            iframe {
                width: 800px;
                height: 600px;
            }

            pre {
                background: rgba(135, 131, 120, 0.15);
                border-radius: 3px;
                padding: 1em;
                font-size: 90%;
                tab-size: 2;
            }

            table,
            th,
            td {
                border: 1px solid rgba(55, 53, 47, 0.09);
                border-collapse: collapse;
            }

            table {
                border-left: none;
                border-right: none;
            }

            th,
            td {
                font-weight: normal;
                padding: 0em 0.5em;
                line-height: 1.5;
                min-height: 1.5em;
                text-align: left;
            }

            th {
                color: rgba(55, 53, 47, 0.6);
            }

            blockquote {
                margin: 1em 0;
                padding-left: 1em;
                border-left: 2px solid #ddd;
            }

            .mention {
                background-color: #eee; 
                padding-left: 8px; 
                padding-right: 8px; 
                border-radius: 8px
            }

            .tag {
                background-color: aliceblue; 
                color: royalblue; 
                padding-left: 8px; 
                padding-right: 8px; 
                border-radius: 8px
            }
        </style>
    </head>
    <body>
        <div class="content">
            ${children.join('\n')}
        </div>
    </body>
</html>`
}




