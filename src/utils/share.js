import Share from 'react-native-share';
import { get_server_host } from './serverAPIUtil';

function truncate(str, n, useWordBoundary) {
    if (str.length <= n) { return str; }
    var subString = str.substr(0, n - 1);
    lastSpace = subString.lastIndexOf(' ');

    return (useWordBoundary && lastSpace > 0
        ? subString.substr(0, lastSpace)
        : subString) + "...";
};

export const shareSlide = async (slide) => {
    const server_host = await get_server_host();
    const url = `${server_host}present.html?hid=${slide.hid}`;
    const title = `Ticket: ${truncate(slide.title, 40, true)}`;
    const message = slide.title;
    const icon = 'data:<data_type>/<file_extension>;base64,<base64_data>';
    const options = Platform.select({
        ios: {
            activityItemSources: [
                { // For using custom icon instead of default text icon at share preview when sharing with message.
                    subject: {
                        default: title,
                    },
                    placeholderItem: {
                        type: 'text',
                        content: message
                    },
                    item: {
                        default: {
                            type: 'text',
                            content: `${message} ${url}`
                        },
                    },
                    linkMetadata: {
                        title: message,
                        icon: icon
                    }
                },
            ],
        },
        default: {
            title,
            subject: title,
            message: `${message} ${url}`,
        },
    });

    Share.open(options)
        .then((res) => { console.log(res) })
        .catch((err) => { err && console.log(err); });;
}