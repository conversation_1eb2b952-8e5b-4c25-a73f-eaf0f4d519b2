import Tts from "react-native-tts";
import MusicControl from 'react-native-music-control';
import { Platform } from "react-native";

const isIOS = Platform.OS === 'ios';
const TtsInstance = (function () {

    return {
        init: async function ({ speechRate, pitch, voice, ttsFinished, onStart, ttsProgress, onPause, onStop, onNext, onPrevious, noMusicControl }) {
            try {
                if (voice) {
                    await Tts.setDefaultLanguage(voice.language);
                    await Tts.setDefaultVoice(voice.id);
                } else {
                    await Tts.setDefaultLanguage('zh');
                }
                await Tts.getInitStatus();
            } catch (err) {
                if (err.code === 'no_engine') {
                    Tts.requestInstallEngine();
                }
                if (err.code === 'lang_missing_data' || err.code === 'lang_not_supported') {
                    Tts.requestInstallData();
                    await Tts.setDefaultLanguage('zh');
                }

                return err.code;
            }

            Tts.setDucking(true);
            Tts.setDefaultRate(speechRate || 0.52);
            Tts.setDefaultPitch(pitch || 1);
            Tts.setIgnoreSilentSwitch("ignore");

            if (!noMusicControl) {
                MusicControl.enableControl('play', true)
                MusicControl.enableControl('pause', true)
                MusicControl.enableControl('stop', true)
                MusicControl.enableControl('nextTrack', true)
                MusicControl.enableControl('previousTrack', true)

                MusicControl.enableControl('changePlaybackPosition', false)

                MusicControl.enableControl('closeNotification', true, { when: 'paused' });

                MusicControl.enableBackgroundMode(true);
                MusicControl.handleAudioInterruptions(false);
            }


            Tts.removeAllListeners("tts-finish");
            Tts.removeAllListeners("tts-start");
            Tts.removeAllListeners("tts-progress");
            Tts.removeAllListeners("tts-cancel");

            Tts.addEventListener("tts-finish", ttsFinished);
            Tts.addEventListener("tts-start", onStart);
            Tts.addEventListener("tts-progress", ttsProgress);
            Tts.addEventListener("tts-cancel", () => { });

            if (!noMusicControl) {
                MusicControl.on('pause', onPause);
                MusicControl.on('stop', onStop);
                MusicControl.on('play', onStart);
                MusicControl.on('nextTrack', onNext)
                MusicControl.on('previousTrack', onPrevious);
            }

            return null;
        },
        uninit: function () {
            Tts.removeAllListeners("tts-finish");
            Tts.removeAllListeners("tts-start");
            Tts.removeAllListeners("tts-progress");
            Tts.removeAllListeners("tts-cancel");
        },
        speak: (utterance) => {
            Tts.speak(utterance);
        },
        stop: Tts.stop,
        pause: (onWordBoundary) => {
            Tts.pause(onWordBoundary);
        },
        resume: Tts.resume
    };
})();

export default TtsInstance;