import { getUninstallReasons, saveUninstallReason } from "@/actions/ticketAction";
import { getLocale } from "@/utils/Intl";
import { Button } from "@mui/material";
import { useEffect, useState } from "react";
import { useIntl } from "react-intl";
import { useDispatch, useSelector } from "react-redux";

const ExtensionUninstalled = () => {
    const dispatch = useDispatch();
    const intl = useIntl();
    const [reason, setReason] = useState("");
    const [detail, setDetail] = useState("");
    const [reasons, setReasons] = useState([]);
    const lng = useSelector(state => state.uiState.lng) || getLocale();
    const operationStatus = useSelector(state => state.operationStatus);
    const [loading, setLoading] = useState(false);
    const [saved, setSaved] = useState()
    const [valid, setValid] = useState({
        reason: true,
        detail: true
    })

    useEffect(() => {
        dispatch(getUninstallReasons({ locale: lng }, (items) => {
            setReasons(items)
        }))
    }, [])

    useEffect(() => {
        if (!operationStatus?.inProgress) {
            setLoading(null);
        }
    }, [operationStatus])

    const handleSubmit = (e) => {
        e.preventDefault();

        if (!reason || !detail) {
            return setValid({
                reason: !!reason,
                detail: !!detail
            })
        }
        // 收集用户输入的卸载原因
        // 将卸载原因发送到服务器
        setLoading(true)

        dispatch(saveUninstallReason({
            reason,
            detail
        }, () => {
            setSaved(true);
        }))
    };

    return (
        <div className="full-height" style={{ display: 'flex', flexDirection: 'column', width: '100%', alignItems: 'center', justifyContent: 'center' }}>
            {
                !saved &&
                <div style={{ maxWidth: 800 }}>
                    <h2>{intl.formatMessage({ id: 'sorry_for_uninstall' })} :(</h2>
                    <p>{intl.formatMessage({ id: 'please_tell_your_uninstall_reason' })}:</p>
                    {reasons.map((item) => (
                        <div
                            style={{
                                cursor: 'pointer',
                                padding: 6,
                                paddingLeft: 0
                            }}
                            key={item.value}
                        >
                            <input
                                type="radio"
                                name="reason"
                                value={item.value}
                                id={item.value}
                                checked={reason === item.value}
                                onChange={(e) => {
                                    setReason(e.target.value)
                                    setValid({
                                        ...valid,
                                        reason: !!e.target.value
                                    })
                                }}
                                style={{
                                    cursor: 'pointer'
                                }}
                            />
                            <label
                                htmlFor={item.value}
                                style={{
                                    cursor: 'pointer',
                                    paddingLeft: 6
                                }}
                            >{item.label}</label>
                        </div>
                    ))}
                    {
                        !valid?.reason &&
                        <div style={{
                            color: 'red',
                            fontSize: 14
                        }}>
                            {intl.formatMessage({ id: 'please_give_reason' })}
                        </div>
                    }

                    <textarea
                        style={{
                            marginTop: 10,
                            border: '1px solid gray',
                            borderRadius: 5,
                            outline: 'none',
                            width: '-webkit-fill-available',
                            fontFamily: 'inherit',
                            lineHeight: 1.2,
                            padding: 4
                        }}
                        placeholder={reasons?.find(r => r.value == reason)?.hint}
                        rows={5}
                        value={detail}
                        onChange={(event) => {
                            setDetail(event.target.value)
                            setValid({
                                ...valid,
                                detail: !!event.target.value
                            })
                        }}
                    />
                    {
                        !valid?.detail &&
                        <div style={{
                            color: 'red',
                            fontSize: 14
                        }}>
                            {intl.formatMessage({ id: 'please_give_detail' })}
                        </div>
                    }

                    <div style={{
                        marginTop: 10,
                        width: '100%',
                        display: 'flex',
                        flexDirection: 'row',
                        justifyContent: 'flex-end'
                    }}>
                        <Button variant="contained" onClick={handleSubmit} disabled={loading}>
                            {intl.formatMessage({ id: 'submit' })}
                        </Button>
                    </div>

                </div>
            }
            {
                saved &&
                <div style={{ display: 'flex', flexDirection: 'column', maxWidth: 800 }}>
                    <p>
                        {intl.formatMessage({ id: 'thanks_for_feedback' })}
                    </p>
                    <p>
                        {intl.formatMessage({ id: 'intro_funblocks' })}
                    </p>
                    <div>
                        <a
                            href={`https://${window.location.host}`}
                            // variant="contained"
                            // onClick={() => {
                            //     window.open(`https://${window.location.host}`)
                            // }} disabled={loading}
                        >
                            {`https://${window.location.host}`}
                        </a>
                    </div>
                </div>
            }
        </div>
    );
}

export default ExtensionUninstalled