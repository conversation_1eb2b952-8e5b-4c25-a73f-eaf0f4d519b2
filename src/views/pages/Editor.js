import { withRouter, useLocation } from "react-router-dom";
import EditorEntrance from "src/components/editor/EditorEntrance";

const Editor = (props) => {
    const currentLocation = useLocation();
    const params = new Proxy(new URLSearchParams(currentLocation.search), {
        get: (searchParams, prop) => searchParams.get(prop) || '',
    });

    const { hid, space, orderFactor, title } = params;

    return <EditorEntrance
        hid={hid}
        title={title}
        space={space}
        orderFactor={orderFactor}
        mode={'window'}
    />
};

export default withRouter(Editor);