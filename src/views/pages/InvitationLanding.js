import { INVITED_CODE } from "@/constants/actionTypes";
import { Button } from "@mui/material";
import { useEffect, useState } from "react";
import { useIntl } from "react-intl";
import { useDispatch } from "react-redux";
import { useLocation } from "react-router-dom";
import ReactGA from "react-ga4";

const InvitationLanding = () => {
    const intl = useIntl();
    const dispatch = useDispatch();

    const currentLocation = useLocation();
    const params = new Proxy(new URLSearchParams(currentLocation.search), {
        get: (searchParams, prop) => searchParams.get(prop) || '',
    });

    useEffect(() => {
        if (params?.app == 'flow' && params.inviteCode) {
            dispatch({
                type: INVITED_CODE,
                value: params.inviteCode
            })
            setTimeout(() => window.location.href = '/#/aiflow', 200);
        }
    }, [params?.app, params?.inviteCode])

    useEffect(() => {
        // ReactGA.initialize(GOOGLE_ANALYTICS_TRACKID);
        ReactGA.send({ hitType: "pageview", page: "/invitation-landing", title: "Invitation Message Landing" });
    }, [])

    if (params?.app == 'flow') {
        return;
    }

    return <div className="fill-available full-height"
        style={{
            columnGap: '10px', backgroundColor: '#f3f3f3',
            display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center',
        }} >
        <div style={{ display: 'flex', flexDirection: 'column', width: 600, padding: 48, paddingTop: 48, rowGap: 20, backgroundColor: 'white', borderRadius: 20 }}>
            <div style={{
                fontSize: 24,
                width: '-webkit-fill-available',
                display: 'flex',
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'center'
            }}>
                {intl.formatMessage({ id: 'welcome_to_funblocks' })}
            </div>
            <div>{intl.formatMessage({ id: 'intro_extension' })}</div>
            <img
                className="highlighted-image"
                width={420}
                src={'https://www.funblocks.net/assets/img/portfolio/fullsize/extension_rw.png'}
                draggable={false}
            />
            <Button
                variant='contained'
                style={{ marginTop: 10, alignSelf: 'center', textTransform: 'none' }}
                onClick={() => {
                    if (params.inviteCode) {
                        dispatch({
                            type: INVITED_CODE,
                            value: params?.inviteCode
                        })
                    }

                    const userAgent = navigator.userAgent;
                    if (userAgent.includes("Edg")) {
                        window.open('https://microsoftedge.microsoft.com/addons/detail/funblocks-ai-your-ultim/lmmlojdklhcdiefaniakpkhhdmamnigk', '_blank')
                    } else if (userAgent.includes("Chrome")) {
                        window.open('https://chromewebstore.google.com/detail/funblocks-ai-your-ultimat/coodnehmocjfaandkbeknihiagfccoid', '_blank')
                    }
                }}
            >
                {intl.formatMessage({ id: 'download_extension_to_participate' })}
            </Button>
            <div style={{
                alignSelf: 'center'
            }}>
                {params?.freeTokens && intl.formatMessage({ id: 'free_coins_awarded' }, { coins: params?.freeTokens })}
            </div>
        </div>
    </div>

}

export default InvitationLanding