import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { fetchDeletedSlides, fetchSlides } from 'src/actions/ticketAction';
import { LIST_KEYS } from 'src/constants/actionTypes';
import { getStateByUser } from 'src/reducers/listReducer';

import CommonList from 'src/components/CommonList';
import SlidesItem from 'src/components/slides/SlidesItem';

const Slides = ({ }) => {
  const user = useSelector(state => state.loginIn.user)
  const folder = useSelector(state => state.uiState.slidesFolder) || 0;
  const refreshSlides = useSelector(state => state.uiState.refreshSlides);

  const fetcher = [fetchSlides, fetchDeletedSlides];

  const data_lists = [
    useSelector(state=>getStateByUser(state.slide_lists, user)), 
    useSelector(state=>getStateByUser(state.deleted_slide_lists, user)), 
  ];

  const pageBy = 'updatedAt';
  const [fetcherProps, setFetcherProps] = useState({
    list_key: LIST_KEYS.slides, 
    data_fetcher: fetcher[0],
  });

  useEffect(()=>{
    setFetcherProps(prevState => {
      return {
        ...prevState,
        data_fetcher: fetcher[folder],
        invalidate: false,
        fetch_params:  {
          pageBy, folder
        }
      }
    })
  }, [folder]);

  useEffect(() => {
    setFetcherProps(prevState=> {
      return {
        ...prevState,
        fetch_params: {
          pageBy, folder
        }, 
        invalidate: true
      }
    })
  }, [refreshSlides]);

  return <div style={{ display: 'flex', flexDirection: 'column', width: '100%' }}>
    <CommonList
      list_data = {data_lists[folder]}
      fetcherProps={fetcherProps}
      itemRender={(item) => <SlidesItem
        key={item._id}
        item={item} mode='ril' />}
    />
   
  </div>;
};


export default Slides
