import React, { useState, useEffect, useLayoutEffect, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link, useHistory, useLocation } from 'react-router-dom'
import {
  <PERSON><PERSON>,
  Card,
  CardContent,
  TextField,
  InputAdornment,
  IconButton
} from '@mui/material';
import { Visibility } from '@styled-icons/material/Visibility';
import { VisibilityOff } from '@styled-icons/material/VisibilityOff';

import * as TicketAction from '../../../actions/ticketAction';
import * as Validator from '../../../utils/validator';
import { get_server_host } from '../../../utils/serverAPIUtil';
import SpinnerAndToast from 'src/components/SpinnerAndToast';
import { useIntl } from 'react-intl';
import { getLocale } from 'src/utils/Intl';
import { Refresh } from '@styled-icons/material-outlined/Refresh'
import { isCNDomain } from 'src/utils/constants';

const Login = () => {

  const [account, setAccount] = useState('');
  const [accountValid, setAccountValid] = useState(true);
  const [isAccountPhone, setIsAccountPhone] = useState(false);
  const [nickname, setNickname] = useState('');
  const [nicknameValid, setNicknameValid] = useState(true);
  const [password, setPassword] = useState('');
  const [passwordValid, setPasswordValid] = useState(true);
  const [showPassword, setShowPassword] = useState(false);
  const [captcha, setCaptcha] = useState('');
  const [captchaRefresher, setCaptchaRefresher] = useState('');
  const [serverHost, setServerHost] = useState();
  const [captchaValid, setCaptchaValid] = useState(false);
  const [vcode, setVcode] = useState('');
  const [vcodeValid, setVcodeValid] = useState(true);
  const [vCodeRequested, setVCodeRequested] = useState(false);
  const [invitation, setInvitation] = useState('');
  const vcode_ref = useRef();

  const [submitting, setSubmitting] = useState(false);

  const dispatch = useDispatch();
  const history = useHistory();
  const intl = useIntl();

  const currentLocation = useLocation();

  const doResetPswd = (type) => {
    // this.setState({ commitId: 'logSign' });

    if (accountValid && vcodeValid && passwordValid && captchaValid) {
      setSubmitting(true);

      dispatch(TicketAction.resetPassword({ username: account, vcode, password, type }, passwordUpdated, path));
      refreshCaptcha();
    }
  }

  const passwordUpdated = () => {
    setSubmitting(false);
    setPassword('');
    history.push('/login')
  }

  const doRegister = () => {
    // this.setState({ commitId: 'logSign' });

    if (accountValid && vcodeValid && nicknameValid && passwordValid && (captchaValid || (invitation && invitation.invitedUserAccount == account))) {
      setSubmitting(true);

      dispatch(TicketAction.register({ username: account, vcode, nickname, password, invitationId: params.invitationId, inviteCode: inviteCode, locale: getLocale() }, logon, path));
      refreshCaptcha();
    }
  }

  const doLogin = () => {
    // this.setState({ commitId: 'logSign' });

    if (accountValid && (path === '/login' && password && passwordValid || path === '/vcodelogin' && vcode && vcodeValid)) {
      let params = {
        username: account, locale: getLocale()
      }

      if (path == '/login') {
        params.password = password;
      } else if (path == '/vcodelogin') {
        params.vcode = vcode;
        params.mode = 'temp_token';
      }
      dispatch(TicketAction.login(params, logon, null, 'login'));
    }
  }

  const logon = (user) => {
    if (user) {
      if (user.activated) {
        if (currentLocation.pathname.includes('/login') || currentLocation.pathname.includes('/register')) {
          currentLocation.pathname = '/'
        }
        // else {
        //   currentLocation.pathname = '/login'
        // }
        history.push(currentLocation);
      } else {
        history.push("/activate")
      }
    }

    setSubmitting(false);
  }

  useLayoutEffect(() => {
    get_server_host().then((url) => setServerHost(url))
  }, [])

  let { pathname, search } = useLocation();

  const params = new Proxy(new URLSearchParams(search), {
    get: (searchParams, prop) => searchParams.get(prop) || '',
  });

  const [inviteCode, setInviteCode] = useState(params.inviteCode);

  useEffect(() => {
    if (params.invitationId) {
      dispatch(TicketAction.getInvitation({ invitationId: params.invitationId }, invitation => {
        setInvitation(invitation);
        setAccount(invitation.invitedUserAccount);
        if (!invitation.userActivated) {
          history.push({ pathname: '/register', search });
        }
      }, 'register'));
    }
  }, [params.invitationId]);


  const pages = [
    {
      path: '/login',
      action: 'login',
      title: intl.formatMessage({ id: 'login' }),
      btnLabel: intl.formatMessage({ id: 'login' }),
      btnAction: doLogin,
      formInfoText: intl.formatMessage({ id: "login_signin_form_info" })
    },
    {
      path: '/vcodelogin',
      action: 'login',
      title: intl.formatMessage({ id: 'vcode_login' }),
      btnLabel: intl.formatMessage({ id: 'login' }),
      btnAction: doLogin,
      formInfoText: intl.formatMessage({ id: "login_signin_vcode_form_info" })
    },
    {
      path: '/forget',
      action: 'forget',
      title: intl.formatMessage({ id: 'resetpswd' }),
      btnLabel: intl.formatMessage({ id: 'resetpswd' }),
      btnAction: () => doResetPswd('forget'),
      formInfoText: intl.formatMessage({ id: "login_resetpswd_form_info" })
    },
    {
      path: '/register',
      action: 'register',
      title: intl.formatMessage({ id: 'signup' }),
      btnLabel: intl.formatMessage({ id: 'signup' }),
      btnAction: doRegister,
      formInfoText: intl.formatMessage({ id: "login_signup_form_info" })
    }
  ];
  const { title, btnLabel, formInfoText, btnAction, path, action } = pages.find(ele => ele.path === pathname) || pages[0];

  const AccountInput = {
    Phone: {
      placeholder: 'phone',
      validator: Validator.validatePhone
    },
    Email: {
      placeholder: 'email',
      validator: Validator.validateEmail
    },
    PhoneOrMail: {
      placeholder: 'phone_or_email',
      validator: (account) => Validator.validatePhone(account) || Validator.validateEmail(account)
    }
  }
  const AccountMode = path === '/vcodelogin' || !isCNDomain() ? 'Email' : 'PhoneOrMail';

  const onAccountBlur = (e) => {
    let value = e.target.value ? e.target.value.trim() : '';
    setAccount(value);
    setAccountValid(AccountInput[AccountMode].validator(value));
    setIsAccountPhone(AccountInput[AccountMode].validator(value) && Validator.validatePhone(value));
  }

  const [vCoderTimer, setVCoderTimer] = useState(0);
  const vCoderTimerCounting = vCoderTimer < 120 && vCoderTimer > 0;
  const sendVerificationCode = () => {
    // this.setState({ commitId: 'sendVCode' });

    if (accountValid && captchaValid) {
      setSubmitting(true);
      dispatch(TicketAction.sendVerificationCode({ username: account, type: btnLabel.toLowerCase(), type: action }, vCodeSent, path));
    }
  }

  let clockCall;
  const vCodeSent = () => {
    setSubmitting(false);
    setVCodeRequested(true);
    vcode_ref.current.focus();

    setVCoderTimer(120);
    clockCall = setInterval(() => {
      decrementClock();
    }, 1000);

  }

  const decrementClock = () => {
    setVCoderTimer(vCoderTimer => {
      if (vCoderTimer === 0) {
        clearInterval(clockCall);
      }

      return vCoderTimer - 1
    });
  };

  // showAccountInactiveModal = (isVisible) => {
  //     this.setState({ isAccountInactiveModalVisible: isVisible });
  // }

  // resendActivationEmail = () => {
  //     const { dispatch, navigation } = this.props;
  //     const { account } = this.state;

  //     this.setState({
  //         commitId: 'sendActivationEmail'
  //     })
  //     dispatch(TicketAction.resendActivationEmail({ email: account }, () => this.setState({ commitId: null, activationEmailResent: true }), navigation.state.key));
  // };

  const refreshCaptcha = () => {
    setCaptchaRefresher(Math.random());
  }

  const onCaptchaChange = (captcha) => {
    setCaptcha(captcha);

    if (!captcha || !captcha.trim() || captcha.trim().length != 4) {
      return setCaptchaValid(false);
    }

    // this.setState({ commitId: 'verifyingCaptcha' })
    dispatch(TicketAction.verifyCaptcha({ captcha: captcha.trim() }, ({ verified }) => setCaptchaValid(verified), path));
  }

  const captchaUri = `${serverHost}users/captcha?${captchaRefresher}`;

  return (
    <div className='full-height' style={{ display: 'flex', width: '100%', alignItems: 'center', justifyContent: 'center', background: '#f5f5f5' }}>
      <Card style={{ display: 'flex', width: 'fit-content', maxWidth: '800px', minWidth: '420px', height: 'fit-content', flexDirection: 'row', alignItems: 'center', backgroundColor: '#321fdb', alignSelf: 'center', }}>
        <Card style={{ width: '-webkit-fill-available' }}>
          <CardContent style={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
            <h1>{title}</h1>
            <p className="text-medium-emphasis">{formInfoText}</p>

            <div style={{ display: 'flex', flexDirection: 'column', justifyContent: 'space-around', flex: 1 }}>
              <TextField
                value={account}
                label={intl.formatMessage({ id: AccountInput[AccountMode].placeholder })}
                style={{
                  margin: "10px 0px",
                }}
                error={!accountValid}
                autoComplete="username"
                required={true}
                onChange={(e) => {
                  setAccount(e.target.value ? e.target.value.trim() : '');
                }}
                onBlur={onAccountBlur}
              />

              {
                path === '/register' &&
                <TextField
                  value={nickname}
                  label={intl.formatMessage({ id: 'nickname' })}
                  style={{
                    margin: "10px 0px",
                  }}
                  error={!nicknameValid}
                  autoComplete="off"
                  required={true}
                  onChange={(e) => {
                    setNickname(e.target.value);
                  }}
                  onBlur={(e) => setNicknameValid(e.target.value && e.target.value.length >= 4 && e.target.value.length <= 20)}
                />
              }

              {
                path !== '/login' && (path !== '/register' || !invitation || invitation.invitedUserAccount != account) &&

                <TextField
                  value={captcha}
                  label={intl.formatMessage({ id: 'captcha' })}
                  style={{
                    margin: "10px 0px",
                  }}
                  required={true}
                  autoComplete='off'
                  placeholder={intl.formatMessage({ id: 'captcha' })}
                  helperText={captchaValid ? '' : intl.formatMessage({ id: 'vcode_err' })}
                  error={!captchaValid}
                  InputProps={{
                    startAdornment: <InputAdornment
                      position="start"
                    >
                      <div
                        style={{ cursor: 'pointer', display: 'flex', flexDirection: 'row', alignItems: 'center' }}
                        title="click to refresh"
                        onClick={refreshCaptcha}
                        onMouseDown={(event) => event.preventDefault()}
                        edge="end"
                      >
                        <img
                          src={captchaUri}
                        />
                        <Refresh size={28} />
                      </div>

                    </InputAdornment>
                  }}
                  onChange={(e) => {
                    onCaptchaChange(e.target.value);
                  }}
                />
              }

              {
                path !== '/login' &&
                !(invitation && invitation.invitedUserAccount == account) &&
                !(path === '/register' && !isAccountPhone) &&
                <TextField
                  value={vcode}
                  label={intl.formatMessage({ id: 'verification_code' })}
                  style={{
                    margin: "10px 0px",
                  }}
                  disabled={!vCodeRequested}
                  ref={vcode_ref}
                  required={true}
                  InputProps={{
                    endAdornment: <InputAdornment
                      position="end"
                    >
                      <Button
                        onClick={sendVerificationCode}
                        onMouseDown={(event) => event.preventDefault()}
                        disabled={vCoderTimerCounting || !captchaValid || submitting}

                        edge="start"
                        variant="contained"
                      >
                        {vCoderTimerCounting ? vCoderTimer + 's' : intl.formatMessage({ id: 'getvcode' })}
                      </Button>

                    </InputAdornment>
                  }}
                  onChange={(e) => {
                    setVcode(e.target.value);
                  }}
                  onBlur={(e) => setVcodeValid(e.target.value && e.target.value.length >= 4 && e.target.value.length <= 6)}
                  error={!vcodeValid}
                />
              }
              {
                path != '/vcodelogin' &&
                <TextField
                  type={showPassword ? 'text' : 'password'}
                  label={intl.formatMessage({ id: 'password' })}
                  style={{
                    margin: "10px 0px",
                  }}
                  value={password}
                  autoComplete="current-password"
                  required={true}
                  onChange={(e) => setPassword(e.target.value)}
                  onBlur={(e) => setPasswordValid(e.target.value && e.target.value.length >= 6 && e.target.value.length <= 20)}
                  error={!passwordValid}
                  InputProps={{
                    endAdornment: <InputAdornment
                      style={{ paddingRight: '6px' }}
                      position="end"
                    >
                      <IconButton
                        aria-label="toggle password visibility"
                        onClick={() => setShowPassword(!showPassword)}
                        onMouseDown={(event) => event.preventDefault()}
                        edge="end"
                      >
                        {showPassword ? <VisibilityOff style={{ width: '24px', height: '24px' }} /> : <Visibility style={{ width: '24px', height: '24px' }} />}
                      </IconButton>
                    </InputAdornment>
                  }}
                />
              }
              {
                path == '/register' && !!inviteCode &&
                <TextField
                  value={inviteCode}
                  label={intl.formatMessage({ id: 'invite_code' })}
                  style={{
                    margin: "10px 0px",
                  }}
                  disabled={!!params.inviteCode}
                  required={false}
                  onChange={(e) => {
                    setInviteCode(e.target.value);
                  }}
                />
              }
              <div style={{ display: 'flex', flexDirection: 'row', justifyContent: 'space-between' }}>
                <div style={{ display: 'flex', flexDirection: 'row' }}>
                  <Button variant="contained"
                    onClick={() => {
                      btnAction();
                    }}
                    disabled={submitting}
                  >
                    {btnLabel}
                  </Button>
                  <Button variant="text"
                    onClick={() => {
                      history.push({ pathname: path != '/vcodelogin' ? '/vcodelogin' : '/login', search });
                    }}
                    disabled={submitting}
                  >
                    {intl.formatMessage({ id: path != '/vcodelogin' ? 'vcode_login' : 'password_login' })}
                  </Button>
                </div>
                {
                  path === '/login' &&
                  <Button variant="text" onClick={() => history.push({ pathname: '/forget', search })}>
                    {intl.formatMessage({ id: 'forgotpswd' })}
                  </Button>
                }
                {
                  path === '/register' &&
                  <Button variant="text" onClick={() => history.push({ pathname: '/login', search })}>
                    {intl.formatMessage({ id: 'hadaccount' })}
                  </Button>
                }
                {
                  path === '/forget' &&
                  <Button variant="text" onClick={() => history.push({ pathname: '/login', search })}>
                    {intl.formatMessage({ id: 'login' })}
                  </Button>
                }
              </div>
            </div>
          </CardContent>
        </Card>
        {
          (path === '/login' || path === '/forget') &&
          <div style={{ flexFlow: 1, height: '100%', color: 'white', display: 'flex', flexDirection: 'column', alignSelf: 'center', justifyContent: 'center' }}>
            <CardContent style={{ height: '100%', alignItems: 'center', display: 'flex', flexDirection: 'column', alignSelf: 'center', justifyContent: 'center', rowGap: '10px' }}>
              <h2>{intl.formatMessage({ id: 'signup' })}</h2>
              <p>
                {intl.formatMessage({ id: 'signup_info' })}
              </p>
              <Button
                variant="contained"
                tabIndex={-1}
                onClick={() => history.push('/register?' + (invitation ? 'invitationId=' + invitation._id : ''))}
              >
                {intl.formatMessage({ id: 'register_now' })}
              </Button>
            </CardContent>
          </div>
        }

      </Card>
      <SpinnerAndToast
        statusCallbacker={setSubmitting}
      />
    </div>
  )
}

export default Login
