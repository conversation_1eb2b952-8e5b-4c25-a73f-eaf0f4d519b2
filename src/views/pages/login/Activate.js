import React, { useState, useEffect, useLayoutEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link, useHistory, useLocation } from 'react-router-dom'
import { useIntl } from 'react-intl';

import Typography from '@mui/material/Typography';
import { <PERSON><PERSON>, Card } from '@mui/material';

import * as TicketAction from '../../../actions/ticketAction';
import SpinnerAndToast from 'src/components/SpinnerAndToast';
import LoadingButton from '@mui/lab/LoadingButton/LoadingButton';
import ReactGA from 'react-ga4';

const Activate = () => {
  const intl = useIntl();
  const dispatch = useDispatch();
  const history = useHistory();
  const [loading, setLoading] = useState();

  const user = useSelector(state => state.loginIn.user);

  useEffect(() => {
    if (user.activated) {
      history.push('/home')
    }
  }, [user.activated]);


  const resendActivationEmail = () => {
    setLoading(true);
    dispatch(TicketAction.resendActivationEmail({ email: user.username }, () => {
      setLoading(false);
    }, 'activate'));
  }

  const exitActivation = () => {
    dispatch(TicketAction.logout_of_app())
    setTimeout(() => {
      history.push("/register")
    }, 200)
  }

  const refreshUserInfo = () => {
    dispatch(TicketAction.getUserInfo(null, 'activate'));
  }

  const relogin = () => {
    history.push('/login');
  }

  useEffect(() => {
    // ReactGA.initialize(GOOGLE_ANALYTICS_TRACKID);
    ReactGA.send({ hitType: "pageview", page: "/activate", title: "Activate" });
}, [])

  return (
    <div className='full-height' style={{ display: 'flex', flexDirection: 'column', width: '100%', alignItems: 'center', justifyContent: 'center', background: '#f5f5f5' }}>
      <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', }}>
        <Card style={{ backgroundColor: 'white', padding: 20, alignItems: 'center', alignContent: 'center', display: 'flex', flexDirection: 'column' }} sx={{ width: 420 }}>
          <Typography gutterBottom variant="text" component="div">

            {intl.formatMessage({ id: 'account_not_activated' }, { email: user.username })}
          </Typography>

        </Card>

        <div style={{ backgroundColor: 'transparent', flexDirection: 'column', padding: 20, rowGap: 20, alignItems: 'center', alignContent: 'center', display: 'flex' }} sx={{ width: 420 }}>
          <div style={{display: 'flex', flexDirection: 'row', alignItems: 'center', columnGap: '10px'}}>
            <Typography gutterBottom variant="text" component="div">
              {intl.formatMessage({ id: 'already_activated' })}
            </Typography>
            <Button variant="contained" size="large" onClick={relogin}>{intl.formatMessage({ id: 'login' })}</Button>
          </div>

          <div style={{display: 'flex', flexDirection: 'row', alignItems: 'center', columnGap: '10px'}}>
            <Typography gutterBottom variant="text" component="div">
              {intl.formatMessage({ id: 'no_activation_email' })}
            </Typography>
            <LoadingButton variant="outlined" size="large" disabled={loading} loading={loading} onClick={resendActivationEmail}>{intl.formatMessage({ id: 'resend_activation_email' })}</LoadingButton>
          </div>

          <div style={{display: 'flex', flexDirection: 'row', alignItems: 'center', columnGap: '10px'}}>
            <Typography gutterBottom variant="text" component="div">
              {intl.formatMessage({ id: 'still_no_activation_email' })}
            </Typography>
            <LoadingButton variant="outlined" size="large" disabled={loading} loading={loading} onClick={exitActivation}>{intl.formatMessage({ id: 'try_another_way' })}</LoadingButton>
          </div>
        </div>
        <SpinnerAndToast />
      </div>
    </div>
  )
}

export default Activate
