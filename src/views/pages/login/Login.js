import React, { useState, useEffect, useLayoutEffect, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useHistory, useLocation } from 'react-router-dom'
import {
  Button,
  Card,
  CardContent,
  TextField,
  InputAdornment,
  IconButton
} from '@mui/material';
import { Visibility } from '@styled-icons/material/Visibility';
import { VisibilityOff } from '@styled-icons/material/VisibilityOff';

import * as TicketAction from '../../../actions/ticketAction';
import * as Validator from '../../../utils/validator';
import { get_server_host } from '../../../utils/serverAPIUtil';
import SpinnerAndToast from 'src/components/SpinnerAndToast';
import { useIntl } from 'react-intl';
import { getLocale } from 'src/utils/Intl';
import { Refresh } from '@styled-icons/material-outlined/Refresh'
import { isCNDomain } from 'src/utils/constants';

import { Email } from '@styled-icons/material-outlined/Email';
import { Password } from '@styled-icons/material/Password'
import LoadingScreen from '@/components/LoadingScreen';
import { useMediaQuery } from 'react-responsive';
import { GoogleLogin } from './GoogleLogin';
import ReactGA from "react-ga4";
import { MOBILE_MEDIA_QUERY } from '../../../utils/constants';

const Login = () => {
  const loginUser = useSelector(state => state.loginIn.user);
  const [account, setAccount] = useState('');
  const [accountValid, setAccountValid] = useState(true);
  const [isAccountPhone, setIsAccountPhone] = useState(false);
  const [nickname, setNickname] = useState('');
  const [nicknameValid, setNicknameValid] = useState(true);
  const [password, setPassword] = useState('');
  const [passwordValid, setPasswordValid] = useState(true);
  const [showPassword, setShowPassword] = useState(false);
  const [captcha, setCaptcha] = useState('');
  const [captchaRefresher, setCaptchaRefresher] = useState('');
  const [serverHost, setServerHost] = useState();
  const [captchaValid, setCaptchaValid] = useState(false);
  const [vcode, setVcode] = useState('');
  const [vcodeValid, setVcodeValid] = useState(true);
  const [vCodeRequested, setVCodeRequested] = useState(false);
  const [invitation, setInvitation] = useState('');
  const vcode_ref = useRef();

  const [submitting, setSubmitting] = useState(false);
  const [googleLoging, setGoogleLoging] = useState(false);
  const [loginMethod, setLoginMethod] = useState();
  const dispatch = useDispatch();
  const history = useHistory();
  const intl = useIntl();

  const currentLocation = useLocation();
  const params = new Proxy(new URLSearchParams(currentLocation.search), {
    get: (searchParams, prop) => searchParams.get(prop),
  });

  const isMobile = useMediaQuery(MOBILE_MEDIA_QUERY)

  const doResetPswd = (type) => {
    if (accountValid && vcodeValid && passwordValid && captchaValid) {
      setSubmitting(true);
      dispatch(TicketAction.resetPassword({ username: account, vcode, password, type }, passwordUpdated, path));
      refreshCaptcha();
    }
  }

  const passwordUpdated = () => {
    setSubmitting(false);
    setPassword('');
    history.push('/login')
  }

  const doRegister = () => {
    if (accountValid && vcodeValid && nicknameValid && passwordValid && (captchaValid || (invitation && invitation.invitedUserAccount == account))) {
      setSubmitting(true);
      dispatch(TicketAction.register({ username: account, vcode, nickname, password, invitationId: params.invitationId, inviteCode: inviteCode, aid, locale: getLocale() }, logon, path));
      refreshCaptcha();
    }
  }

  const doLogin = () => {
    if (accountValid && (path === '/login' && password && passwordValid || path === '/vcodelogin' && vcode && vcodeValid)) {
      let params = {
        username: account,
        locale: getLocale(),
        inviteCode,
        aid
      }

      if (path == '/login') {
        params.password = password;
      } else if (path == '/vcodelogin') {
        params.vcode = vcode;
        params.mode = 'temp_token';
      }
      dispatch(TicketAction.login(params, logon, null, 'login'));
    }
  }

  const logon = (user) => {
    if (user?._id) {
      if (user.activated) {
        if (params?.source == 'extension') {
          window.location.href = '/#/invitation-event?greetings=logon&source=' + params?.source;
          return
        } else if (params?.source == 'flow' || isMobile) {
          window.location.href = '/#/aiflow?source=' + params?.source;
          return
        }

        if (currentLocation.pathname.includes('/login') || currentLocation.pathname.includes('/register')) {
          currentLocation.pathname = '/'
        }
        history.push(currentLocation);
      } else {
        history.push("/activate")
      }
    }

    setSubmitting(false);
    setGoogleLoging(false);
  }

  useEffect(() => {
    if (loginUser) logon(loginUser);
  }, [loginUser])

  useEffect(() => {
    dispatch(TicketAction.getUserInfo());
  }, [])

  useLayoutEffect(() => {
    get_server_host().then((url) => setServerHost(url))
  }, [])

  useEffect(() => {
    ReactGA.send({ hitType: "pageview", page: "/login", title: "Login" });
  }, [])

  let { pathname, search } = useLocation();

  const invited_code = useSelector(state => state.uiState.invited_code)
  const [inviteCode, setInviteCode] = useState(params.inviteCode || invited_code);
  const [aid, setAid] = useState(params.aid);

  useEffect(() => {
    if (params.invitationId) {
      dispatch(TicketAction.getInvitation({ invitationId: params.invitationId }, invitation => {
        setInvitation(invitation);
        setAccount(invitation.invitedUserAccount);
        if (!invitation.userActivated) {
          history.push({ pathname: '/register', search });
        }
      }, 'register'));
    }
  }, [params.invitationId]);

  useEffect(() => {
    if (params.g_login_token) {
      setGoogleLoging(true);
      dispatch(TicketAction.oauth_sign_in_credential({ credential: params.g_login_token, inviteCode, aid }, logon))
      return;
    }
  }, [params.g_login_token])

  const pages = [
    {
      path: '/login',
      action: 'login',
      title: !loginMethod ? intl.formatMessage({ id: 'sign_in' }) : intl.formatMessage({ id: 'sign_in_with' }, { method: intl.formatMessage({ id: 'password_account' }) }),
      btnLabel: intl.formatMessage({ id: 'login' }),
      btnAction: doLogin,
      formInfoText: intl.formatMessage({ id: "login_signin_form_info" })
    },
    {
      path: '/vcodelogin',
      action: 'login',
      title: intl.formatMessage({ id: 'sign_in_with_email_vcode' }),
      btnLabel: intl.formatMessage({ id: 'login' }),
      btnAction: doLogin,
      formInfoText: intl.formatMessage({ id: "login_signin_vcode_form_info" })
    },
    {
      path: '/forget',
      action: 'forget',
      title: intl.formatMessage({ id: 'resetpswd' }),
      btnLabel: intl.formatMessage({ id: 'resetpswd' }),
      btnAction: () => doResetPswd('forget'),
      formInfoText: intl.formatMessage({ id: "login_resetpswd_form_info" })
    },
    {
      path: '/register',
      action: 'register',
      title: intl.formatMessage({ id: 'signup' }),
      btnLabel: intl.formatMessage({ id: 'signup' }),
      btnAction: doRegister,
      formInfoText: intl.formatMessage({ id: "login_signup_form_info" })
    }
  ];
  const { title, btnLabel, formInfoText, btnAction, path, action } = pages.find(ele => ele.path === pathname) || pages[0];

  const AccountInput = {
    Phone: {
      placeholder: 'phone',
      validator: Validator.validatePhone
    },
    Email: {
      placeholder: 'email',
      validator: Validator.validateEmail
    },
    PhoneOrMail: {
      placeholder: 'phone_or_email',
      validator: (account) => Validator.validatePhone(account) || Validator.validateEmail(account)
    }
  }
  const AccountMode = path === '/vcodelogin' || !isCNDomain() ? 'Email' : 'PhoneOrMail';

  const onAccountBlur = (e) => {
    let value = e.target.value ? e.target.value.trim() : '';
    setAccount(value);
    setAccountValid(AccountInput[AccountMode].validator(value));
    setIsAccountPhone(AccountInput[AccountMode].validator(value) && Validator.validatePhone(value));
  }

  const [vCoderTimer, setVCoderTimer] = useState(0);
  const vCoderTimerCounting = vCoderTimer < 120 && vCoderTimer > 0;
  const sendVerificationCode = () => {
    if (accountValid && captchaValid) {
      setSubmitting(true);
      dispatch(TicketAction.sendVerificationCode({ username: account, type: btnLabel.toLowerCase(), type: action }, vCodeSent, path));
    }
  }

  let clockCall;
  const vCodeSent = () => {
    setSubmitting(false);
    setVCodeRequested(true);
    vcode_ref.current.focus();

    setVCoderTimer(120);
    clockCall = setInterval(() => {
      decrementClock();
    }, 1000);
  }

  const decrementClock = () => {
    setVCoderTimer(vCoderTimer => {
      if (vCoderTimer === 0) {
        clearInterval(clockCall);
      }
      return vCoderTimer - 1
    });
  };

  const refreshCaptcha = () => {
    setCaptchaRefresher(Math.random());
  }

  const onCaptchaChange = (captcha) => {
    setCaptcha(captcha);

    if (!captcha || !captcha.trim() || captcha.trim().length != 4) {
      return setCaptchaValid(false);
    }

    dispatch(TicketAction.verifyCaptcha({ captcha: captcha.trim() }, ({ verified }) => setCaptchaValid(verified), path));
  }

  const captchaUri = `${serverHost}users/captcha?${captchaRefresher}`;

  return (
    <div className='full-height' style={{
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      padding: 20,
      minHeight: '100vh'
    }}>
      {!googleLoging && (
        <Card style={{
          maxWidth: '420px',
          width: '100%',
          borderRadius: '16px',
          boxShadow: '0 20px 40px rgba(0,0,0,0.1)',
          border: 'none',
          overflow: 'hidden'
        }}>
          <CardContent style={{
            padding: '48px 40px',
            paddingBottom: '20px',
            display: 'flex',
            flexDirection: 'column',
            background: 'white'
          }}>
            {/* Header */}
            <div style={{
              textAlign: 'center',
              marginBottom: 32
            }}>
              <h1 style={{
                fontSize: '28px',
                fontWeight: '600',
                color: '#1a1a1a',
                margin: 0,
                marginBottom: 8
              }}>
                {title}
              </h1>
              {
                path === '/login' &&
                <p style={{
                  color: '#666',
                  fontSize: '16px',
                  margin: 0
                }}>
                  Welcome back! Please sign in to continue
                </p>
              }
            </div>

            {/* Login Method Selection */}
            {!loginMethod && (
              <div style={{
                display: 'flex',
                flexDirection: 'column',
                gap: 16,
                marginBottom: 24
              }}>
                {!(isCNDomain() || params.g_login_token) && (
                  <GoogleLogin logon={logon} setGoogleLoging={setGoogleLoging} showButton={true} inviteCode={inviteCode} aid={aid} />
                )}

                <button
                  type="button"
                  className='modern-login-button'
                  onClick={() => {
                    setLoginMethod('verification_code');
                    history.push({ pathname: '/vcodelogin', search });
                  }}
                >
                  <Email size={20} /> {intl.formatMessage({ id: 'sign_in_with_email_vcode' })}
                </button>

                <button
                  type="button"
                  className='modern-login-button'
                  onClick={() => {
                    setLoginMethod('password');
                  }}
                >
                  <Password size={20} /> {intl.formatMessage({ id: 'sign_in_with_password' })}
                </button>

                <div style={{
                  textAlign: 'center',
                  marginTop: 24,
                  paddingTop: 24,
                  borderTop: '1px solid #e5e7eb'
                }}>
                  <a
                    href='https://funblocks.net/privacypolicy_en.html'
                    target='_blank'
                    rel="noopener noreferrer"
                    style={{
                      color: '#9ca3af',
                      fontSize: '14px',
                      textDecoration: 'none',
                      transition: 'color 0.2s ease'
                    }}
                    onMouseEnter={(e) => e.target.style.color = '#667eea'}
                    onMouseLeave={(e) => e.target.style.color = '#9ca3af'}
                  >
                    Privacy Policy
                  </a>
                </div>
              </div>
            )}

            {/* Login Form */}
            {['password', 'verification_code'].includes(loginMethod) && (
              <div style={{ marginTop: 8 }}>
                <p style={{
                  color: '#666',
                  fontSize: '14px',
                  marginBottom: 24,
                  textAlign: 'center'
                }}>
                  {formInfoText}
                </p>

                <div style={{
                  display: 'flex',
                  flexDirection: 'column',
                  gap: 20
                }}>
                  {/* Account Input */}
                  <TextField
                    value={account}
                    label={intl.formatMessage({ id: AccountInput[AccountMode].placeholder })}
                    variant="outlined"
                    fullWidth
                    error={!accountValid}
                    autoComplete="username"
                    required={true}
                    onChange={(e) => {
                      setAccount(e.target.value ? e.target.value.trim() : '');
                    }}
                    onBlur={onAccountBlur}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: '12px',
                        '&:hover fieldset': {
                          borderColor: '#667eea',
                        },
                        '&.Mui-focused fieldset': {
                          borderColor: '#667eea',
                        },
                      },
                      '& .MuiInputLabel-root.Mui-focused': {
                        color: '#667eea',
                      },
                    }}
                  />

                  {/* Nickname Input for Registration */}
                  {path === '/register' && (
                    <TextField
                      value={nickname}
                      label={intl.formatMessage({ id: 'nickname' })}
                      variant="outlined"
                      fullWidth
                      error={!nicknameValid}
                      autoComplete="off"
                      required={true}
                      onChange={(e) => {
                        setNickname(e.target.value);
                      }}
                      onBlur={(e) => setNicknameValid(e.target.value && e.target.value.length >= 4 && e.target.value.length <= 20)}
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          borderRadius: '12px',
                          '&:hover fieldset': {
                            borderColor: '#667eea',
                          },
                          '&.Mui-focused fieldset': {
                            borderColor: '#667eea',
                          },
                        },
                        '& .MuiInputLabel-root.Mui-focused': {
                          color: '#667eea',
                        },
                      }}
                    />
                  )}

                  {/* Captcha Input */}
                  {path !== '/login' && (path !== '/register' || !invitation || invitation.invitedUserAccount != account) && (
                    <TextField
                      value={captcha}
                      label={intl.formatMessage({ id: 'captcha' })}
                      variant="outlined"
                      fullWidth
                      required={true}
                      autoComplete='off'
                      placeholder={intl.formatMessage({ id: 'captcha' })}
                      helperText={captchaValid ? '' : intl.formatMessage({ id: 'vcode_err' })}
                      error={!captchaValid}
                      InputProps={{
                        startAdornment: <InputAdornment position="start">
                          <div
                            style={{
                              cursor: 'pointer',
                              display: 'flex',
                              alignItems: 'center',
                              gap: 8,
                              padding: '4px 8px',
                              borderRadius: '8px',
                              border: '1px solid #e1e5e9',
                              background: '#f8f9fa'
                            }}
                            title="Click to refresh captcha"
                            onClick={refreshCaptcha}
                            onMouseDown={(event) => event.preventDefault()}
                          >
                            <img
                              src={captchaUri}
                              style={{
                                height: '32px',
                                borderRadius: '4px'
                              }}
                              alt="Captcha"
                            />
                            <Refresh size={20} style={{ color: '#667eea' }} />
                          </div>
                        </InputAdornment>
                      }}
                      onChange={(e) => {
                        onCaptchaChange(e.target.value);
                      }}
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          borderRadius: '12px',
                          '&:hover fieldset': {
                            borderColor: '#667eea',
                          },
                          '&.Mui-focused fieldset': {
                            borderColor: '#667eea',
                          },
                        },
                        '& .MuiInputLabel-root.Mui-focused': {
                          color: '#667eea',
                        },
                      }}
                    />
                  )}

                  {/* Verification Code Input */}
                  {path !== '/login' && !(invitation && invitation.invitedUserAccount == account) && !(path === '/register' && !isAccountPhone) && (
                    <TextField
                      value={vcode}
                      label={intl.formatMessage({ id: 'verification_code' })}
                      variant="outlined"
                      fullWidth
                      disabled={!vCodeRequested}
                      ref={vcode_ref}
                      required={true}
                      InputProps={{
                        endAdornment: <InputAdornment position="end">
                          <Button
                            onClick={sendVerificationCode}
                            onMouseDown={(event) => event.preventDefault()}
                            disabled={vCoderTimerCounting || !captchaValid || submitting}
                            variant="contained"
                            size="small"
                            sx={{
                              borderRadius: '8px',
                              textTransform: 'none',
                              fontWeight: '500',
                              background: vCoderTimerCounting ? '#f3f4f6' : '#667eea',
                              '&:hover': {
                                background: vCoderTimerCounting ? '#f3f4f6' : '#5a67d8',
                              },
                              '&:disabled': {
                                background: '#f3f4f6',
                                color: '#9ca3af'
                              }
                            }}
                          >
                            {vCoderTimerCounting ? `${vCoderTimer}s` : intl.formatMessage({ id: 'getvcode' })}
                          </Button>
                        </InputAdornment>
                      }}
                      onChange={(e) => {
                        setVcode(e.target.value);
                      }}
                      onBlur={(e) => setVcodeValid(e.target.value && e.target.value.length >= 4 && e.target.value.length <= 6)}
                      error={!vcodeValid}
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          borderRadius: '12px',
                          '&:hover fieldset': {
                            borderColor: '#667eea',
                          },
                          '&.Mui-focused fieldset': {
                            borderColor: '#667eea',
                          },
                        },
                        '& .MuiInputLabel-root.Mui-focused': {
                          color: '#667eea',
                        },
                      }}
                    />
                  )}

                  {/* Password Input */}
                  {path != '/vcodelogin' && (
                    <TextField
                      type={showPassword ? 'text' : 'password'}
                      label={intl.formatMessage({ id: 'password' })}
                      variant="outlined"
                      fullWidth
                      value={password}
                      autoComplete="current-password"
                      required={true}
                      onChange={(e) => setPassword(e.target.value)}
                      onBlur={(e) => setPasswordValid(e.target.value && e.target.value.length >= 6 && e.target.value.length <= 20)}
                      error={!passwordValid}
                      InputProps={{
                        endAdornment: <InputAdornment position="end">
                          <IconButton
                            aria-label="toggle password visibility"
                            onClick={() => setShowPassword(!showPassword)}
                            onMouseDown={(event) => event.preventDefault()}
                            edge="end"
                            sx={{
                              color: '#667eea',
                              '&:hover': {
                                background: 'rgba(102, 126, 234, 0.1)'
                              }
                            }}
                          >
                            {showPassword ?
                              <VisibilityOff style={{ width: '20px', height: '20px' }} /> :
                              <Visibility style={{ width: '20px', height: '20px' }} />
                            }
                          </IconButton>
                        </InputAdornment>
                      }}
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          borderRadius: '12px',
                          '&:hover fieldset': {
                            borderColor: '#667eea',
                          },
                          '&.Mui-focused fieldset': {
                            borderColor: '#667eea',
                          },
                        },
                        '& .MuiInputLabel-root.Mui-focused': {
                          color: '#667eea',
                        },
                      }}
                    />
                  )}

                  {/* Invite Code Input */}
                  {path == '/register' && !!inviteCode && (
                    <TextField
                      value={inviteCode}
                      label={intl.formatMessage({ id: 'invite_code' })}
                      variant="outlined"
                      fullWidth
                      disabled={!!params.inviteCode}
                      required={false}
                      onChange={(e) => {
                        setInviteCode(e.target.value);
                      }}
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          borderRadius: '12px',
                          '&:hover fieldset': {
                            borderColor: '#667eea',
                          },
                          '&.Mui-focused fieldset': {
                            borderColor: '#667eea',
                          },
                        },
                        '& .MuiInputLabel-root.Mui-focused': {
                          color: '#667eea',
                        },
                      }}
                    />
                  )}

                  {/* Action Buttons */}
                  <div style={{
                    display: 'flex',
                    flexDirection: 'column',
                    gap: 16,
                    marginTop: 32
                  }}>
                    <Button
                      variant="contained"
                      fullWidth
                      onClick={() => {
                        btnAction();
                      }}
                      disabled={submitting}
                      sx={{
                        height: '48px',
                        borderRadius: '12px',
                        textTransform: 'none',
                        fontSize: '16px',
                        fontWeight: '600',
                        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                        boxShadow: '0 4px 12px rgba(102, 126, 234, 0.3)',
                        '&:hover': {
                          background: 'linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%)',
                          boxShadow: '0 6px 16px rgba(102, 126, 234, 0.4)',
                        },
                        '&:disabled': {
                          background: '#e5e7eb',
                          color: '#9ca3af',
                          boxShadow: 'none'
                        }
                      }}
                    >
                      {btnLabel}
                    </Button>

                    <div style={{
                      display: 'flex',
                      flexDirection: isMobile ? 'column' : 'row',
                      gap: 12,
                      justifyContent: 'center',
                      flexWrap: 'wrap'
                    }}>
                      <Button
                        variant="text"
                        onClick={() => {
                          setLoginMethod(null);
                          history.push({ pathname: '/login', search });
                        }}
                        disabled={submitting}
                        sx={{
                          textTransform: 'none',
                          color: '#667eea',
                          fontWeight: '500',
                          '&:hover': {
                            background: 'rgba(102, 126, 234, 0.1)'
                          }
                        }}
                      >
                        {intl.formatMessage({ id: 'choose_login_method' })}
                      </Button>

                      {path === '/login' && (
                        <>
                          <Button
                            variant="text"
                            onClick={() => history.push({ pathname: '/forget', search })}
                            sx={{
                              textTransform: 'none',
                              color: '#667eea',
                              fontWeight: '500',
                              '&:hover': {
                                background: 'rgba(102, 126, 234, 0.1)'
                              }
                            }}
                          >
                            {intl.formatMessage({ id: 'forgotpswd' })}
                          </Button>
                          <Button
                            variant="text"
                            onClick={() => {
                              let newSearchParams = new URLSearchParams(search);
                              if (invitation) {
                                newSearchParams.set('invitationId', invitation._id);
                              }
                              history.push({ pathname: '/register', search: newSearchParams.toString() });
                            }}
                            sx={{
                              textTransform: 'none',
                              color: '#667eea',
                              fontWeight: '500',
                              '&:hover': {
                                background: 'rgba(102, 126, 234, 0.1)'
                              }
                            }}
                          >
                            {intl.formatMessage({ id: 'not_registered' })}
                          </Button>
                        </>
                      )}

                      {path === '/register' && (
                        <Button
                          variant="text"
                          onClick={() => history.push({ pathname: '/login', search })}
                          sx={{
                            textTransform: 'none',
                            color: '#667eea',
                            fontWeight: '500',
                            '&:hover': {
                              background: 'rgba(102, 126, 234, 0.1)'
                            }
                          }}
                        >
                          {intl.formatMessage({ id: 'hadaccount' })}
                        </Button>
                      )}

                      {path === '/forget' && (
                        <Button
                          variant="text"
                          onClick={() => history.push({ pathname: '/login', search })}
                          sx={{
                            textTransform: 'none',
                            color: '#667eea',
                            fontWeight: '500',
                            '&:hover': {
                              background: 'rgba(102, 126, 234, 0.1)'
                            }
                          }}
                        >
                          {intl.formatMessage({ id: 'login' })}
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {googleLoging && (
        <LoadingScreen style={{ position: 'absolute' }} />
      )}

      <SpinnerAndToast statusCallbacker={setSubmitting} />
    </div>
  )
}

export default Login
