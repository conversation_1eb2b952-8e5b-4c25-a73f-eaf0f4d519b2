/* @flow */

import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useLocation } from 'react-router-dom'

// import SpinnerAndToast, { showMessage } from './SpinnerAndToast';
import * as TicketAction from '../../actions/ticketAction';
import { getState } from '../../reducers/listReducer';
import * as Constants from '../../constants/constants';
import HighLightActionBar from '../../components/rils/HighLightActionBar';
import { get_server_host } from '../../utils/serverAPIUtil';
// import ImageViewerModal from './ImageViewerModal';
import { NOTE_DIALOG, PRIVATE_DOCS_ACTIONS, SUB_DOCS_ACTIONS } from '../../constants/actionTypes';
// import * as TagMaker from '../../utils/tagMaker';
import HighlightNoteEditorModal from 'src/components/rils/HighlightNoteEditorModal';

const ArticleReader = () => {
  const dispatch = useDispatch();
  const location = useLocation();
  const [articleId, setArticleId] = useState();

  const articles = useSelector(state => state.articles.byId);
  const net_info = useSelector(state => state.uiState.net_info);
  const loginState = useSelector(state => state.loginIn);
  const article_highlight_lists = useSelector(state => state.article_highlight_lists);
  const [item, setItem] = useState({});
  const [actionBarVisible, setActionBarVisible] = useState(true);
  const [highlightActionBarVisible, setHighlightActionBarVisible] = useState(false);
  const [highlight, setHighlight] = useState();
  const [server_host, setServer_host] = useState();
  const gathered = parseInt(item && item.article ? item.article.status : 1) === Constants.ARTICLE_STATUS.succeed;
  const [readPos, setReadPos] = useState({ readPosition: 0, lastReadPosition: 0, pageHeight: 0 });
  const [resetReadPosTimer, setResetReadPosTimer] = useState(0);

  const container = useRef(null);
  const [width, setWidth] = useState(400);

  useEffect(() => {
    get_server_host().then((value) => setServer_host(value));
  }, [])

  useEffect(() => {
    const params = new Proxy(new URLSearchParams(location.search), {
      get: (searchParams, prop) => searchParams.get(prop),
    });

    setArticleId(location.state && location.state.id || params.id)
    setItem(location.state && location.state.item || { _id: articleId, article: articles[articleId] });

    updateReadPosition(true);
    setReadPos({ readPosition: 0, lastReadPosition: 0, pageHeight: 0 });
  }, [location]);

  useEffect(() => {
    window.addEventListener('message', onWebMessage);

    return () => {
      window.removeEventListener("message", onWebMessage);
    }
  }, [articleId, server_host]);

  useEffect(() => {
    if (container && container.current) {
      setWidth(container.current.offsetWidth);
    }
  }, [container.current]);


  const getArticle = () => {
    return articles[articleId];
  }

  const updateReadPosition = (rightNow) => {
    if (!item?._id || !rightNow && (Math.abs(readPos.lastReadPosition - readPos.readPosition) * readPos.pageHeight < 300)) {
      return;
    }

    dispatch(TicketAction.updateRILReadPosition({ _id: item._id, data: { readPosition: readPos.readPosition } }, () => {
      setReadPos(prevState => {
        return {
          ...prevState,
          lastReadPosition: readPos.readPosition
        }
      })
    }, 'article'));
  }

  useEffect(() => {
    const timer = setTimeout(updateReadPosition, 5000);

    return () => {
      clearTimeout(timer);
    }
  }, [resetReadPosTimer, readPos]);

  const tickleReadPositionUpdate = () => {
    if (item.userId !== loginState.user._id) {
      return;
    }

    setResetReadPosTimer(prevState => prevState + 1);
  }

  const onScroll = (direction, message) => {
    if (message.position) {
      setReadPos(prevState => {
        return {
          ...prevState,
          readPosition: message.position,
          pageHeight: message.pageHeight
        }
      })
    }
    toggleHighlightActionBar(false);
    // toggleReaderActionBar(direction === 'up');

    tickleReadPositionUpdate();
  }

  const onWebMessage = (event) => {
    if ((event.origin + '/') !== server_host) {
      return;
    }
    if (process.env.NODE_ENV !== 'production') {
      console.log('get message from web..............', event.data, articleId, item)
    }

    const url = item && item.article && item.article.url;
    let message = event.data;

    if (typeof message === 'string') {
      try {
        message = JSON.parse(message);
      } catch (err) {
        console.log(err);
      }
    }

    switch (message.type) {
      case "orignalHtml":
        if (this.state.loadingErr) {
          break;
        }
        // let content = TagMaker.createTag(message.content, url);
        // if (!content) {
        //   setTimeout(() => this.onWebViewLoaded(true), 1000);
        //   break;
        // }
        // let source = message.source;
        // if (!source) {
        //   source = getHostFromUrl(url);
        // }
        // this.setState({ gatheredArticle: { title: message.title, content, icon: message.icon, source }, gatherMethod: message.gatherMethod }, () => {
        //   const { gatheredArticle, gatherMethod } = this.state;
        //   dispatch(TicketAction.uploadHtmlForGathering({ articleId, url: item.article.url, article: gatheredArticle, gatherMethod }, navigation.state.key))
        // });
        break;
      case "elementShow":
      case "scrollup":
        onScroll('up', message);
        break;
      case "elementDisappear":
      case "scrolldown":
        onScroll('down', message);
        break;
      case "highlight":
        let highlight = {
          start: message.start,
          end: message.end,
          content: message.content
        }

        if (message.selectStart) {
          highlight.selectStart = message.selectStart;
        }
        if (message.selectEnd) {
          highlight.selectEnd = message.selectEnd;
        }

        dispatch(TicketAction.highlight({ articleId, highlight }, (item) => {
          highlightResult(true, message);
          if (item.newCreatedDoc) {
            dispatch({
              type: item.newCreatedDoc.parent === 'root' ? PRIVATE_DOCS_ACTIONS.added : SUB_DOCS_ACTIONS.added,
              _id: item.newCreatedDoc._id,
              item: Object.assign({}, item.newCreatedDoc),
              params: { pageBy: 'orderFactor' },
              key: item.newCreatedDoc.parent === 'root' ? loginState.user.workingOrgId : item.newCreatedDoc.parent
            })
          }
        }, () => highlightResult(false, message), 'article'));
        break;
      case "noteList":
        openHighlightNoteFromMessage(message);
        break;
      case "longPress":
        // Vibration.vibrate(300);
        break;
      case "menuDisAppear":
        toggleHighlightActionBar(false);
        break;
      case "menuShow":
        toggleHighlightActionBar(true, message);
        break;
      case "imgSrc":
        this.imgClicked(message.src, message.imgUrls);
        break;
      case "original":
        // if (Constants.isWeb) {
        window.open(url, "_blank");
        // } else {
        //   navigation.navigate('ArticleOrignalReader', { url: item.article.url });
        // }
        break;
    }
  }

  const postMessage = (data) => {
    document.getElementById('articleFrame').contentWindow.postMessage(JSON.stringify(data), server_host)
    // if (!this.refs.webview) {
    //   return;
    // }

    // // if (Constants.isWeb) {
    // this.refs.webview.postMessage(JSON.stringify(data), this.state.server_host);
    // } else {
    //   const js = `
    //     ${data.method}('${data.params.join(`','`)}');
    //     true;
    //   `;
    //   this.refs.webview.injectJavaScript(js);
    // }
  }

  const openHighlightNoteFromMessage = (message) => {
    openHighlightNoteEditor(message);

    // const highlights = getSavedHighlights(message);

    // if (!highlights) {
    //   return;
    // }

    // if (highlights.length > 1) {
    //   //Todo:
    // } else {
    //   openHighlightNoteEditor(highlights[0])
    // }
  }

  const openHighlightNoteEditor = (highlight) => {
    dispatch({ type: NOTE_DIALOG, value: { visible: true, item: highlight } });
  }

  const highlightResult = (succeed, { start, end }) => {
    postMessage({
      method: 'removeColor',
      params: [succeed, start, end]
    });
  }

  const highlightRemoved = (highlight) => {
    if (!highlight || !highlight.highlight) {
      return;
    }

    postMessage({
      method: 'deleteHighLight',
      params: [highlight.highlight.start, highlight.highlight.end]
    });

    toggleHighlightActionBar(false);
  }

  const noteAdded = (highlight) => {
    if (!highlight) {
      return;
    }

    postMessage({
      method: 'addNoteBtn',
      params: [highlight.highlight.start, highlight.highlight.end, highlight.note && highlight.note.note]
    });
    // showMessage('note updated')
  }

  const highlightCopied = () => {
    toggleHighlightActionBar(false);
    // showMessage('copied to clipboard')
  }

  const toggleHighlightActionBar = (isVisible, highlight) => {
    if (highlight && highlight.start) {
      setHighlight(highlight);
    }
    setHighlightActionBarVisible(isVisible);
  }

  const toggleReaderActionBar = (isVisible) => {
    if (actionBarVisible != isVisible) {
      setActionBarVisible(isVisible);
    }
  }

  const onWebViewLoaded = (retryGather) => {
    if (!gathered) {
      return;
    }

    dispatch(TicketAction.fetchArticleHighlights(articleId, highlightsLoaded));

    postMessage({ method: 'scrollToY', params: [item.readPosition] })

  }

  const highlightsLoaded = (highlights) => {
    if (!highlights || highlights.length === 0) {
      return;
    }

    postMessage({
      method: 'markHighLight',
      params: [JSON.stringify(highlights.map(hl => {
        return {
          highlight: hl.highlight,
          note: hl.note,
          isOwner: hl.isOwner
        }
      }))]
    })
  }


  const onShouldStartLoadWithRequest = (request) => {
    const url = request.url;
    if (!url ||
      url.startsWith('http') ||
      url.startsWith('https') ||
      url.startsWith('about:blank')
    ) {
      return true;
    }

    return false;
  }

  const newTag = (tag, callback) => {
    dispatch(TicketAction.newTag({ tag, app: 'ril' }, callback, 'article'));
  }

  const tagRIL = (selectedTags, rilId) => {
    dispatch(TicketAction.updateRILTags({ _id: rilId, tags: selectedTags }, 'article'))
  }

  return (
    <div
      ref={container}
      style={{ width: '100%', height: '100%', overflow: 'hidden' }}
    // style={styles.container} forceInset={{ bottom: (highlightActionBarVisible || actionBarVisible) ? 'always' : 'never' }}
    >

      <iframe
        id="articleFrame"
        style={{ width: '100%', height: '100%' }}
        frameBorder="0"
        src={gathered ? `${server_host}article/details/${articleId}?viewerApp=webapp` : (item.article && item.article.url)}
        onLoad={() => onWebViewLoaded(false)}
      />


      <HighlightNoteEditorModal
        articleId={articleId}
        noteAdded={noteAdded}
      />

      {
        highlightActionBarVisible &&
        < div style={{ ...styles.actionbar, width }}>
          <HighLightActionBar
            articleId={articleId}
            savedHighlight={highlight}
            highlight={highlight}
            highlightRemoved={highlightRemoved}
            copied={highlightCopied}
            addNote={openHighlightNoteEditor}
          />
        </div>
      }
    </div>
  )
}


const styles = {
  container: {
    flex: 1,
    backgroundColor: '#f8f8f8',
    justifyContent: 'flex-end'
  },
  actionbar: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    paddingTop: 10,
    // paddingBottom: (Platform.OS === 'ios' && IS_IPHONE_X) ? 0 : 10,
    backgroundColor: '#f8f8f8',
    // alignItems: 'stretch',
  },
  backIcon: {
    alignItems: 'flex-start',
    paddingHorizontal: 16,
  },
  seperator: {
    height: 1,
    paddingHorizontal: 12,
    backgroundColor: '#EFEFEF',
  },

};

export default ArticleReader;
