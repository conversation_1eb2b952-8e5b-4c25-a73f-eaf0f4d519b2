import { useEffect, useState } from "react";
import { withRouter, useParams, useHistory, useLocation } from "react-router-dom";
import { useIntl } from "react-intl";
import { useDispatch, useSelector } from "react-redux";
import DataEditorCore from "src/components/dbeditor/DataEditorCore";

const DataEditor = (props) => {
    const currentLocation = useLocation();
    const intl = useIntl();
    const params = new Proxy(new URLSearchParams(currentLocation.search), {
        get: (searchParams, prop) => searchParams.get(prop) || '',
    });

    const { hid, dataId, toHtml } = params;

    return <DataEditorCore
        dataSourceHid={hid}
        dataId={dataId}
        toHtml={toHtml}
    />
};

export default withRouter(DataEditor);