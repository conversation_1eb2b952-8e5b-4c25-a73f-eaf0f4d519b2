import InviteFriendsMessage from "@/components/feedbacks/InviteFriendsMessage";
import { useLocation } from "react-router-dom";
import { getTopLevelDomain } from "../../utils/url";
import ReactGA from "react-ga4";
import { useEffect } from "react";

const Invitation = () => {
    const currentLocation = useLocation();
    const params = new Proxy(new URLSearchParams(currentLocation.search), {
        get: (searchParams, prop) => searchParams.get(prop) || '',
    });
    const source = params.source;
    const greetings = params.greetings;

    useEffect(() => {
        // ReactGA.initialize(GOOGLE_ANALYTICS_TRACKID);
        ReactGA.send({ hitType: "pageview", page: "/invitation-event", title: "Init Invitation" });
    }, [])

    return <div className="fill-available full-height"
        style={{
            columnGap: '10px', display: 'flex', flexDirection: 'column',
            alignItems: 'center', paddingTop: 120,
            backgroundColor: '#f3f3f3'
        }}
    >
        <div style={{ display: 'flex', flexDirection: 'column' }}>
            <InviteFriendsMessage app={source} greetings={greetings} />
            {
                source === 'extension' &&
                <div
                    className="fill-available"
                    style={{
                        // width: '100%',
                        paddingTop: 30,
                        display: 'flex',
                        flexDirection: 'row',
                        justifyContent: 'space-between',
                        color: 'deepskyblue',
                    }}
                >
                    <div
                        style={{
                            cursor: 'pointer'
                        }}
                        onClick={() => {
                            window.open(`https://${getTopLevelDomain()}/welcome_extension.html`, '_blank')
                        }}
                    >
                        FunBlocks AI Extension Guide
                    </div>
                    <div
                        style={{
                            cursor: 'pointer'
                        }}
                        onClick={() => {
                            window.open('/', '_blank')
                        }}
                    >
                        FunBlocks Workspace
                    </div>
                </div>
            }
        </div>
    </div>
}

export default Invitation