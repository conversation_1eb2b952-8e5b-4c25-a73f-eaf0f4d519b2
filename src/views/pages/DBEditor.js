import { useLocation } from "react-router-dom";
import DBEditorFrame from 'src/components/dbeditor/DBEditorFrame';

import { useIntl } from "react-intl";

const DBEditor = () => {
    const intl = useIntl();
    const currentLocation = useLocation();
    const params = new Proxy(new URLSearchParams(currentLocation.search), {
        get: (searchParams, prop) => searchParams.get(prop) || '',
    });
    const hid = params.hid;

    return (
        <DBEditorFrame
            hid={hid}
        />
    )
}

export default DBEditor;