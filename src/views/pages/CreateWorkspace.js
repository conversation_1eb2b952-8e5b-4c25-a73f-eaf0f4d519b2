import React, { useState, useEffect, useLayoutEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link, useHistory, useLocation } from 'react-router-dom'
import { useIntl } from 'react-intl';

import Radio from '@mui/material/Radio';
import RadioGroup from '@mui/material/RadioGroup';
import FormControlLabel from '@mui/material/FormControlLabel';
import FormControl from '@mui/material/FormControl';
import FormLabel from '@mui/material/FormLabel';

import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import Typography from '@mui/material/Typography';
import { Button, CardActionArea, TextField } from '@mui/material';


import * as TicketAction from '../../actions/ticketAction';
import SpinnerAndToast from 'src/components/SpinnerAndToast';
import { JOIN_WORKSPACE_DIALOG, LOGIN_ACTIONS } from 'src/constants/actionTypes';
import { getStateByUser } from 'src/reducers/listReducer';
import JoinWorkspace from './JoinWorkspace';
import { PromoteModal } from '../../components/common/PromoteModal';
import SettingsModal from '../../components/SettingsModal';

const CreateWorkspace = () => {
  const intl = useIntl();
  const dispatch = useDispatch();
  const history = useHistory();
  const currentLocation = useLocation();
  const routeState = currentLocation.state || {};

  const user = useSelector(state => state.loginIn.user);
  const to_confirm_org_lists = useSelector(state => getStateByUser(state.to_confirm_org_lists, user));

  if (!user._id) {
    history.push({ pathname: '/login', search: currentLocation.search });
  } else if (!user.activated) {
    history.push('/activate');
  }

  if (to_confirm_org_lists.items && to_confirm_org_lists.items.length > 0) {
    dispatch({ type: JOIN_WORKSPACE_DIALOG, value: { visible: true } });
  }

  useEffect(() => {
    dispatch(TicketAction.fetchToComfirmOrgs());
  }, []);

  const [spaceType, setSpaceType] = useState('personal');
  const [teamSpaceSelected, setTeamSpaceSelected] = useState(false);
  const [orgName, setOrgName] = useState();
  const [orgNameValid, setOrgNameValid] = useState(true);

  const handleChange = (event) => {
    setSpaceType(event.target.value);
  };

  const onSpaceTypeSelected = () => {
    setTeamSpaceSelected(spaceType === 'team');

    if (spaceType === 'personal') {
      createSpace();
    }
  }

  const createSpace = () => {
    const isOrgNameValid = !teamSpaceSelected || orgName && orgName.length >= 2;
    
    const name = teamSpaceSelected ? `${orgName + intl.formatMessage({id: 'team_space'})}` : `${user.nickname  + intl.formatMessage({id: 'personal_space'})}`;
    setOrgNameValid(isOrgNameValid);
    if (isOrgNameValid) {
      dispatch(TicketAction.addOrg({ name, type: spaceType }, (org) => {
        dispatch({ type: LOGIN_ACTIONS.workspace, item: { orgId: org._id } });
        history.push('/home')
      }));
    }
  }

  return (
    <div className='full-height' style={{ display: 'flex', flexDirection: 'column', width: '100%', alignItems: 'center', justifyContent: 'center', background: '#f5f5f5' }}>
      {!teamSpaceSelected &&
        <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', }}>
          <Typography gutterBottom variant="h3" component="div">
            {intl.formatMessage({ id: 'create_space_title' })}
          </Typography>
          <FormControl style={{ paddingTop: 30, paddingBottom: 30 }}>
            <RadioGroup
              row
              aria-labelledby="demo-form-control-label-placement"
              name="position"
              value={spaceType}
              onChange={handleChange}
            >
              <FormControlLabel
                value="personal"
                control={<Radio />}
                label={<FormLabel>
                  <Card style={{ backgroundColor: spaceType === 'personal' ? 'aliceblue' : 'white' }}>
                    <CardActionArea
                      onClick={() => setSpaceType('personal')}
                    >
                      {/* <CardMedia
                    component="img"
                    height="140"
                    image="/static/images/cards/contemplative-reptile.jpg"
                    alt="green iguana"
                  /> */}
                      <CardContent  sx={{ maxWidth: 345, minHeight: 200 }}>
                        <Typography gutterBottom variant="h3" component="div">
                          {intl.formatMessage({ id: 'personal' })}
                        </Typography>
                        <Typography variant="body1" color="text.secondary">
                          {intl.formatMessage({ id: 'personal_desc' })}
                        </Typography>
                      </CardContent>
                    </CardActionArea>
                  </Card>
                </FormLabel>}
                labelPlacement="top"
              />
              <FormControlLabel
                value="team"
                control={<Radio />}
                label={<FormLabel>
                  <Card style={{ backgroundColor: spaceType !== 'personal' ? 'aliceblue' : 'white' }}>
                    <CardActionArea
                      onClick={() => setSpaceType('team')}
                    >
                      {/* <CardMedia
                    component="img"
                    height="140"
                    image="/static/images/cards/contemplative-reptile.jpg"
                    alt="green iguana"
                  /> */}
                      <CardContent sx={{ maxWidth: 345, minHeight: 200 }}>
                        <Typography gutterBottom variant="h3" component="div">
                          {intl.formatMessage({ id: 'team' })}
                        </Typography>
                        <Typography variant="body1" color="text.secondary">
                          {intl.formatMessage({ id: 'team_desc' })}
                        </Typography>
                      </CardContent>
                    </CardActionArea>
                  </Card>
                </FormLabel>}
                labelPlacement="top"
              />
            </RadioGroup>
          </FormControl>
          <Button variant="contained" size="large" onClick={onSpaceTypeSelected}>{intl.formatMessage({ id: 'confirm' })}</Button>
        </div>
      }
      {
        teamSpaceSelected &&
        <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', }}>
          <Typography gutterBottom variant="h3" component="div">
            {intl.formatMessage({ id: 'create_team_workspace' })}
          </Typography>
          <Typography variant="body1" color="text.secondary">
            {intl.formatMessage({ id: 'create_team_workspace_detail' })}
          </Typography>

          <FormControl style={{ paddingTop: 30, paddingBottom: 30, paddingLeft: 10, paddingRight: 10, width: '100%' }}>
            <TextField
              style={{ width: '100%' }}
              required
              id="outlined-required"
              label={intl.formatMessage({ id: 'team_name' })}
              onChange={(e) => setOrgName(e.target.value)}
              error={!orgNameValid}
            />
          </FormControl>
          <div style={{ width: '100%', display: 'flex', marginLeft: 10, marginRight: 10, justifyContent: 'space-between' }}>
            <Button variant="text" size="large" onClick={() => setTeamSpaceSelected(false)}>{intl.formatMessage({ id: 'back' })}</Button>
            <Button variant="contained" size="large" onClick={createSpace}>{intl.formatMessage({ id: 'confirm' })}</Button>
          </div>
        </div>
      }

      {
        routeState.from === 'profile' &&
        <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', marginTop: 40 }}>
          <Button variant="text" size="medium" onClick={() => history.push('/home')}>{intl.formatMessage({ id: 'back' })}</Button>
        </div>
      }
      <JoinWorkspace />
      <PromoteModal />
      <SettingsModal />
      <SpinnerAndToast />
    </div>
  )
}

export default CreateWorkspace
