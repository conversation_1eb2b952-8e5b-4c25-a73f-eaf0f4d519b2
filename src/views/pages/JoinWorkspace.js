import React, { useState, useEffect, useLayoutEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link, useHistory, useLocation } from 'react-router-dom'
import { useIntl } from 'react-intl';

import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogTitle from '@mui/material/DialogTitle';

import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
import ListItemButton from '@mui/material/ListItemButton';
import ListItemText from '@mui/material/ListItemText';

import * as TicketAction from '../../actions/ticketAction';
import { JOIN_WORKSPACE_DIALOG } from 'src/constants/actionTypes';
import { getStateByUser } from 'src/reducers/listReducer';

const JoinWorkspace = () => {
  const intl = useIntl();
  const dispatch = useDispatch();
  const history = useHistory();

  const user = useSelector(state => state.loginIn.user);
  const to_confirm_org_lists = useSelector(state => getStateByUser(state.to_confirm_org_lists, user));
  const org_lists = useSelector(state => getStateByUser(state.org_lists, user));
  const joinWorkspaceDialogState = useSelector(state => state.uiState.joinWorkspaceDialog);
  const [confirmedOrgId, setConfirmedOrgId] = useState(''); 

  const handleClose = () => {
    dispatch({ type: JOIN_WORKSPACE_DIALOG, value: { visible: false } });
  }

  const handleConfirm = (org, confirmed) => {
    dispatch(TicketAction.confirmJoinOrg({ orgId: org._id, confirmed }, () => {
      setConfirmedOrgId(org._id);
    }, 'joinWorkspaceDialog'));
  }

  const enterHome = () => {
    if (org_lists.items.length === 0 && !confirmedOrgId) {
      return handleClose();
    }

    dispatch(TicketAction.switchWorkspace({ orgId: confirmedOrgId || org_lists.items[0]._id }, () => {
      history.push('/home');
      handleClose();
    }, 'profile'));
  }

  return (
    <Dialog
      open={!!joinWorkspaceDialogState && joinWorkspaceDialogState.visible}
      onClose={handleClose}
      scroll='paper'
      aria-labelledby="scroll-dialog-title"
      aria-describedby="scroll-dialog-description"
    >
      <DialogTitle id="scroll-dialog-title" style={{ display: 'flex', justifyContent: 'space-between' }}>
        <span>{intl.formatMessage({ id: 'invited_to_teams' })}</span>
      </DialogTitle>
      <DialogContent dividers={true}>
        <List sx={{ width: '100%', width: 488, bgcolor: 'background.paper' }}>
          {to_confirm_org_lists.items.length === 0 &&
            <DialogContentText>{intl.formatMessage({ id: 'create_or_enter_space' })}</DialogContentText>
          }
          {to_confirm_org_lists.items.map((item, index) => {
            return <ListItem key={index} component="div" disablePadding>
              <ListItemButton>
                <ListItemText style={{ width: '320px' }} primary={item.name} />
                <Button
                  onClick={(event) => {
                    handleConfirm(item, 1);
                  }}>
                  {intl.formatMessage({ id: 'accept' })}
                </Button>
                <Button
                  onClick={(event) => {
                    handleConfirm(item, -1);
                  }}>
                  {intl.formatMessage({ id: 'reject' })}
                </Button>
              </ListItemButton>
            </ListItem>
          })}
        </List>
      </DialogContent>
      <DialogActions>
        <Button onClick={handleClose}>{intl.formatMessage({ id: 'create_space' })}</Button>
        <Button onClick={enterHome}>{intl.formatMessage({ id: 'enter_space' })}</Button>
      </DialogActions>
    </Dialog>
  );
}

export default JoinWorkspace
