import { getLocale } from "@/utils/Intl";
import { useEffect, useState } from "react";
import { useIntl } from "react-intl";
import { useDispatch, useSelector } from "react-redux";
import AIServiceSubscribe from "@/components/settings/ServiceSubscribe_ai_en";
const ExtensionAIPlans = () => {
    const intl = useIntl();
    const lng = useSelector(state => state.uiState.lng) || getLocale();

    return (
        <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', width: '100%', alignSelf: 'center', backgroundColor: 'white' }}>
            <AIServiceSubscribe />
        </div>
    );
}

export default ExtensionAIPlans