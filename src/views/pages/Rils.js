import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { fetchRILs, fetchRILsDeleted, fetchRILsRead, } from 'src/actions/ticketAction';
import ChangeRILTitleModal from 'src/components/rils/ChangeRILTitleModal';
import RILItem from 'src/components/rils/RILItem';
import { LIST_KEYS } from 'src/constants/actionTypes';
import { getStateByUser } from 'src/reducers/listReducer';

import CommonList from 'src/components/CommonList';


const Rils = ({ }) => {
  const user = useSelector(state => state.loginIn.user)
  const folder = useSelector(state => state.uiState.rilsFolder) || 0;

  const fetcher = [fetchRILs, fetchRILsRead, fetchRILsDeleted];

  const data_lists = [
    useSelector(state=>getStateByUser(state.ril_lists, user)), 
    useSelector(state=>getStateByUser(state.read_ril_lists, user)), 
    useSelector(state=>getStateByUser(state.deleted_ril_lists, user)), 
  ];

  const refreshRils = useSelector(state => state.uiState.refreshRils);


  const pageBy = 'updatedAt';
  const [fetcherProps, setFetcherProps] = useState({
    list_key: LIST_KEYS.rils, 
    data_fetcher: fetcher[0],
  });

  useEffect(()=>{
    setFetcherProps(prevState => {
      return {
        ...prevState,
        data_fetcher: fetcher[folder],
        invalidate: false,
        fetch_params:  {
          pageBy, folder
        }
      }
    })
  }, [folder]);

  useEffect(() => {
    setFetcherProps(prevState=> {
      return {
        ...prevState,
        fetch_params: {
          pageBy, folder
        }, 
        invalidate: true
      }
    })
  }, [refreshRils]);

  return <div style={{ display: 'flex', flexDirection: 'column', width: '100%' }}>
    <ChangeRILTitleModal
    />
    <CommonList
      list_data = {data_lists[folder]}
      fetcherProps={fetcherProps}
      itemRender={(item) => <RILItem
        key={item._id}
        item={item} mode='ril' />}
    />
   
  </div>;
};


export default Rils
