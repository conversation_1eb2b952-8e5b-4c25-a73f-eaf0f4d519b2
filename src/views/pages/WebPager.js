/* @flow */

import { useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom'

// import SpinnerAndToast, { showMessage } from './SpinnerAndToast';
import { get_server_host } from '../../utils/serverAPIUtil';

const WebPager = () => {
  const location = useLocation();

  const [server_host, setServer_host] = useState();
  const [uri, setUri] = useState();

  useEffect(() => {
    get_server_host().then((value) => setServer_host(value));
  }, [])

  useEffect(() => {
    const params = new Proxy(new URLSearchParams(location.search), {
      get: (searchParams, prop) => searchParams.get(prop),
    });

    setUri(location.state.uri || (server_host + location.state.webpage || params.webpage));
  }, [location]);

  return (
    <div
      style={{ width: '100%', height: '100%', overflow: 'hidden' }}
    >
      <iframe
        id="articleFrame"
        style={{ width: '100%', height: '100%' }}
        frameBorder="0"
        src={uri}
      />
    </div>
  )
}

export default WebPager;
