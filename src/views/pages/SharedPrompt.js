import { PromptForm } from "@/components/settings/PromptForm";
import { useCallback, useEffect, useState } from "react";
import { withRouter, useParams, useHistory, useLocation } from "react-router-dom";
import { useDispatch, useSelector } from 'react-redux';
import { getPrompt, pinPrompt } from "@/actions/ticketAction";
import { Tooltip, Button } from "@mui/material";
import LoadingButton from '@mui/lab/LoadingButton/LoadingButton';
import { useIntl } from 'react-intl';
import { PROMPTS_DIALOG, SETTINGS_DIALOG } from "@/constants/actionTypes";
import { PromptsModalDataHandler } from "@/components/settings/PromptsModalDataHandler";

const SharedPrompt = (props) => {
    const dispatch = useDispatch();
    const intl = useIntl();
    const currentLocation = useLocation();
    const params = new Proxy(new URLSearchParams(currentLocation.search), {
        get: (searchParams, prop) => searchParams.get(prop) || '',
    });
    const loginUser = useSelector(state => state.loginIn && state.loginIn.user);
    const operationStatus = useSelector(state => state.operationStatus);

    const [data, setData] = useState();
    const [loading, setLoading] = useState(false);
    const [pinned, setPinned] = useState();

    useEffect(() => {
        if (!operationStatus?.inProgress) {
            setLoading(false);
        }
    }, [operationStatus])

    useEffect(() => {
        dispatch({ type: SETTINGS_DIALOG, value: { visible: false } });

        if (!params.id || params.id === 'undefined') {
            return;
        }

        dispatch(getPrompt({ _id: params.id }, (item) => {
            setData(item);
        }))
    }, [params.id])

    const handlePinPrompt = useCallback((item) => {
        setLoading(true);
        dispatch(pinPrompt({ _id: item._id, pin: true }, (item) => {
            if (item) {
                setPinned(true);
            }
        }, 'prompts'));
    }, []);

    const handleRunPrompt = useCallback((item) => {
        dispatch({
            type: PROMPTS_DIALOG,
            value: {
                visible: true,
                space: 'private',
                prompt: item,
                refresher: Math.random()
            }
        })
    }, []);

    if (!data) return <></>

    return <div style={{ paddingTop: 30, width: 720 }}>
        <PromptForm
            data={data}
            setData={setData}
            readOnly={true}
            trigger={'shared'}
        />
        <div style={{
            display: 'flex',
            flexDirection: 'row',
            columnGap: '20px',
            justifyContent: 'center',
            width: '100%',
            padding: 6,
            paddingRight: '16px',
        }}>
            {
                loginUser._id != data.userId?._id &&
                <Tooltip title={intl.formatMessage({ id: 'prompts_pinned_desc' })} placement="top-start">
                    <LoadingButton disabled={loading || pinned} loading={loading} variant='contained' onClick={() => handlePinPrompt(data)}>{intl.formatMessage({ id: pinned ? 'pinned' : 'pin' })}</LoadingButton>
                </Tooltip>
            }
            {
                !data.prompt.includes('{{selected_text}}') &&
                <Button variant='contained' color="success" onClick={() => handleRunPrompt(data)}>{intl.formatMessage({ id: 'run_prompt' })}</Button>
            }
        </div>
        <PromptsModalDataHandler />
    </div>
};

export default withRouter(SharedPrompt);