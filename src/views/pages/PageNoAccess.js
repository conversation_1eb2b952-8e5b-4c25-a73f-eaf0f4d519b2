import { useIntl } from 'react-intl';

const PageNoAccess = () => {
  const intl = useIntl();
  return (
    <div className="bg-light min-vh-100 d-flex flex-row align-items-center">

      <div className="clearfix">
        <h1 className="float-start display-3 me-4">{intl.formatMessage({id: 'no_access'})}</h1>
        <h4 className="pt-3">{intl.formatMessage({id: 'no_access_notice'})}</h4>
        <p className="text-medium-emphasis float-start">
          {intl.formatMessage({id: 'no_access_guide'})}
        </p>
      </div>
    </div>
  )
}

export default PageNoAccess
