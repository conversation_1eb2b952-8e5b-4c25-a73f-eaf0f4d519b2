import { SETTINGS_DIALOG } from '@/constants/actionTypes';
import { useDispatch } from 'react-redux';
import { Redirect, useLocation } from 'react-router-dom'

const RedirectTo = () => {
    const dispatch = useDispatch()

    const currentLocation = useLocation();
    const params = new Proxy(new URLSearchParams(currentLocation.search), {
        get: (searchParams, prop) => searchParams.get(prop) || '',
    });

    if(params.to === 'settings') {
        dispatch({ type: SETTINGS_DIALOG, value: { visible: true, page: params.page } });
    }

    return <Redirect from="/home" to="/" />
}

export default RedirectTo