/* @flow */

import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useLocation, useHistory } from 'react-router-dom'
import { getDoc } from 'src/actions/ticketAction';
import SlidesActionBar from 'src/components/slides/SlidesActionBar';
import { SHOW_APP_LIST } from 'src/constants/actionTypes';

import { get_server_host } from '../../utils/serverAPIUtil';

const SlidesViewer = () => {
  const dispatch = useDispatch();
  const location = useLocation();
  const history = useHistory();
  const [hid, setHid] = useState();
  const [item, setItem] = useState();

  const loginState = useSelector(state => state.loginIn);
  const docs = useSelector(state => state.docs);
  const [server_host, setServer_host] = useState();

  const container = useRef(null);
  const [width, setWidth] = useState(400);

  useEffect(() => {
    get_server_host().then((value) => setServer_host(value));
  }, []);

  useEffect(() => {
    if (container && container.current) {
      setWidth(container.current.offsetWidth);
    }
  }, [container.current]);

  useEffect(() => {
    const params = new Proxy(new URLSearchParams(location.search), {
      get: (searchParams, prop) => searchParams.get(prop),
    });

    setHid(location.state && location.state.hid || params.hid);
  }, [location]);

  useEffect(() => {
    if (hid) {
      // setLoading(true);
      dispatch(getDoc({ hid: hid }, (doc) => {
        // setLoading(false);
      }, null, 'slidesViewer'));
      dispatch({ type: SHOW_APP_LIST, value: false })
    }
  }, [hid]);

  useEffect(() => {
    setItem(docs.byId[hid])
  }, [hid, docs]);

  if (item && item.permission < 0) {
    history.push({ pathname: '/noaccess', search: location.search });
  }

  return (
    <div
      style={{ width: '100%', height: '100%', overflow: 'hidden' }}
      ref={container}
    >
      <iframe
        id="articleFrame"
        style={{ width: '100%', height: '100%' }}
        frameBorder="0"
        src={server_host + 'view.html?mode=preview&hid=' + hid}
      />

      {
        item &&
        <div style={{
          position: 'relative',
          bottom: '68px',
          width: 'fit-content'
        }}>
          <SlidesActionBar item={item} mode={'fixed_at_viewer'} style={{ justifyContent: 'flex-start' }} />
        </div>
      }
    </div>
  )
}

export default SlidesViewer;
