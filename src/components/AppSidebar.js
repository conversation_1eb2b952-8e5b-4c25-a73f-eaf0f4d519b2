import React, { useCallback, useEffect, useLayoutEffect, useMemo, useState } from 'react';
import { useIntl } from 'react-intl';
import {
  ProSidebar,
  Menu,
  MenuItem,
  SubMenu,
  SidebarHeader,
  SidebarFooter,
  SidebarContent,
} from 'react-pro-sidebar';

import { FileSlides } from '@styled-icons/bootstrap/FileSlides'
import { FileMedical } from '@styled-icons/bootstrap/FileMedical'
import { FileText } from '@styled-icons/bootstrap/FileText'
import { FileSpreadsheet } from '@styled-icons/bootstrap/FileSpreadsheet'

import { FaPlus } from 'react-icons/fa';
import { AddSquareMultiple } from '@styled-icons/fluentui-system-regular/AddSquareMultiple'
import { Settings } from '@styled-icons/material/Settings';
import { Trash } from '@styled-icons/bootstrap/Trash';
import { PencilSquare } from '@styled-icons/bootstrap/PencilSquare';
import { Divider, Tooltip } from '@mui/material';
import { Link, useHistory, useLocation } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { addSlides, duplicateDoc, fetchPrivateDocs, fetchSharedDocs, fetchSubDocs, fetchWorkSpaceDocs, getLatestAppVersion, newDoc, refreshList, transferDoc, trashDoc, upsertDoc } from 'src/actions/ticketAction'
import { getState } from 'src/reducers/listReducer';
import { DOC_REFS, SUB_DOCS_ACTIONS, APP_LIST, RIL_DIALOG, WORKSPACE_DOCS_ACTIONS, PRIVATE_DOCS_ACTIONS, SHARED_DOCS_ACTIONS, DOC_ACTIONS, SETTINGS_DIALOG, OPERATION_SUCCESS, SETTINGS, APP_UPGRADE_DIALOG } from 'src/constants/actionTypes';

import DeletedDocsDialog from './DeletedDocsDialog';
import { DroppableContainer } from './dndlist/DroppableContainer';
import { DraggableCard } from './dndlist/DraggableCard';
import Profile from './sidebar/Profile';
import { FileCopy } from '@styled-icons/material-outlined/FileCopy';
import { get_server_host } from '../utils/serverAPIUtil';
import SettingsModal from './SettingsModal';
import { DOC_PERMISSION } from 'src/constants/constants';

import { ContextMenuTrigger } from "react-contextmenu";
import { ContextMenu, MenuItem as CMMenuItem } from 'react-contextmenu';
import { InputModal } from './common/InputModal';
import { linkToPage } from 'src/utils/PageLinkMaker';
import packageJson from '../../package.json';
import { getLocale } from 'src/utils/Intl';
import { isCNDomain } from 'src/utils/constants';
import { FilePlus } from 'lucide-react';

const AppSidebar = ({ image, collapsed, rtl, toggled, handleToggleSidebar }) => {
  const intl = useIntl();

  const dispatch = useDispatch();
  const history = useHistory();
  const currentLocation = useLocation();
  const params = new Proxy(new URLSearchParams(currentLocation.search), {
    get: (searchParams, prop) => searchParams.get(prop),
  });

  const [server_host, setServer_host] = useState();

  const sidebarShow = useSelector((state) => state.uiState.sidebarShow);
  const [showAddDocBtn, setShowAddDocBtn] = useState(false);
  const [showSidebarSwitch, setShowSidebarSwitch] = useState(false);

  const loginUser = useSelector(state => state.loginIn.user);
  const workspace_doc_lists = useSelector(state => state.workspace_doc_lists);
  const private_doc_lists = useSelector(state => state.private_doc_lists);
  const shared_doc_lists = useSelector(state => state.shared_doc_lists);
  const sub_doc_lists = useSelector(state => state.sub_doc_lists);

  const workspaceDocs = getState(workspace_doc_lists, loginUser.workingOrgId);
  const privateDocs = getState(private_doc_lists, loginUser.workingOrgId);
  const sharedDocs = getState(shared_doc_lists, loginUser.workingOrgId);

  const docs = useSelector(state => state.docs);
  const currentDocPath = useSelector(state => state.uiState.docPath);
  const [subMenuOpen, setSubMenuOpen] = useState({});
  const [activePage, setActivePage] = useState('');
  const lng = useSelector(state => state.uiState.lng);

  const hover_state_init = {
    dragItem: {},
    hoverItem: {},
    enter: false,
    leave: -1,
  };
  const [hoverState, setHoverState] = useState(hover_state_init);

  const pageBy = 'orderFactor';
  const init_fetch_params = { pageBy, pageSize: 10, orgId: loginUser.workingOrgId };
  const [fetchProps, setFetchProps] = useState({
    params: init_fetch_params
  });

  useEffect(() => {
    get_server_host().then((value) => setServer_host(value));
    // dispatch({ type: SETTINGS.RESET_CACHE });
  }, [])

  useEffect(() => {
    if (params.hid) {
      setActivePage(params.hid);
    }

    dispatch(getLatestAppVersion({ app: 'funblocks' }, (item) => {
      if (parseInt(item) > parseInt(packageJson.version)) {
        dispatch({
          type: APP_UPGRADE_DIALOG,
          value: {
            visible: true
          }
        })
      }
    }));
  }, [params.hid])

  useEffect(() => {
    if (currentLocation.state?.hid) {
      setActivePage(currentLocation.state.hid)
    }
  }, [currentLocation])

  useEffect(() => {
    if (!loginUser.workingOrgId) return;

    setFetchProps({
      params: init_fetch_params
    });

    dispatch(refreshList(privateDocs, fetchPrivateDocs, init_fetch_params, loginUser.workingOrgId, true));
    dispatch(refreshList(sharedDocs, fetchSharedDocs, init_fetch_params, loginUser.workingOrgId, true));
    dispatch(refreshList(workspaceDocs, fetchWorkSpaceDocs, init_fetch_params, loginUser.workingOrgId, true));
  }, [loginUser.workingOrgId])

  useEffect(() => {
    if (fetchProps.fetcher) {
      dispatch(refreshList(fetchProps.data_list, fetchProps.fetcher, fetchProps.params, fetchProps.params.parent, fetchProps.invalidate))
    }
  }, [fetchProps]);

  const handleLoadMore = useCallback((list_data, fetcher, parent) => {
    if (list_data.isFetching || list_data.isEnd || !list_data.items || list_data.items.length === 0) {
      return;
    }

    let pageFrom = null;
    if (pageBy) {
      pageFrom = list_data.items[list_data.items.length - 1][pageBy];
    }

    setFetchProps(prevState => {
      return {
        ...prevState,
        params: {
          ...prevState.params,
          pageFrom,
          parent,
        },
        data_list: list_data,
        fetcher,
        invalidate: true
      }
    })
  }, [pageBy]);

  const docRefs = new Map();

  const addNewPage = (event, space) => {
    let list = privateDocs;
    if (space === 'workspace') {
      list = workspaceDocs;
    }

    const largest_order_factor = list.items.reduce((prev, curr) => {
      return prev.orderFactor > curr.orderFactor ? prev : curr;
    }, { orderFactor: 0 }).orderFactor;

    // history.push(`/editor?space=${space}&orderFactor=${largest_order_factor + Math.floor(1000 * Math.random())}`);
    history.push(linkToPage(null, { space, orderFactor: largest_order_factor + Math.floor(1000 * Math.random()) }));

    event.preventDefault();
    event.stopPropagation();
  }

  const openSpaceSettings = () => {
    dispatch({ type: SETTINGS_DIALOG, value: { visible: true } });
  }

  const SPACE_LISTS = {
    workspace: workspace_doc_lists,
    private: private_doc_lists,
    shared: shared_doc_lists
  };

  const calcOrderFactor = (to, position, space) => {
    const doc_list = to === 'root' ? getState(SPACE_LISTS[space], loginUser.workingOrgId) : getState(sub_doc_lists, to);
    const toList = doc_list.items;

    let orderFactor = 0;
    if (toList.length !== 0) {
      if (position === 0) {
        orderFactor = toList[0].orderFactor + Math.floor(1000 * Math.random());
      } else if (position === toList.length) {
        orderFactor = toList[toList.length - 1].orderFactor - Math.floor(1000 * Math.random());
      } else {
        orderFactor = (toList[position].orderFactor + toList[position - 1].orderFactor) / 2;
      }
    }

    return orderFactor;
  }

  const isAncestor = (ancestor, child) => {
    if (child === 'root') {
      return false;
    }
    if (child === ancestor) {
      return true;
    }

    let item = docRefs.get(child);
    if (!item) {
      return false;
    }

    return isAncestor(ancestor, item.parent);
  }

  const onDrop = (space) => {
    let from = hoverState.dragItem.parent;
    let to = hoverState.enter ? hoverState.hoverItem.id : hoverState.hoverItem.parent;
    if (hoverState.enter) {
      to = hoverState.hoverItem.id;
    } else {
      to = hoverState.hoverItem.parent;
      if (workspaceDocs.items.find(item => item.hid === hoverState.hoverItem)
        || privateDocs.items.find(item => item.hid == hoverState.hoverItem)
        || sharedDocs.items.find(item => item.hid == hoverState.hoverItem)) {
        to = 'root';
      }
    }

    let newPosition = hoverState.enter ? 0 : (hoverState.hoverItem.index + hoverState.leave);
    let orderFactor = calcOrderFactor(to, newPosition, space);

    if (from === to && space === hoverState.dragItem.space) {
      if (hoverState.dragItem.index < newPosition) {
        newPosition = newPosition - 1;
      }

      if (newPosition === hoverState.dragItem.index) {
        setHoverState(hover_state_init);
        return;
      }
    }

    if (isAncestor(hoverState.dragItem.id, to)) {
      setHoverState(hover_state_init);
      return;
    }

    let targetParams = {
      hid: hoverState.dragItem.id,
      orderFactor
    }

    if (!checkMoveDocPermission(targetParams.hid, from, to)) {
      setHoverState(hover_state_init);
      return;
    }

    if (space !== 'shared' || to !== 'root') {
      dispatch(transferDoc({ doc: targetParams, from, to, fromSpace: hoverState.dragItem.space, toSpace: space, orgId: loginUser.workingOrgId }, (updatedDoc) => {
        dispatch({
          type: DOC_ACTIONS.grantedPermission,
          hid: updatedDoc.hid,
          grantedTo: updatedDoc.grantedTo
        });

        if (updatedDoc.parent === 'root') {
          return;
        }

        const parentDoc = docRefs.get(updatedDoc.parent);
        if (parentDoc.children && parentDoc.children.length) {
          return;
        }

        const isGrandParentRoot = parentDoc.parent === 'root';
        let action_type = SUB_DOCS_ACTIONS.updated;
        if (isGrandParentRoot) {
          if (space === 'workspace') {
            action_type = WORKSPACE_DOCS_ACTIONS.updated;
          } else if (space === 'private') {
            action_type = PRIVATE_DOCS_ACTIONS.updated;
          } else {
            action_type = SHARED_DOCS_ACTIONS.updated;
          }
        }

        dispatch({
          _id: parentDoc._id,
          item: Object.assign({}, parentDoc, { children: [updatedDoc.hid] }),
          params: { ...targetParams, pageBy: 'orderFactor' },
          type: action_type,
          key: isGrandParentRoot ? loginUser.workingOrgId : parentDoc.parent
        });

      }, 'appsidebar'))
    }

    setHoverState(hover_state_init);
  }

  const openSubMenu = (hid, open) => {
    setSubMenuOpen(state => {
      let state_copy = { ...state };
      state_copy[hid] = open;
      return state_copy
    });
  }

  const makeMenuItem = (doc, index, space) => {
    if (!doc) {
      return;
    }

    docRefs.set(doc.hid, doc);

    const insertBehind = space !== 'shared' && hoverState.space === space && hoverState.hoverItem && !hoverState.enter && ((doc.parent === hoverState.hoverItem.parent) && (index === hoverState.hoverItem.index + hoverState.leave - 1));
    const itemTargeted = hoverState.hoverItem && hoverState.enter && (doc.hid === hoverState.hoverItem.id);

    let linkTo = linkToPage(doc, { space });

    const title = <DraggableCard
      itemData={{
        index,
        id: doc.hid,
        space,
        parent: doc.parent
      }}

      onDrop={() => {
        onDrop(space);
      }}

      onDroppedOutside={() => {
        setHoverState(hover_state_init);
      }}

      itemTargeted={itemTargeted}
      enterItem={(dragItem, item) => {
        if (doc.type === 'slides' || doc.type === 'db' || doc.type === 'flow') {
          return;
        }
        if (hoverState.dragItem.id === dragItem.id && hoverState.hoverItem.id === item.id && hoverState.hoverItem.enter) {
          return;
        }

        setHoverState({
          space,
          dragItem,
          hoverItem: item,
          enter: true,
          leave: -1
        })
      }}
      leaveItem={(dragItem, item, direction) => {
        if (hoverState.dragItem.id === dragItem.id && hoverState.hoverItem.id === item.id && hoverState.leave === direction) {
          return;
        }
        setHoverState({
          space,
          dragItem,
          hoverItem: item,
          enter: false,
          leave: direction,
        })
      }}
    >
      <Link
        className={doc.hid === activePage ? "active" : null}
        to={linkTo}>
        {doc.title || 'Untitled'}
      </Link>
    </DraggableCard>;

    let component;
    let subDocs = getState(sub_doc_lists, doc.hid);

    if (doc.children && doc.children.length) {
      component = <ContextMenuTrigger id="same_unique_identifier"
        holdToDisplay={-1}
        collect={() => {
          return { hid: doc.hid, parent: doc.parent, title: doc.title, space };
        }}
      >
        <SubMenu
          key={doc.hid}
          prefix={<FileText size={18} />}
          title={title}
          style={{ backgroundColor: doc.hid === activePage ? 'rgba(211, 211, 211, 0.5)' : 'transparent' }}
          onOpenChange={() => {
            if (!subMenuOpen[doc.hid]) {
              setFetchProps(prevState => {
                return {
                  ...prevState,
                  params: {
                    orgId: loginUser.workingOrgId,
                    pageBy: 'orderFactor',
                    parent: doc.hid
                  },
                  data_list: sub_doc_lists,
                  fetcher: fetchSubDocs
                }
              })
            }
            openSubMenu(doc.hid, !subMenuOpen[doc.hid]);
          }}
          open={subMenuOpen[doc.hid]}
        >
          <DroppableContainer onDrop={() => onDrop(space)}>
            {
              !!subDocs && subDocs.items.map((item, i) => {
                return makeMenuItem(item, i, space);
              })
            }
            {
              !!subDocs && !subDocs.isEnd &&
              <LoadMoreBtn
                fetcher={fetchSubDocs}
                data_list={subDocs}
                parent={doc.hid}
              />
            }
          </DroppableContainer>
        </SubMenu>
      </ContextMenuTrigger>
    } else {
      component = <ContextMenuTrigger id="same_unique_identifier"
        holdToDisplay={-1}
        collect={() => {
          return { hid: doc.hid, parent: doc.parent, title: doc.title, space };
        }}
      >
        <MenuItem
          key={doc.hid}
          prefix={doc.type == 'flow' && <FileMedical size={18} /> || doc.type === 'slides' && <FileSlides size={18} /> || doc.type === 'db' && <FileSpreadsheet size={18} /> || <FileText size={18} />}
          style={{ backgroundColor: doc.hid === activePage ? 'rgba(211, 211, 211, 0.5)' : 'transparent' }}
        >
          {title}
        </MenuItem>
      </ContextMenuTrigger>;
    }

    return <div key={doc.hid}>
      {component}
      {insertBehind && <div style={{ width: '100%', height: '4px', backgroundColor: 'lightblue' }}></div>}
    </div>
  }

  const [docTreeWorkSpace, setDocTreeWorkSpace] = useState();
  const [docTreeShared, setDocTreeShared] = useState();
  const [docTreePrivate, setDocTreePrivate] = useState();

  const LoadMoreBtn = useMemo(() => ({ data_list, parent, fetcher }) => {
    return <div style={{
      color: '#999'
    }}> <div
      className="hoverStand1"
      style={{
        marginLeft: '10px',
        padding: 4,
        paddingLeft: 6,
        fontSize: 13,
        borderRadius: 4,
      }}
      onClick={() => handleLoadMore(data_list, fetcher, parent)}
    >
        {intl.formatMessage({ id: 'loadmore' })}
      </div>
    </div>
  }, []);


  useEffect(() => {
    const makeDocTree = (rootList, fetcher, space) => {
      let children = <>
        {
          rootList.items.map((item, index) => {
            return makeMenuItem(item, index, space);
          }).filter(item => !!item)
        }
        {
          !rootList.isEnd &&
          <LoadMoreBtn
            fetcher={fetcher}
            data_list={rootList}
            parent={'root'}
          />
        }
      </>;

      if (space === 'shared') {
        return children
      }

      return <DroppableContainer onDrop={() => onDrop(space)}>
        {children}
      </DroppableContainer>
    }

    setDocTreeWorkSpace(makeDocTree(workspaceDocs, fetchWorkSpaceDocs, 'workspace'));
    setDocTreePrivate(makeDocTree(privateDocs, fetchPrivateDocs, 'private'));
    setDocTreeShared(makeDocTree(sharedDocs, fetchSharedDocs, 'shared'));
  }, [sub_doc_lists, workspaceDocs, privateDocs, sharedDocs, hoverState, subMenuOpen, activePage])


  const loadDefaultPage = useCallback((workspaceDocs, sharedDocs, privateDocs) => {
    let firstPage;
    let space;
    if (workspaceDocs && workspaceDocs.items && workspaceDocs.items.length > 0) {
      firstPage = workspaceDocs.items[0];
      space = 'workspace';
    } else if (privateDocs && privateDocs.items && privateDocs.items.length > 0) {
      firstPage = privateDocs.items[0];
      space = 'private';
    } else if (sharedDocs && sharedDocs.items && sharedDocs.items.length > 0) {
      firstPage = sharedDocs.items[0];
      space = 'shared';
    }

    if (!firstPage) {
      return;
    }

    history.push(linkToPage(firstPage, { space }));
  }, []);

  useEffect(() => {
    if (currentLocation.pathname != '/') return;

    loadDefaultPage(workspaceDocs, sharedDocs, privateDocs);
  }, [workspaceDocs, privateDocs, sharedDocs, currentLocation.pathname]);

  useEffect(() => {
    if (['/article', '/webpager', '/noaccess', '/sharedPrompt', '/slidesEditor'].includes(currentLocation.pathname)) return;

    if (!params.hid || (!docs.byId[params.hid] || docs.byId[params.hid] && docs.byId[params.hid].orgId !== loginUser.workingOrgId)) {
      setTimeout(() => {
        history.push('/');
      }, 100);
    }
  }, [loginUser.workingOrgId]);

  useEffect(() => {
    currentDocPath.map(path => openSubMenu(path.hid, true))
  }, [currentDocPath])

  const [trashBinOpen, setTrashBinOpen] = React.useState(false);

  const addToRil = (event) => {
    dispatch({ type: RIL_DIALOG, value: { visible: true, url: '' } });
    event.preventDefault();
    event.stopPropagation();
  }

  const checkMoveDocPermission = useCallback((hid, parent, to) => {
    let parentDoc = docRefs.get(parent);
    let doc = docRefs.get(hid);
    let toDoc = to && docRefs.get(to);
    // console.log('check move doc permission..................', parentDoc, doc, toDoc, docRefs);

    if (doc && doc.permission < DOC_PERMISSION.edit || !!parentDoc && parentDoc.permission < DOC_PERMISSION.edit || !!toDoc && toDoc.permission < DOC_PERMISSION.edit) {
      dispatch({
        type: OPERATION_SUCCESS,
        message: 'Permission denied',
        navBack: false,
        screen: 'appsidebar'
      });

      return false;
    }

    return true;
  }, [docRefs.size]);

  const [renameState, setRenameState] = useState({
    data: {},
    anchorEl: null,
  });

  const contextMenuItems = useMemo(() => {
    return [
      {
        text: intl.formatMessage({ id: 'delete' }),
        icon: <Trash size={18} />,
        onClick: (e, data) => { dispatch(trashDoc({ hid: data.hid, parent: data.parent, orgId: loginUser.workingOrgId }, null, 'appsidebar')); }
      },
      {
        text: intl.formatMessage({ id: 'duplicate' }),
        icon: <FileCopy size={18} />,
        onClick: (e, data) => { dispatch(duplicateDoc({ hid: data.hid }, null, 'appsidebar')); }
      },
      {
        text: intl.formatMessage({ id: 'rename' }),
        icon: <PencilSquare size={16} />,
        onClick: (e, data) => {
          setRenameState({
            data: {
              hid: data.hid,
              title: data.title,
              parent: data.parent,
              space: data.space,
            },
            anchorEl: e.currentTarget,
          });
        }
      },
    ];
  }, [intl, loginUser.workingOrgId]);

  const rename = useCallback((hid, name, space, parent) => {
    if (!name) {
      return;
    }

    dispatch(upsertDoc({
      data: {
        doc: {
          hid,
          title: name,
          parent
        },
        space,
        orgId: loginUser.workingOrgId,
      }
    }, null, false, 'appsidebar'));

    setRenameState({
      data: {},
      anchorEl: null,
    });

  }, [loginUser.workingOrgId]);

  const addRilBtn = useMemo(() => <div style={{ alignItems: 'center' }}>
    <Tooltip title={intl.formatMessage({ id: 'add_ril_tooltip' })} arrow placement="top">
      <div
        onClick={addToRil}
        className='btn-transparent hoverStand1'
        style={{ padding: '2px 4px', margin: '0px 10px', borderRadius: '4px' }}
      >
        <AddSquareMultiple size={28} />
      </div>
    </Tooltip>
  </div>, [intl])

  return (
    <ProSidebar
      // image={image ? sidebarBg : false}
      rtl={rtl}
      collapsed={!sidebarShow}
      toggled={true}
      breakPoint="md"
      onToggle={handleToggleSidebar}
      style={{ display: 'flex', flexDirection: 'column', justifyContent: 'space-between' }}

      onMouseOver={() => setShowSidebarSwitch(true)}
      onMouseLeave={() => setShowSidebarSwitch(false)}
    >
      <SidebarHeader>
        <Profile showSidebarSwitch={showSidebarSwitch} />
        <Tooltip title={!sidebarShow && intl.formatMessage({ id: 'settings' })}>
          <div style={{
            paddingLeft: sidebarShow?  10 : 0,
            paddingBottom: 10,
            paddingTop: 4,
            cursor: 'pointer',
            display: 'flex',
            whiteSpace: 'nowrap',
            alignItems: 'center',
            justifyContent: !sidebarShow ? 'center' : undefined,
            fontSize: 14,
            color: '#777'
          }}
            onClick={openSpaceSettings}
          >
            <Settings size={18} style={{ paddingRight: 5 }} /> {sidebarShow && intl.formatMessage({ id: 'settings' })}
          </div>
        </Tooltip>
      </SidebarHeader>
      <SidebarContent
        style={{
          flexGrow: '1',
          overflow: 'auto',
        }}
      >
        <Menu iconShape="circle"
          subMenuBullets={false}
          innerSubMenuArrows={true}
        >
          <SubMenu
            onMouseOver={() => setShowAddDocBtn(sidebarShow)}
            onMouseLeave={() => setShowAddDocBtn(false)}
            suffix={
              showAddDocBtn &&
              <div style={{ alignItems: 'center' }}>
                <Tooltip title={intl.formatMessage({ id: 'new_document_workspace' })} arrow placement="top">
                  <div
                    onClick={(event) => addNewPage(event, 'workspace')}
                    className='btn-transparent hoverStand1'
                    style={{ padding: '4px 8px', margin: '0px 16px', borderRadius: '4px' }}
                  >
                    <FaPlus size={14} />
                  </div>
                </Tooltip>
              </div>}
            title={
              <Tooltip title={intl.formatMessage({ id: 'workspace_tooltip' })} placement="bottom-start">
                <div style={{ color: 'gray' }}>
                  {intl.formatMessage({ id: 'workspace' }).substring(0, sidebarShow? undefined: 1)}
                </div>
              </Tooltip>
            }
            defaultOpen={true}
          >
            <div style={{ flexGrow: '1', overflow: 'auto' }}>
              {
                docTreeWorkSpace
              }
            </div>
          </SubMenu>
        </Menu>

        {
          sharedDocs.items.length > 0 &&
          <Menu iconShape="circle"
            subMenuBullets={false}
            innerSubMenuArrows={true}
          >
            <SubMenu
              title={
                <Tooltip title={intl.formatMessage({ id: 'shared_tooltip' })} placement="bottom-start">
                  <div style={{ color: 'gray' }}>
                    {intl.formatMessage({ id: 'shared' }).substring(0, sidebarShow? undefined: 1)}
                  </div>
                </Tooltip>
              }
              defaultOpen={true}
            >
              <div style={{ flexGrow: '1', overflow: 'auto' }}>
                {
                  docTreeShared
                }
              </div>
            </SubMenu>
          </Menu>
        }

        <Menu iconShape="circle"
          subMenuBullets={false}
          innerSubMenuArrows={true}
        >
          <SubMenu
            onMouseOver={() => setShowAddDocBtn(sidebarShow)}
            onMouseLeave={() => setShowAddDocBtn(false)}
            suffix={
              showAddDocBtn &&
              <div style={{ alignItems: 'center' }}>
                <Tooltip title={intl.formatMessage({ id: 'new_document_private' })} arrow placement="top">
                  <div
                    onClick={(event) => addNewPage(event, 'private')}
                    className='btn-transparent hoverStand1'
                    style={{ padding: '4px 8px', margin: '0px 16px', borderRadius: '4px' }}
                  >
                    <FaPlus size={14} />
                  </div>
                </Tooltip>
              </div>
            }
            title={
              <Tooltip title={intl.formatMessage({ id: 'private_tooltip' })} placement="bottom-start">
                <div style={{ color: 'gray' }}>
                  {intl.formatMessage({ id: 'private' }).substring(0, sidebarShow? undefined: 1)}
                </div>
              </Tooltip>
            }
            defaultOpen={true}
          >
            <div style={{ flexGrow: '1', overflow: 'auto' }}>
              {
                docTreePrivate
              }
            </div>
          </SubMenu>
        </Menu>

        {
          isCNDomain() &&
          loginUser.rilBindedOrgId === loginUser.workingOrgId &&
          <Menu style={{ flexShrink: '0' }}
            iconShape="circle"
          >
            {
              !sidebarShow && addRilBtn
            }
            {
              sidebarShow &&
              <MenuItem
                suffix={addRilBtn}
                onClick={() => {
                  dispatch({ type: APP_LIST, value: '/rils' });
                }}
              >
                <Tooltip title={intl.formatMessage({ id: 'ril_tooltip' })} placement="bottom-start">
                  <div>
                    {intl.formatMessage({ id: 'ril' })}
                  </div>
                </Tooltip>
              </MenuItem>
            }
          </Menu>
        }
      </SidebarContent>

      {
        sidebarShow &&
        <SidebarFooter style={{ textAlign: 'center', flexShrink: '0' }}>
          <div
            className="sidebar-footer-wrapper"
            style={{ display: 'flex', flexDirection: 'row', color: '#555', fontSize: 14 }}
          >
            <DroppableContainer
              dragOverColor={'#ffdead'}
              dragOverView={<span style={{ textAlign: 'center' }}>{intl.formatMessage({ id: 'release_to_delete' })}</span>}
              style={{
                width: '100%',
                height: '100%',
                display: 'flex',
                alignItems: 'center',
                flex: 1
              }}
              onDrop={(item) => {
                if (!checkMoveDocPermission(item.id, item.parent, null)) {
                  return;
                }
                dispatch(trashDoc({ hid: item.id, parent: item.parent, orgId: loginUser.workingOrgId }, null, 'appsidebar'));
                setHoverState(hover_state_init);
              }}
            >
              <Tooltip title={intl.formatMessage({ id: 'trashbin_tooltip' })} placement="bottom-start">
                <div
                  className='hoverStand1'
                  style={{
                    columnGap: 4,
                    height: '-webkit-fill-available',
                    width: '100%',
                    alignItems: 'center',
                    justifyContent: 'center',
                    flex: 1
                  }}
                  onClick={() => setTrashBinOpen(true)}
                >
                  <Trash size={22} />
                  {intl.formatMessage({ id: 'trashbin' })}
                </div>
              </Tooltip>
            </DroppableContainer>
            <Divider orientation="vertical" style={{
              borderColor: 'rgba(85, 85, 85, 0.14)'
            }}/>
            <Tooltip title={intl.formatMessage({ id: 'new_document_private' })} arrow placement="top">
              <div
                className='hoverStand1'
                style={{
                  columnGap: 4,
                  height: '-webkit-fill-available',
                  width: '100%',
                  alignItems: 'center',
                  justifyContent: 'center',
                  flex: 1
                }}
                onClick={(event) => addNewPage(event, 'private')}
              >
                <FilePlus size={22} />
                {intl.formatMessage({ id: 'new_page' })}
              </div>
            </Tooltip>
          </div>
          <DeletedDocsDialog
            open={trashBinOpen}
            handleClose={() => setTrashBinOpen(false)}
          />
          <ContextMenu id="same_unique_identifier">
            {
              contextMenuItems.map((item, index) => {
                return (
                  <CMMenuItem key={index} onClick={item.onClick}>
                    <div style={{ display: 'flex', alignItems: 'center', flexDirection: 'row' }}>
                      <div style={{ display: 'flex', alignItems: 'center', flexDirection: 'row', marginRight: 6 }}>
                        {item.icon}
                      </div>
                      {item.text}
                    </div>
                  </CMMenuItem>
                );
              })
            }
          </ContextMenu>

          <InputModal
            value={renameState.data.title || ''}
            anchorEl={renameState.anchorEl}
            onClose={() => setRenameState({ ...renameState, anchorEl: null })}
            onChange={(e) => {
              setRenameState({ ...renameState, data: { ...renameState.data, title: e.target.value } });
            }}
            onSubmit={() => rename(renameState.data.hid, renameState.data.title, renameState.data.space, renameState.data.parent)}
          />
        </SidebarFooter>
      }
    </ProSidebar>
  );
};


export default AppSidebar
