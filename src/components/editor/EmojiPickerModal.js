import * as React from 'react';
import { EMOJI_PICKER_DIALOG, REFRESH_DOC } from 'src/constants/actionTypes';
import { useDispatch, useSelector } from 'react-redux';
import { DialogContent, Dialog, Button, DialogTitle, DialogActions } from '@mui/material';
import { useIntl } from 'react-intl';
import {

    EmojiSettings,
    FrequentEmojiStorage,
    EmojiFloatingLibrary,
    EmojiFloatingIndexSearch,
    useEmojiPicker
} from '@udecode/plate-emoji';
import {
    EmojiPicker, 
} from '../plate-ui/emoji-picker';
import { emojiCategoryIcons, emojiSearchIcons } from '../plate-ui/emoji-icons';
import { useEditorRef } from '@udecode/plate-common';


const EmojiPickerModal = () => {
    const intl = useIntl();
    const dialogState = useSelector(state => state.uiState.emojiPickerDialog) || { visible: false };
    const { visible } = dialogState;
    const dispatch = useDispatch();
    const editor = useEditorRef();

    const handleClose = () => {
        dispatch({ type: EMOJI_PICKER_DIALOG, value: { visible: false } });
    }

    const emojiFloatingLibraryRef = React.useRef();
    const emojiFloatingIndexSearchRef = React.useRef();

    React.useEffect(() => {
        const frequentEmojiStorage = new FrequentEmojiStorage({
            limit: EmojiSettings.showFrequent.limit,
        });
        emojiFloatingLibraryRef.current = EmojiFloatingLibrary.getInstance(
            EmojiSettings,
            frequentEmojiStorage
        );

        emojiFloatingIndexSearchRef.current = EmojiFloatingIndexSearch.getInstance(
            emojiFloatingLibraryRef.current
        );
    }, []);

    const emojiPicker = useEmojiPicker({
        closeOnSelect: true,
        editor,
        emojiLibrary: emojiFloatingLibraryRef.current,
        indexSearch: emojiFloatingIndexSearchRef.current,
    });

    React.useEffect(() => {
        if (!emojiPicker.isOpen) {
            dispatch({
                type: EMOJI_PICKER_DIALOG,
                value: { visible: false }
            })
        }
    }, [emojiPicker.isOpen])

    React.useEffect(() => {
        if (dialogState.visible) {
            emojiPicker.setIsOpen(dialogState.visible)
        }
    }, [dialogState])

    return (
        <Dialog
            open={visible}
            onClose={handleClose}
            scroll='paper'
            aria-labelledby="scroll-dialog-title"
            aria-describedby="scroll-dialog-description"
            maxWidth='lg'
            style={{
                zIndex: 100,
            }}
        >
            <DialogContent dividers={true} style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', minHeight: 300, minHeight: 300, padding: 0 }}>
                {
                    emojiPicker.emojiLibrary &&
                    <EmojiPicker
                        {...emojiPicker}
                        isOpen={emojiPicker.isOpen}
                        onToggle={
                            emojiPicker.onToggle
                        }
                        icons={{
                            categories: emojiCategoryIcons,
                            search: emojiSearchIcons,
                        }}
                        settings={EmojiSettings}
                    />
                }
            </DialogContent>
        </Dialog>
    );
}

export default EmojiPickerModal;
