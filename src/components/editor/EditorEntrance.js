import { useCallback, useEffect, useRef, useState } from "react";
import { with<PERSON><PERSON><PERSON>, use<PERSON>ara<PERSON>, useHistory, useLocation } from "react-router-dom";
import { TitleEditor } from "src/components/common/TitleEditor";
import { useIntl } from "react-intl";
import DocEditor from "src/components/editor/DocEditor";
import { VIEW_LAYOUTS } from "src/constants/constants";
import { useDispatch, useSelector } from "react-redux";
import { getDoc, newDoc } from "src/actions/ticketAction";
import { FileText } from "@styled-icons/bootstrap/FileText";
import { FileSlides } from "@styled-icons/bootstrap/FileSlides";
import { FileMedical } from '@styled-icons/bootstrap/FileMedical';
import { Import } from '@styled-icons/boxicons-regular/Import';
import { ALERT_DIALOG, IMPORT_DOC_DIALOG, PROMPTS_DIALOG, REFRESH_EDITOR_CONTENT, SHOW_APP_LIST } from "src/constants/actionTypes";
import { linkToPage } from "src/utils/PageLinkMaker";
import LoadingScreen from "../LoadingScreen";
import { Magic } from "@styled-icons/bootstrap";
import { PromptsModalDataHandler } from "../settings/PromptsModalDataHandler";
import { Button } from "@mui/material";

const EditorEntrance = (props) => {
    const intl = useIntl();
    const dispatch = useDispatch();
    const history = useHistory();

    const { hid, space, orderFactor, mode } = props;
    const docs = useSelector(state => state.docs);
    const doc = docs.byId[hid];

    const loginUser = useSelector(state => state.loginIn.user);
    const db_enabled = useSelector(state => state.uiState.db_enabled);
    const promptsDialogState = useSelector(state => state.uiState.promptsDialog) || {};

    const [title, setTitle] = useState('');
    const [docLoading, setDocLoading] = useState(false);
    const [activeTab, setActiveTab] = useState('document');
    const [selectedOption, setSelectedOption] = useState(null);
    const [hoveredOption, setHoveredOption] = useState(null);
    const [hoveredTab, setHoveredTab] = useState(null);

    // 分类选项
    const tabOptions = {
        document: {
            label: intl?.formatMessage({ id: 'document_section_title' }) || 'Document',
            options: [
                {
                    id: 'empty_page',
                    label: intl?.formatMessage({ id: 'empty_page' }) || 'Empty Page',
                    icon: <FileText size={18} />,
                    onClick: () => setNewPageParams({ type: 'doc' }),
                    isDefault: true
                },
                {
                    id: 'import',
                    label: intl?.formatMessage({ id: 'import' }) || 'Import',
                    icon: <Import size={18} />,
                    onClick: () => {
                        dispatch({
                            type: IMPORT_DOC_DIALOG,
                            value: {
                                visible: true,
                                space,
                                hid
                            }
                        })
                    }
                },
                {
                    id: 'empty_page_with_ai',
                    label: intl?.formatMessage({ id: 'empty_page_with_ai' }) || 'Empty Page with AI',
                    icon: <Magic color="#1890ff" size={18} />,
                    // onClick: () => newPageWithAI()
                    onClick: () => setNewPageParams({ type: 'doc', withAI: true })
                },
            ]
        },
        slides: {
            label: intl?.formatMessage({ id: 'slides_section_title' }) || 'Slides',
            options: [
                {
                    id: 'slides',
                    label: intl?.formatMessage({ id: 'empty_page' }) || 'Empty',
                    icon: <FileSlides size={18} />,
                    onClick: () => setNewPageParams({ type: 'slides' }),
                    isDefault: true
                },
                {
                    id: 'empty_slides_with_ai',
                    label: intl?.formatMessage({ id: 'empty_slides_with_ai' }) || 'Slides with AI',
                    icon: <Magic color="#1890ff" size={18} />,
                    onClick: () => setNewPageParams({ type: 'slides', withAI: true })
                }
            ]
        },
        flow: {
            label: intl?.formatMessage({ id: 'flow_section_title' }) || 'Flow',
            options: [
                {
                    id: 'flow',
                    label: intl?.formatMessage({ id: 'flow' }) || 'Flow',
                    icon: <FileMedical size={18} />,
                    onClick: () => setNewPageParams({ type: 'flow' }),
                    isDefault: true
                }
            ]
        }
    };

    const [newPagePararms, setNewPageParams] = useState();
    const title_ref = useRef();

    const newPage = ({ title, type, view, withAI, action, sub_item }) => {
        let newdoc = {
            parent: doc?.parent || 'root',
            orderFactor: orderFactor || doc?.orderFactor,
            title,
            type,
            hid
        };

        if (type === 'slides') {
            newdoc.markdown = `# ${title}`;
        }

        dispatch(newDoc({
            data: {
                space,
                orgId: loginUser.workingOrgId,
                doc: newdoc,
                view,
                isBlank: true
            }
        }, (doc) => {
            setTitle('');
            if (type === 'slides') {
                history.push({ pathname: '/slidesEditor', state: { hid: doc.hid, hideHeader: true, withAI } });
            } else {
                history.push(linkToPage(doc, { space, withAI, action, sub_item }));
            }
        }, 'editor'));
    }

    const handleNewPageCreation = useCallback(() => {
        if (!newPagePararms) {
            return;
        }

        newPagePararms.title = title;

        if (newPagePararms.type === 'doc' && newPagePararms.withAI) {
            newPageWithAI();
            return;
        }

        newPage(newPagePararms);
    }, [newPagePararms, title]);

    const newPageWithAI = () => {
        dispatch({
            type: PROMPTS_DIALOG,
            value: {
                visible: true,
                title,
                space
            }
        })
    }

    useEffect(() => {
        if (hid) {
            setDocLoading(true);
            dispatch(getDoc({ hid: hid }, (doc) => {
                setDocLoading(false);
                dispatch({ type: REFRESH_EDITOR_CONTENT, value: hid });
            }, (doc) => {
                setDocLoading(false);
                dispatch({ type: REFRESH_EDITOR_CONTENT, value: hid });
            }, 'editor'));
            dispatch({ type: SHOW_APP_LIST, value: false })
        }
    }, [hid]);

    useEffect(() => {
        setTitle(props.title || doc?.title || '');
    }, [props.title, doc]);


    useEffect(() => {
        setSelectedOption(tabOptions['document'].options.find(option => option.isDefault));
        tabOptions['document'].options.find(option => option.isDefault).onClick();
    }, []);

    // 如果启用数据库，添加数据库选项
    if (db_enabled) {
        tabOptions.database = {
            label: intl?.formatMessage({ id: 'db_section_title' }) || 'Database',
            options: VIEW_LAYOUTS.map(l => ({
                id: l.name,
                label: l.label,
                icon: l.icon,
                onClick: () => setNewPageParams({ type: 'db', view: l.name }),
                isDefault: l.name === 'table'
            }))
        };
    }

    const handleKeyDown = (e) => {
        if (promptsDialogState.visible) return;

        if (e.key === 'Enter') {
            if (selectedOption) {
                selectedOption.onClick();
            } else {
                // 默认创建空白文档
                newPage({ type: "doc" });
            }
            e.preventDefault();
            e.stopPropagation();
        }
    };

    if (hid && doc?.blocks?.length) {
        return <DocEditor
            hid={hid}
            space={space}
            forceTitle={true}
            autoFocus={false}
        />
    }

    if (hid && docLoading) {
        return <LoadingScreen />
    }

    const currentTabOptions = tabOptions[activeTab]?.options || [];

    return (
        <div
            style={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                minHeight: '60vh',
                maxWidth: '900px',
                margin: '0 auto',
                padding: '40px 20px'
            }}
        // onKeyDown={handleKeyDown}
        >
            {/* Tab 导航 */}
            <div style={{
                width: '100%',
                minWidth: '700px',
                marginBottom: '32px'
            }}>
                <div style={{
                    display: 'flex',
                    justifyContent: 'center',
                    borderBottom: '2px solid #f3f4f6',
                    marginBottom: '24px'
                }}>
                    {Object.entries(tabOptions).map(([key, tab]) => (
                        <button
                            key={key}
                            onClick={() => {
                                setActiveTab(key);

                                setSelectedOption(tabOptions[key].options.find(option => option.isDefault));
                                tabOptions[key].options.find(option => option.isDefault).onClick();
                            }}
                            onMouseEnter={() => setHoveredTab(key)}
                            onMouseLeave={() => setHoveredTab(null)}
                            style={{
                                padding: '12px 24px',
                                fontSize: '16px',
                                fontWeight: '500',
                                border: 'none',
                                background: 'transparent',
                                cursor: 'pointer',
                                borderBottom: activeTab === key ? '2px solid #3b82f6' : '2px solid transparent',
                                color: activeTab === key ? '#3b82f6' : '#6b7280',
                                transition: 'all 0.2s ease-in-out',
                                borderRadius: '8px 8px 0 0'
                            }}
                        >
                            {tab.label}
                        </button>
                    ))}
                </div>

                {/* 选项按钮 - 横向网格布局 */}
                <div style={{
                    display: 'grid',
                    gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
                    gap: '16px',
                    width: '100%'
                }}>
                    {currentTabOptions.map((option) => (
                        <button
                            key={option.id}
                            onClick={() => {
                                setSelectedOption(option);
                                option.onClick();
                            }}
                            onMouseEnter={() => setHoveredOption(option.id)}
                            onMouseLeave={() => setHoveredOption(null)}
                            style={{
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'flex-start',
                                padding: '10px 14px',
                                backgroundColor: selectedOption?.id === option.id ? '#eff6ff' :
                                    hoveredOption === option.id ? '#f8fafc' : '#ffffff',
                                border: selectedOption?.id === option.id ? '1px solid #3b82f6' :
                                    hoveredOption === option.id ? '1px solid #3b82f6' : '1px solid #e5e7eb',
                                borderRadius: '12px',
                                cursor: 'pointer',
                                transition: 'all 0.2s ease-in-out',
                                fontSize: '15px',
                                fontWeight: '500',
                                color: '#374151',
                                gap: '12px',
                                minHeight: '44px',
                                boxShadow: hoveredOption === option.id ? '0 4px 6px -1px rgba(0, 0, 0, 0.1)' : '0 2px 4px -1px rgba(0, 0, 0, 0.06)',
                                transform: hoveredOption === option.id ? 'translateY(-1px)' : 'translateY(0)',
                                maxWidth: '340px'
                            }}
                        >
                            <div style={{
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                width: '32px',
                                height: '32px',
                                backgroundColor: '#f3f4f6',
                                borderRadius: '8px',
                                flexShrink: 0
                            }}>
                                {option.icon}
                            </div>
                            <span style={{ flex: 1, textAlign: 'left' }}>
                                {option.label}
                            </span>
                        </button>
                    ))}
                </div>

                {/* 标题输入框 - 居中对话框风格 */}
                <div style={{
                    width: '100%',
                    marginTop: '32px',
                    textAlign: 'center'
                }}>
                    {/* <h2 style={{
                        fontSize: '24px',
                        fontWeight: '600',
                        color: '#1f2937',
                        marginBottom: '30px'
                    }}>
                        {intl?.formatMessage({ id: 'create_new_page' }) || 'Create New Page'}
                    </h2> */}
                    <div style={{
                        background: '#ffffff',
                        border: '1px solid #e5e7eb',
                        borderRadius: '12px',
                        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                        transition: 'all 0.2s ease-in-out',
                        ':focus-within': {
                            borderColor: '#3b82f6',
                            boxShadow: '0 0 0 3px rgba(59, 130, 246, 0.1)'
                        }
                    }}>
                        <TitleEditor
                            containerStyle={{
                                paddingLeft: 0,
                                border: 'none',
                                outline: 'none',
                                fontSize: '18px',
                                fontWeight: 'normal',
                                padding: '16px 20px',
                                borderRadius: '8px'
                            }}
                            title={title}
                            inputRef={title_ref}
                            onChange={(value)  => { 
                                setTitle(value)
                            }}
                            // onKeyPress={handleKeyDown}
                            autoFocus={true}
                            placeholder={intl?.formatMessage({ id: 'enter_title' }) || 'Enter title...'}
                        />
                    </div>
                </div>

                <div style={{
                    width: '100%',
                    marginTop: '32px',
                    display: 'flex',
                    justifyContent: 'flex-end'
                }}>
                    <Button
                        variant="contained"
                        onClick={handleNewPageCreation}
                        disabled={!newPagePararms}
                    >
                        {intl?.formatMessage({ id: 'create' }) || 'Create'}
                    </Button>
                </div>
            </div>

            <PromptsModalDataHandler />
        </div>
    );
};

export default withRouter(EditorEntrance);