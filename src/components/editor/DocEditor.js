// import "../../scss/editor.css"
import { EditorCore } from "src/components";
import { with<PERSON><PERSON>er, useParams, useHistory, useLocation } from "react-router-dom";
import { useCallback, useEffect, useState, useRef, useMemo } from "react";
import { useDispatch, useSelector } from "react-redux";
import { getDoc, upsertDoc, newDoc, fetchTags, fetchComments, getPrompt } from "src/actions/ticketAction";
import { Alert } from "@mui/material";
import { DOCS_FOLDER, DOC_PERMISSION } from '../../constants/constants';
import RestoreDocButton from "src/components/RestoreDocButton";
import { SHOW_APP_LIST, REFRESH_EDITOR_CONTENT, TO_HTML, EDITOR_DIALOG, DB_EDITOR_DIALOG, AI_ASSISTANT_DIALOG, PROMPT_DIALOG } from "src/constants/actionTypes";
import { useIntl } from "react-intl";
import { getState, getStateByUser, tag_lists } from "src/reducers/listReducer";
import { TitleEditor } from "../common/TitleEditor";
import { ELEMENT_TAG } from 'src/plate-plugins/tags/core';
import { useEventEditorSelectors } from '@udecode/plate-common';

const DocEditor = ({ hid, space, forceTitle, dbDataId, saveOnBlockSwitch = true, autoFocus, toHtml }) => {
    const intl = useIntl();
    const initDoc = useCallback(() => {
        const id = new Date().getTime() + 1;

        return {
            hid: 'initDoc',
            dbDataId,
            blocks: [{ type: 'p', isInitBlock: true, id: id, children: [{ text: '' }] }],
            blockIds: [id],
        };
    }, [dbDataId]);

    const docs = useSelector(state => state.docs);
    const comments = useSelector(state => getState(state.comment_lists, hid));

    const currentLocation = useLocation();
    const params = new Proxy(new URLSearchParams(currentLocation.search), {
        get: (searchParams, prop) => searchParams.get(prop) || '',
    });

    const dispatch = useDispatch();
    const history = useHistory();

    const loginUser = useSelector(state => state.loginIn.user);
    const orgs = useSelector(state => getStateByUser(state.org_lists, loginUser));
    const workingSpace = orgs.items.find(org => org._id === loginUser.workingOrgId) || {};

    const contentRefresher = useSelector(state => state.uiState.contentRefresher);
    const docRefresher = useSelector(state => state.uiState.docRefresher);
    const docEditorRef = useRef();
    const titleEditorRef = useRef();
    const [keyDownFromTitle, setKeyDownFromTitle] = useState(null);

    const [doc, setDoc] = useState(docs.byId[hid] || {});
    const [title, setTitle] = useState('');

    const taglist = useSelector(state => getState(state.tag_lists, 'doc'));

    useEffect(() => {
        if (docs && hid && docs.byId[hid]) {
            setDoc(docs.byId[hid]);
            setTitle(docs.byId[hid].title || '');
        } else {
            setDoc(initDoc());
            setTitle('');
        }
    }, [hid, docs]);

    useEffect(() => {
        dispatch(fetchTags({ orgId: loginUser.workingOrgId, app: 'doc' }));
        // if (hid) {
        //     dispatch(fetchComments({ hid }));
        // }
    }, [hid, loginUser.workingOrgId]);

    const mentionables = useMemo(() => workingSpace.users?.filter(user => !!user.user).map(user => {
        return {
            key: user.user._id,
            text: user.user.nickname,
            data: { email: user.user.email }
        }
    }), [workingSpace.users]);
    const users = useMemo(() => {
        let arr = workingSpace.users?.filter(user => !!user.user).map(user => {
            return {
                id: user.user._id,
                name: user.user.nickname,
                avatarUrl: user.user.avatarURL
            }
        });

        let obj = arr?.reduce(function (acc, cur, i) {
            acc[cur.id] = cur;
            return acc;
        }, {});

        return obj;
    }, [workingSpace.users]);

    useEffect(() => {
        if (hid && docRefresher?.stamp) {
            dispatch(getDoc({ hid: hid }, (doc) => {
                dispatch({ type: REFRESH_EDITOR_CONTENT, value: hid });
            }, null, 'editor'));
        }
    }, [docRefresher]);

    const diff = (arr1, arr2) => arr1.filter(x => !arr2.includes(x));
    const equal = require('fast-deep-equal/react');


    const getDiffedBlocks = (newBlocks) => {
        let lastSavedBlockIds = doc.blocks ? doc.blocks.map(block => block.id) : [];
        let newBlockIds = newBlocks.map(block => block.id);
        let deletedBlockIds = diff(lastSavedBlockIds, newBlockIds);
        let addedBlockIds = diff(newBlockIds, lastSavedBlockIds);

        let updatedBlocks = newBlocks.filter(block => {
            let oldOne = doc.blocks && doc.blocks.find(oldBlock => oldBlock.id === block.id);
            if (!oldOne) {
                return false;
            }

            return !equal(oldOne, block);
        });

        console.log('find changes..........', { deletedBlockIds, addedBlockIds, updatedBlocks, oldBlocks: doc.blocks, newBlocks })
        return {
            deletedBlockIds,
            addedBlocks: addedBlockIds.map(id => newBlocks.find(newBlock => newBlock.id === id)),
            updatedBlocks,
            // shouldUpdateDocBlockChildren: !equal(newBlockIds, lastSavedBlockIds)
        }
    };

    const [noHistoryLogCount, setNoHistoryLogCount] = useState(0);

    const extractTagsFromBlock = (block, tags) => {
        if (block.type == ELEMENT_TAG) {
            if (!tags.includes(block.value)) {
                tags.push(block.value);
            }
        } else if (block.children) {
            block.children.forEach(child => {
                extractTagsFromBlock(child, tags);
            });
        }
    }

    const extractTags = (blocks) => {
        let tags = [];
        blocks.forEach(block => {
            extractTagsFromBlock(block, tags);
        });
        return tags;
    }

    const saveDoc = (value, logToHistory, showMessage) => {
        if (!value || value && value.length === 1 && value[0].isInitBlock && (!value[0].children || value[0].children[0].text === '')) {
            return;
        }

        const blocks = [...value].filter(block => !(!block.id && block.type === 'p'));
        const blockIdsMap = new Map();
        for (let i = 0; i < blocks.length; i++) {
            if (blockIdsMap.get(blocks[i].id)) {
                blocks[i] = { ...blocks[i], id: new Date().getTime() + Math.floor(Math.random() * 1000) };

                let prevBlockIndex = blockIdsMap.get(blocks[i].id) - 1;
                blocks[prevBlockIndex] = { ...blocks[prevBlockIndex], id: new Date().getTime() + Math.floor(Math.random() * 1000) };
                blockIdsMap.set(blocks[prevBlockIndex].id, prevBlockIndex + 1);
            }
            blockIdsMap.set(blocks[i].id, i + 1);
        }

        const blockIds = blocks.map(block => block.id || null);
        const { deletedBlockIds, addedBlocks, updatedBlocks } = getDiffedBlocks(blocks);
        // console.log('diff the updates.........', {
        //     deleted: deletedBlockIds.length,
        //     added: addedBlocks.length,
        //     updated: updatedBlocks.length,
        //     ids: blockIds,
        //     docIds: doc.blockIds,
        // })

        if (equal(blockIds, doc.blockIds) && !deletedBlockIds.length && !addedBlocks.length && !updatedBlocks.length) {
            return;
        }

        let tags = extractTags(blocks);
        const originalTags = extractTags(doc.blocks);

        if (equal(originalTags.sort(), tags.sort())) {
            tags = undefined;
        }

        let docData = { ...doc, tags };
        if (docData.hid === 'initDoc') {
            delete docData.hid;
        }

        delete docData.blocks
        docData.blockIds = blockIds;
        docData.title = title;

        const shouldLogToHistory = logToHistory || noHistoryLogCount > 6;
        if (shouldLogToHistory) {
            setNoHistoryLogCount(0);
        } else {
            setNoHistoryLogCount(noHistoryLogCount + 1);
        }

        dispatch(upsertDoc({ dbDataId, data: { doc: docData, deletedBlockIds, addedBlocks, updatedBlocks, space, logToHistory: shouldLogToHistory, orgId: loginUser.workingOrgId } }, (updatedDoc) => {
            console.log('doc saved');
            setDoc((prevDoc) => {
                if (prevDoc._id === updatedDoc._id) {
                    return {
                        ...updatedDoc,
                        blocks: value
                    }
                } else {
                    return prevDoc;
                }
            });

            if (toHtml) {
                dispatch({
                    type: TO_HTML,
                    value: {
                        hid: updatedDoc.hid,
                        time: new Date().getTime()
                    }
                })
            }
        }, showMessage, 'editor'))
    }

    const newSubDoc = (parent, insertNodeToDoc, type) => {
        dispatch(newDoc({ data: { doc: { parent, type }, } }, (subDoc) => {
            insertNodeToDoc(subDoc.hid);
            setTimeout(() => {
                if (type === 'slides') {
                    history.push({ pathname: '/slidesEditor', state: { hid: subDoc.hid, hideHeader: true } });
                } else if (type === 'flow') {
                    history.push('/flow?hid=' + subDoc.hid);
                } else if (type === 'db') {
                    // history.push('/db?hid=' + subDoc.hid);
                    // setTimeout(() => {
                    dispatch({
                        type: DB_EDITOR_DIALOG,
                        value: {
                            visible: true,
                            hid: subDoc.hid,
                            parent,
                        }
                    });
                    // }, 100);
                } else {
                    // history.push('/editor?hid=' + subDoc.hid);
                    // setTimeout(() => {
                    dispatch({
                        type: EDITOR_DIALOG,
                        value: {
                            visible: true,
                            hid: subDoc.hid,
                            parent,
                        }
                    });
                    // }, 100);
                }
            }, 300);
        }, 'editor'));
    };

    const [value, setValue] = useState(null);
    const editorId = (hid != 'initDoc') && hid || 'editor';

    const onBlurEditorId = useEventEditorSelectors.blur();
    const onFocusEditorId = useEventEditorSelectors.focus();

    useEffect(() => {
        if (saveDoc && (onBlurEditorId == 'editor' || onBlurEditorId?.startsWith && onBlurEditorId?.startsWith(hid)) && !onFocusEditorId) {
            !!value && saveDoc(value, true, false);
        }
    }, [onBlurEditorId, onFocusEditorId]);

    useEffect(() => {
        setValue(null);
    }, [hid])

    useEffect(() => {
        if (doc && doc.permission < 0) {
            history.push({ pathname: '/noaccess', search: currentLocation.search });
        }
    }, [doc && doc.permission]);

    useEffect(() => {
        if (!params.withAI || params.withAI === 'undefined') {
            return;
        }

        setTimeout(() => {
            dispatch({
                type: AI_ASSISTANT_DIALOG,
                value: { caller: 'plate', visible: true, trigger: 'entrance', selectedText: doc.title, action: params.action, sub_item: params.sub_item }
            });

            const searchParams = new URLSearchParams(currentLocation.search);
            searchParams.delete('withAI');
            searchParams.delete('action');
            searchParams.delete('sub_item');
            const newSearch = searchParams.toString();
            history.push({
                pathname: currentLocation.pathname,
                search: newSearch
            });
        }, 1000);
    }, [params.withAI])

    // console.log('render editor permission.....................', doc && doc.permission)

    // TODO: should be checked in Editor.js
    // if (doc.hid == 'initDoc') {
    //     return <Alert variant="filled" severity="warning">
    //         {intl.formatMessage({ id: 'doc_not_found' })}
    //     </Alert>
    // }

    const readOnly = doc.permission < DOC_PERMISSION.edit;

    let containerStyle = styles.container;
    if (readOnly) {
        containerStyle = {
            ...containerStyle,
            maxWidth: '800px',
            alignItems: 'center'
        }
    }

    return <div
        style={containerStyle}
    >
        {
            docs.byId[hid] && docs.byId[hid].folder === DOCS_FOLDER.trashbin &&
            <Alert variant="filled" severity="warning"
                action={
                    <RestoreDocButton item={{ hid, parent: docs.byId[hid].parent }} />
                }
            >
                {intl.formatMessage({ id: 'doc_in_trashbin' })}
            </Alert>
        }

        {
            forceTitle &&
            <TitleEditor
                containerStyle={{
                    paddingLeft: 0,
                    marginLeft: 10,
                }}
                title={title}
                doc={doc}
                space={space}
                inputRef={titleEditorRef}
                onChange={setTitle}
                onKeyPress={(e) => {
                    if (e.key === 'Enter') {
                        const currentPos = e.target.selectionStart;
                        let withText = e.target.value.substring(currentPos);
                        setTitle(e.target.value.substring(0, currentPos));

                        setTimeout(() => {
                            setKeyDownFromTitle({
                                key: e.key,
                                withText: withText
                            });
                        }, 50);

                        e.preventDefault();
                        e.stopPropagation();
                    }
                }}
                onKeyDown={(e) => {
                    if (e.key === 'ArrowDown' || (e.key === 'ArrowRight' && e.target.selectionStart === e.target.value.length)) {
                        e.preventDefault();
                        e.stopPropagation();

                        setKeyDownFromTitle({
                            key: e.key,
                            offset: e.key === 'ArrowDown' ? e.target.selectionStart : undefined
                        });
                    }
                }}
            />
        }

        {
            doc && doc.blocks && editorId &&
            <EditorCore
                content={doc.blocks}
                refresher={contentRefresher}
                hid={hid}
                editorId={editorId}
                onChange={setValue}
                forceTitle={false}
                autoFocus={autoFocus}
                saveOnBlockSwitch={saveOnBlockSwitch}

                readOnly={readOnly}
                saveDoc={saveDoc}
                newSubDoc={newSubDoc}
                mentionables={mentionables}
                users={users}
                userId={loginUser?._id}
                comments={comments?.items}
                tags={taglist.items}
                containerRef={docEditorRef}
                onNavigateUp={(pos, withText) => {
                    if (!forceTitle) {
                        return;
                    }

                    titleEditorRef.current.focus();
                    if (pos > -1) {
                        setTimeout(() => {
                            titleEditorRef.current.setSelectionRange(pos, pos);
                        }, 10)
                    } else {
                        const currentPos = title.length;

                        if (withText) {
                            setTitle(title => title + withText);
                        }

                        setTimeout(() => {
                            titleEditorRef.current.setSelectionRange(currentPos, currentPos);
                        }, 10);
                    }
                }}
                keyDownFromTitle={keyDownFromTitle}
                setKeyDownFromTitle={setKeyDownFromTitle}
            />
        }
    </div>;
};

const styles = {
    container: {
        display: 'flex',
        flexDirection: 'column',
        width: '100%',
        // maxWidth: '800px',
        // alignItems: 'center',
        justifyContent: 'center'
    }
};

export default withRouter(DocEditor);