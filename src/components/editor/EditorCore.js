import React, { useCallback, useEffect, useLayoutEffect, useMemo, useRef, useState } from 'react'
import { createCommandsPlugin, ELEMENT_COMMAND_INPUT } from '../../plate-plugins/plate-commands';
import { CommandsCombobox } from '../../plate-plugins/plate-ui-commands';
import { CommandItems, MarkBallonToolbar } from './config/components/Toolbars'
import { select, Transforms } from 'slate';
import { useDispatch, useSelector } from 'react-redux';
import { CURRENT_EDITOR, EMOJI_PICKER_DIALOG, OPERATION_FAILED, AI_ASSISTANT_DIALOG, IMAGE_UPLOAD_DIALOG, TEXT_SELECTION, TITLE_SELECTION, LINK_INPUT_DIALOG } from 'src/constants/actionTypes';
import { useIntl } from 'react-intl';
import { ReactEditor } from 'slate-react';
import { insertSubPageLink } from 'src/plate-plugins/sub-page-link/transforms/insertSubPageLink';
import { createTagPlugin, ELEMENT_TAG, ELEMENT_TAG_INPUT } from 'src/plate-plugins/tags/core';
import { TagCombobox, TagElement, TagInputElement } from 'src/plate-plugins/tags/ui';
import { deleteComment, upsertComment, upsertDoc } from 'src/actions/ticketAction';
import EmojiPickerModal from './EmojiPickerModal';
import { cloneDeep, map } from 'lodash';
import { Plate, usePlateStates, useEventEditorSelectors, setSelection, removeNodes, insertNodes, getNode, insertBreak, getNodeTexts, getParentNode, insertNode, setNodes, deleteText, useEditorState, usePlateActions, useReplaceEditor, selectEditor } from '@udecode/plate-common';
import { ELEMENT_PARAGRAPH } from '@udecode/plate-paragraph';
import { ELEMENT_H1, ELEMENT_H2, ELEMENT_H3, ELEMENT_H4, ELEMENT_H5, ELEMENT_H6 } from '@udecode/plate-heading';

import { insertImage } from '@udecode/plate-media';
import { MentionCombobox } from '@/components/plate-ui/mention-combobox';
import { FloatingToolbar } from '@/components/plate-ui/floating-toolbar';
import { FloatingToolbarButtons } from '@/components/plate-ui/floating-toolbar-buttons';
import { CursorOverlay } from '../plate-ui/cursor-overlay';
import { TooltipProvider } from '../plate-ui/tooltip';
import { plugins } from '@/lib/plate/plate-plugins';
import { Editor } from '../plate-ui/editor';
import { CommentsPopover } from '../plate-ui/comments-popover';
import { CommentsProvider } from '@udecode/plate-comments';
import { LinkInputModal } from '../common/LinkInputModal';
import { LinkModalDataHandler } from '../plate-ui/link-modal-data-handler';
import { AIModalDataHandler } from '../plate-ui/ai-modal-data-handler';
import enMessages from '../../locales/en';

const equal = require('fast-deep-equal/react');

const loadMessages = (locale) => {
  switch (locale) {
    case 'en':
      return import('../../locales/en');
    case 'cn':
      return import('../../locales/cn');
    case 'fr':
      return import('../../locales/fr');
    case 'de':
      return import('../../locales/de');
    case 'es':
      return import('../../locales/es');
    case 'pt':
      return import('../../locales/pt');
    case 'ja':
      return import('../../locales/ja');

    default:
      return import('../../locales/en');
  }
};

const EditorCore = (props) => {
  const { editorId, content, uploadImage, forceTitle, users, userId, hid, comments, readOnly, onChange } = props;
  const dispatch = useDispatch();
  const intl = useIntl();

  let commentsObj = useMemo(() => {
    const sortField = 'createdAt';
    let arr = comments?.map(comment => {
      comment.createdAt = new Date(comment.createdAt);
      return comment;
    }).sort((a, b) => new Date(a[sortField]) - new Date(b[sortField]));

    return arr?.reduce(function (acc, cur, i) {
      acc[cur.id] = cur;
      return acc;
    }, {})
  }, [comments]) || {};


  const commentUpsert = useCallback((value) => {
    dispatch(upsertComment({
      data: {
        hid,
        ...value,
      }
    }, null, 'comment'))
  }, [hid]);

  const commentDelete = useCallback((id) => {
    dispatch(deleteComment({
      data: {
        hid,
        id
      }
    }, null, 'commentDelete'))
  }, [hid]);

  return (
    // <CommentsProvider
    //   comments={commentsObj}
    //   users={users}
    //   myUserId={userId}

    //   onCommentAdd={commentUpsert}
    //   onCommentUpdate={commentUpsert}
    //   onCommentDelete={commentDelete}
    // >
    <Plate
      id={editorId}
      plugins={plugins}
      initialValue={content}
    >
      <EditorPlate {...props} />

      {/* <CommentsPopover /> */}
    </Plate>
    // </CommentsProvider>
  )
}

const EditorPlate = ({ content, refresher, hid, editorId, newSubDoc, saveDoc, tags, onChange,
  readOnly, mentionables, autoFocus, saveOnBlockSwitch, containerRef, onNavigateUp, keyDownFromTitle, setKeyDownFromTitle }) => {
  const dispatch = useDispatch();
  const intl = useIntl();

  const editor = useEditorState(editorId);
  const [value, setValue] = usePlateStates(editorId).value();
  const replaceEditor = useReplaceEditor(editorId);
  const onFocusEditorId = useEventEditorSelectors.focus();
  const editorDialogState = useSelector(state => state.uiState.editorDialog);
  const dataEditorDialogState = useSelector(state => state.uiState.dataEditorDialog);
  const selection = useSelector(state => state.uiState.selection);
  const db_enabled = useSelector(state => state.uiState.db_enabled);

  const [messages, setMessages] = useState(null);

  useEffect(() => {
    intl.locale && loadMessages(intl.locale).then((messages) => setMessages(messages.default));
  }, [intl?.locale]);

  useEffect(() => {
    dispatch({
      type: CURRENT_EDITOR,
      value: editor
    });

  }, [editor && editor.children?.length]);

  useEffect(() => {
    if (!onFocusEditorId || editorDialogState.visible || dataEditorDialogState.visible) {
      return;
    }

    dispatch({
      type: CURRENT_EDITOR,
      value: editor
    });

  }, [onFocusEditorId])

  useEffect(() => {
    if (refresher && refresher.target === editorId && editor) {
      replaceEditor()
      setValue(content);
    }
  }, [editorId, refresher && refresher.stamp]);

  useEffect(() => {
    if (!editor || !saveDoc) return;

    if (onChange) {
      onChange(value);
    }

    if (saveOnBlockSwitch && (content?.length != value?.length || value?.length === 1 && !value[0].id && value[0].type === 'p' && value[0].children[0]?.text === '')) {
      return saveDoc(value, false, false);
    }
  }, [value]);


  // useEffect(() => {
  //   console.log('selection change........', editor)
  //   if (saveOnBlockSwitch && editor.prevSelection && Math.abs(editor.selection?.focus?.path[0] - editor.prevSelection?.focus?.path[0])>1) {
  //     return saveDoc(value, false, false);
  //   }
  // }, [editor.selection])

  useLayoutEffect(() => {
    if (keyDownFromTitle && editor && editor.children) {
      ReactEditor.focus(editor);

      if (keyDownFromTitle.key === 'Enter') {
        insertNodes(editor, {
          type: 'p',
          children: [{ text: keyDownFromTitle.withText }],
        }, { at: [0] });
      }

      let offset = keyDownFromTitle.offset || 0;
      const node = getNode(editor, [0, 0]);
      if (!node || !node.text) {
        offset = 0;
      } else {
        offset = Math.min(offset, node.text.length);
      }

      Transforms.select(editor, { path: [0, 0], offset });

      setKeyDownFromTitle(false);
    }
  }, [keyDownFromTitle, editor]);

  useLayoutEffect(() => {
    if (!keyDownFromTitle && value && value.length === 1 && value[0].type === 'p' && value[0].children[0].text === '') {
      setTimeout(() => {
        ReactEditor.focus(editor);
      }, 100)
    }
  }, [value?.length, editor, keyDownFromTitle]);

  useLayoutEffect(() => {
    if (autoFocus && editor && editor.children) {
      setTimeout(() => {
        ReactEditor.focus(editor);
        Transforms.select(editor, { path: [0, 0], offset: 0 });
      }, 10);
    }
  }, [autoFocus, editor]);

  // useLayoutEffect(() => {
  //   if(!aiDialogState.visible) {
  //     ReactEditor.focus(editor);
  //   }
  // }, [aiDialogState])

  const [lastPosition, setLastPosition] = useState({
    path: [100, 100],
    offset: 100,
  });

  const getText = useCallback((node) => {
    const texts = getNodeTexts(node);
    return Array.from(texts).map(tn => tn[0].text).join('');
  }, []);

  const onKeyUp = (event) => {
    if (!editor || !editor.selection) {
      return;
    }

    if (onNavigateUp && event.key === 'ArrowLeft' || event.key === 'Backspace') {
      if (editor.selection.anchor.path[0] == 0 && editor.selection.anchor.path[1] == 0
        && editor.selection.anchor.offset === 0) {
        if (lastPosition.offset === -1) {
          if (event.key === 'Backspace') {
            const node = getNode(editor, [0]);
            if ([ELEMENT_PARAGRAPH, ELEMENT_H1, ELEMENT_H2, ELEMENT_H3, ELEMENT_H4, ELEMENT_H5, ELEMENT_H6].includes(node.type)) {
              onNavigateUp(-1, getText(node));
              removeNodes(editor, { at: [0] });
            }
          } else {
            onNavigateUp(-1);
          }
        } else {
          setLastPosition({
            path: [0, 0],
            offset: -1,
          });
        }
      } else {
        setLastPosition(editor.selection?.anchor);
      }
    } else {
      setLastPosition(editor.selection?.anchor);
    }
  };

  const onKeyDown = (event) => {
    if (!editor) {
      return;
    }

    if (editor.selection && onNavigateUp && event.key === 'ArrowUp') {
      if (editor.selection.anchor.path[0] === 0 && editor.selection.anchor.path[1] === 0) {
        let path = cloneDeep(editor.selection.anchor.path);
        path.pop();

        let currentNode = getNode(editor, path);
        if (currentNode && currentNode.type === ELEMENT_COMMAND_INPUT) {
          return;
        }

        onNavigateUp(editor.selection.focus.offset);
      }
    }

    if (event.key === 'Enter') {
      if (possibleHashTagging(event)) {
        event.preventDefault();
        event.stopPropagation();
        return;
      }
    }

    if ((event.ctrlKey || event.metaKey) && event.key === 's') {
      !!saveDoc && saveDoc(editor.children, true, true);
      event.preventDefault();
      event.stopPropagation();
    }
  }

  const onKeyPress = (event) => {
    if (!editor) {
      return;
    }

    if (!editor.selection) {
      return;
    }

    if (event.key === ' ') {
      possibleHashTagging(event);
    }
  }

  const possibleHashTagging = (event) => {
    const path = editor.selection?.anchor?.path;

    const node = getNode(editor, path);

    if (!node || !node.text || !node.text.includes('#')) {
      return false;
    }

    const parentNode = getParentNode(editor, path)[0];
    if (!parentNode
      // || ![ELEMENT_PARAGRAPH, ELEMENT_H1, ELEMENT_H2, ELEMENT_H3, ELEMENT_H4, ELEMENT_H5, ELEMENT_H6].includes(parentNode.type)
    ) {
      return false;
    }

    const parts = node.text.split(/[ ](#[\p{L}]+)/gui);
    const text = parts[0];
    const tag = parts[1];
    if (!tag || !tag.startsWith('#') || tag.length === 1) {
      return false;
    }

    const tagName = tag.substring(1);
    const tagNode = {
      type: ELEMENT_TAG,
      children: [{ text: '' }],
      value: tagName,
      id: new Date().getTime(),
    };

    deleteText(editor, { distance: tag.length, unit: 'character', at: { path, offset: text.length + 1 } });
    insertNode(editor, tagNode);

    setTimeout(() => {
      Transforms.select(editor);
      Transforms.move(editor, { distance: 1, unit: 'character' });
    }, 10);

    return true;
  }

  const imageUploadState = useSelector(state => state.uiState.imageUploadDialog) || {};
  useEffect(() => {
    const { image } = imageUploadState;
    if (imageUploadState.visible || imageUploadState.trigger != 'doc_editor' || !image?.link) {
      return;
    }

    let path = editor.selection?.anchor?.path[0];

    insertImage(editor, image.link);
    setNodes(editor, { width: '88%', caption: [{ text: image.altText || '' }] });

    let node = editor.children[path];
    if (node?.type === 'p' && node?.children[0].text === '') {
      removeNodes(editor, {
        at: [path]
      })
    }

    dispatch({
      type: IMAGE_UPLOAD_DIALOG,
      value: {
        visible: false
      }
    })
  }, [imageUploadState])

  const renderCommandItem = useCallback(({ item }) => {
    const search_short = intl.locale === 'cn' ? intl.formatMessage({ id: item.key + '_search' }).trim() : ''
    return item.data && <div style={{ display: 'flex', flexDirection: 'row', flex: 1, alignItems: 'center', justifyContent: 'flex-start', marginLeft: 0, minHeight: 36 }}>
      {!!item.data.icon &&
        <div style={{
          display: 'flex', flexDirection: 'row', alignItems: 'center', justifyContent: 'center',
          width: 48, height: 48,
          border: '1px solid #ddd', borderRadius: 4
        }}>
          {item.data.icon}
        </div>
      }
      <div style={{ display: 'flex', flexDirection: 'column', flex: 1, justifyContent: 'center', marginLeft: 4 }}>
        <div style={{ display: 'flex', flexDirection: 'row', flex: 1, alignItems: 'center', justifyContent: 'space-between' }}>
          <div style={{ alignSelf: 'center', color: '#555', fontWeight: 500 }}>
            {item.key === 'cmd_trigger' ? item.text : intl.formatMessage({ id: item.key })}
          </div>
          <div style={{ alignSelf: 'center', fontSize: 14, color: '#999' }}>{search_short ? '/' + search_short : ''}</div>
        </div>
        <div style={{ fontSize: 13, color: '#999' }}>{item.data.desc}</div>
      </div>
    </div>
  }, [intl]);

  const getCmdItems = useCallback((trigger) => {
    let items = CommandItems;
    if (!db_enabled) {
      items = items.filter(item => !item.key.startsWith('cmd_new_db'))
    }
    items = items.map(item => {
      if (item.key === 'cmd_image') {
        item.data.handler = (iEditor) => {
          dispatch({ type: IMAGE_UPLOAD_DIALOG, value: { visible: true, trigger: 'doc_editor', hid } });
        }
      } else if (item.key === 'cmd_new_document') {
        item.data.handler = (iEditor) => newSubDoc(iEditor.id, (newhid) => {
          insertSubPageLink(iEditor, {
            hid: newhid,
            docType: 'doc',
            title: 'new doc',
          });
        }, 'doc')
      } else if (item.key === 'cmd_new_xslides') {
        item.data.handler = (iEditor) => newSubDoc(iEditor.id, (newhid) => {
          // upsertLinkAtSelection(editor, { url, wrap: false });
          insertSubPageLink(iEditor, {
            hid: newhid,
            docType: 'slides',
            title: 'new Slides',
          });
        }, 'slides')
      } else if (item.key === 'cmd_new_flow') {
        item.data.handler = (iEditor) => newSubDoc(iEditor.id, (newhid) => {
          // upsertLinkAtSelection(editor, { url, wrap: false });
          insertSubPageLink(iEditor, {
            hid: newhid,
            docType: 'flow',
            title: 'new AI flow',
          });
        }, 'flow')
      } else if (item.key === 'cmd_new_db') {
        item.data.handler = (iEditor) => newSubDoc(iEditor.id, (newhid) => {
          insertSubPageLink(iEditor, {
            hid: newhid,
            docType: 'db',
            title: 'new Database',
          });
        }, 'db')
      } else if (item.key === 'cmd_emoji') {
        item.data.handler = () => {
          dispatch({
            type: EMOJI_PICKER_DIALOG,
            value: { visible: true }
          });
        }
      } else if (item.key === 'cmd_link') {
        item.data.handler = () => {
          dispatch({
            type: LINK_INPUT_DIALOG,
            value: {
              visible: true,
              textEnabled: true,
              trigger: 'link'
            }
          })
        }
      } else if (item.key === 'cmd_ai_continue') {
        item.data.handler = () => {
          dispatch({
            type: AI_ASSISTANT_DIALOG,
            value: { caller: 'plate', visible: true, trigger: 'cmd', action: 'continue', hid }
          });
        }
      } else if (item.key === 'cmd_ai_assistant') {
        item.data.handler = () => {
          dispatch({
            type: AI_ASSISTANT_DIALOG,
            value: { caller: 'plate', visible: true, trigger: 'cmd', hid }
          });
        }
      }

      item.data.desc = (messages || enMessages)[item.key + '_desc'] && intl.formatMessage({ id: item.key + '_desc' });

      item.text = intl.formatMessage({ id: item.key }) + ' '
        + item.data.desc + ' '
        + item.key.replace('_', ' ') + ' '
        + (intl.locale === 'cn'
          ? (intl.formatMessage({ id: item.key + '_search' }).trim() + enMessages[item.key])
          : '');
      return item
    });

    items.unshift({
      key: 'cmd_trigger',
      text: `'${trigger}' ${intl.formatMessage({ id: 'cmd_trigger' })}`,
      data: {
        icon: null,
        handler: (editor) => {
          Transforms.insertText(editor, trigger);
        },
        args: {}
      }
    });

    return items;
  }, [intl, hid, db_enabled, messages]);

  const [cnCmdItems, setCnCmdItems] = useState();
  const [enCmdItems, setEnCmdItems] = useState();
  useEffect(() => {
    setCnCmdItems(getCmdItems('、'));
    setEnCmdItems(getCmdItems('/'));
  }, [getCmdItems]);

  const [taggables, setTaggables] = useState([]);
  useEffect(() => {
    const newTaggables = (tags || []).map(tag => ({
      key: tag._id,
      text: tag.tag,
    }));

    newTaggables.push({
      key: 'new_tag',
      text: intl.formatMessage({ id: 'new_tag' }),
    });

    setTaggables(newTaggables);
  }, [tags, intl]);

  const tagItemSelected = useCallback((item, search) => {
    if (item.key === 'new_tag') {
      if (search && !taggables.find(t => t.text === search)) {
        const newTaggables = [...taggables];
        newTaggables.unshift({
          key: Math.floor(Math.random() * 1000000) + '',
          text: search,
        });

        setTaggables(newTaggables);
      }
    }
  }, [taggables]);

  return (
    <TooltipProvider
      disableHoverableContent
      delayDuration={500}
      skipDelayDuration={0}
    >
      <div
        id="container"
        tabIndex={0}
        onKeyDown={onKeyDown}
        onKeyUp={onKeyUp}
        onKeyPress={onKeyPress}
        ref={containerRef}
        style={{
          position: 'relative',
          display: 'flex',
          flexDirection: 'column',
          // justifyContent: 'center'
          alignItems: 'center'
        }}
      >
        {/* <SearchHighlightToolbar icon={Search} setSearch={setSearch} /> */}
        <Editor
          autoFocus
          readOnly={readOnly}
          focusRing={false}
          variant={'ghost'}
          size={'md'}
          style={{
            paddingLeft: !readOnly ? '56px' : '10px',
            paddingRight: !readOnly ? '56px' : '10px',
            paddingTop: '1em',
            overflowX: 'hidden'
          }}
        />

        {
          !readOnly && <>
            <FloatingToolbar>
              <FloatingToolbarButtons />
            </FloatingToolbar>
            <MentionCombobox items={mentionables} />
            <TagCombobox
              filter={(search) => (item) => item.key == 'new_tag' || item.text.toLowerCase().includes(search.toLowerCase())}
              items={taggables}
              onSelectItem={tagItemSelected}
            />
            <CommandsCombobox
              onRenderItem={renderCommandItem}
              items={enCmdItems} />
            {/* <CommandsCombobox
          pluginKey='、'
          onRenderItem={renderCommandItem}
          items={cnCmdItems} /> */}
            {
              selection &&
              <CursorOverlay
                containerRef={containerRef}
                cursors={{
                  one: {
                    key: 'one',
                    data: { style: { backgroundColor: '#4f90f2' } },
                    selection: selection,
                  },
                }}
                disableCaret={true}
              />
            }
            <EmojiPickerModal />

            <LinkInputModal />
            <LinkModalDataHandler />
            <AIModalDataHandler />
          </>
        }
      </div>
    </TooltipProvider>
  )
}

export default EditorCore;

