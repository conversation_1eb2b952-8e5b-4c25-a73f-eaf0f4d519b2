import * as React from 'react';
import { EDITOR_DIALOG, REFRESH_DOC } from 'src/constants/actionTypes';
import { useDispatch, useSelector } from 'react-redux';
import { DialogContent, Dialog, Button, DialogTitle, DialogActions } from '@mui/material';
import { useIntl } from 'react-intl';
import { useHistory, useLocation } from 'react-router-dom';

import { OpenInFull } from '@styled-icons/material/OpenInFull';
import EditorEntrance from './EditorEntrance';

const EditorModal = () => {
    const intl = useIntl();
    const dialogState = useSelector(state => state.uiState.editorDialog) || { visible: false };
    const { hid, parent, title, space, orderFactor, visible } = dialogState;
    const dispatch = useDispatch();
    const history = useHistory();

    const handleClose = () => {
        dispatch({ type: EDITOR_DIALOG, value: { visible: false } });
        setTimeout(() => {
            dispatch({ type: REFRESH_DOC, value: parent });
        }, 100);
    }

    return (
        <Dialog
            open={visible}
            onClose={handleClose}
            scroll='paper'
            aria-labelledby="scroll-dialog-title"
            aria-describedby="scroll-dialog-description"
            maxWidth='lg'
            style={{
                zIndex: 100,
            }}
        >
            <DialogContent dividers={true} style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', height: 820, width: 920, padding: 0 }}>
                <EditorEntrance
                    hid={hid}
                    title={title}
                    space={space}
                    orderFactor={orderFactor}
                    mode={'modal'}
                />
            </DialogContent>

            <div style={{
                position: 'absolute',
                bottom: 10,
                right: 10,
            }}>
                <Button onClick={handleClose}>{intl.formatMessage({ id: 'done' })} </Button>
            </div>

            <div
                style={{
                    position: 'absolute',
                    top: 10,
                    right: 20,
                    marginRight: '3px',
                    paddingLeft: '10px',
                    paddingRight: '10px',
                    borderRadius: '5px',
                    color: 'gray'
                }}
                className="hoverStand"
            >
                <OpenInFull
                    size={20}
                    onClick={() => {
                        history.push(`/editor?hid=${hid}&title=${title}&space=${space}&orderFactor=${orderFactor}`);
                        handleClose();
                    }}
                />
            </div>
        </Dialog>
    );
}

export default EditorModal;
