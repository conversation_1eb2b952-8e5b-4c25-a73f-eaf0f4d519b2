import React, { useCallback, useState } from 'react'
import { DragIndicator } from '@styled-icons/material/DragIndicator'
import { Divider, Popover } from '@mui/material'
import { Trash } from '@styled-icons/bootstrap/Trash';
import { useLocation } from 'react-router-dom';
import { ELEMENT_DEFAULT, focusEditor, getBlockAbove, getElementAbsolutePosition, getNode, getNodeTexts, getPluginType, getSelectionText, insertNodes, removeNodes, replaceNodeChildren, select, selectEditor, setMarks, setNodes, setSelection, toDOMNode, toggleNodeType, unsetNodes, useEditorRef, useEventPlateId, usePlateEditorState, usePlateId } from '@udecode/plate-common';
import { useIntl } from 'react-intl';
import { ArrowHookDownRight } from '@styled-icons/fluentui-system-filled/ArrowHookDownRight';
import { ArrowHookUpRight } from '@styled-icons/fluentui-system-filled/ArrowHookUpRight';
import { TextFields } from '@styled-icons/material/TextFields';
import { ColorLens } from '@styled-icons/material-outlined/ColorLens';
import { FileCopy } from '@styled-icons/material-outlined/FileCopy';
import { ArrowRepeatAll } from '@styled-icons/fluentui-system-filled/ArrowRepeatAll';
import { exitBreak } from './Toolbars';
import { ColorSelector } from './ColorSelector';
import Tippy from '@tippyjs/react';
import 'tippy.js/themes/light.css';
import { ELEMENT_DB_EMBED } from 'src/plate-plugins/db-embeded';
import { ELEMENT_SLIDES_EMBED } from 'src/plate-plugins/slides-embeded';
import { ELEMENT_SUB_PAGE } from 'src/plate-plugins/sub-page-link/createSubPageLinkPlugin';
import { useDispatch, useSelector } from 'react-redux';
import { AI_ASSISTANT_DIALOG } from 'src/constants/actionTypes';
import { Magic } from '@styled-icons/bootstrap';
import { TextGrammarWand, Wand } from '@styled-icons/fluentui-system-filled';
import { ELEMENT_IMAGE, ELEMENT_MEDIA_EMBED } from '@udecode/plate-media';
import { KEY_LIST_START, KEY_LIST_STYLE_TYPE } from '@udecode/plate-indent-list';
import { TurnIntoTippyMenu, useTurnIntoMenuContent } from '@/components/plate-ui/turn-into-dropdown-menu';
import { Icons } from '@/components/icons';

export const Grabbler = ({ element }) => {
    const intl = useIntl();
    const [anchorEl, setAnchorEl] = React.useState(null);
    const [hoveredIndex, setHoveredIndex] = useState(-1);
    const colorSelector = React.useRef(null);

    // const editor = usePlateEditorState(hid);
    const editor = useEditorRef(useEventPlateId());
    // const editor = useSelector(state => state.uiState.currentEditor);
    const dispatch = useDispatch();


    const handleClose = () => {
        setAnchorEl(null);
    }

    const hasComment = useCallback((element) => {
        if (!element || !element.children) {
            return false;
        }

        for (let child of element.children) {
            if (child.comment) {
                return true;
            }
        }

        return false;
    }, [])

    const path = editor && editor.children ? [editor.children.findIndex(e => e.id === element.id)] : [0];

    const handleSetColor = (type, value) => {
        select(editor, path);
        focusEditor(editor);
        editor.addMark(type, value);

        setAnchorEl(null);
    }

    const { menuContent: turnIntoMenuContent } = useTurnIntoMenuContent(path, handleClose);

    const items = [
        {
            text: intl.formatMessage({ id: 'delete' }),
            icon: <div style={styles.icon}><Trash size={16} /></div>,
            onClick: () => {
                removeNodes(editor, { at: path });
            }
        },
        {
            text: intl.formatMessage({ id: 'duplicate' }),
            icon: <div style={styles.icon}><FileCopy size={16} /></div>,
            onClick: () => {
                insertNodes(editor, element, { at: path });
            }
        },
        {
            type: 'divider',
        },
        {
            text: intl.formatMessage({ id: 'cmd_ai_optimize' }),
            icon: <div style={styles.icon}> <TextGrammarWand color='dodgerblue' size={18} /></div>,
            onClick: () => {
                dispatch({
                    type: AI_ASSISTANT_DIALOG,
                    value: { caller: 'plate', visible: true, trigger: 'blockHandler', action: 'optimize', path }
                })
            }
        },
        {
            text: intl.formatMessage({ id: 'cmd_ai_continue' }),
            icon: <div style={styles.icon}> <Wand color='dodgerblue' size={18} /></div>,
            onClick: () => {
                dispatch({
                    type: AI_ASSISTANT_DIALOG,
                    value: { caller: 'plate', visible: true, trigger: 'blockHandler', action: 'continue', path }
                })
            }
        },
        {
            text: intl.formatMessage({ id: 'cmd_ai_assistant' }),
            icon: <div style={styles.icon}><Magic color='dodgerblue' size={18} /></div>,
            onClick: () => {
                dispatch({
                    type: AI_ASSISTANT_DIALOG,
                    value: { caller: 'plate', visible: true, trigger: 'blockHandler', path }
                })
            }
        },
        // {
        //     text: intl.formatMessage({ id: 'cmd_clear_marks' }),
        //     icon: <div style={styles.icon}><TextFields size={18} /></div>,
        //     onClick: () => {
        //         if (hasComment(element)) {
        //             return;
        //         }

        //         const texts = getNodeTexts(element);
        //         const text = Array.from(texts).map(tn => tn[0].text).join('');
        //         // toggleNodeType(editor, { at: path, activeType: element.type})
        //         setNodes(editor, { type: 'p' }, { at: path });
        //         unsetNodes(editor, [KEY_LIST_STYLE_TYPE, KEY_LIST_START, 'indent'], { at: path });
        //         replaceNodeChildren(editor, {
        //             at: path,
        //             nodes: [{ text }]
        //         })
        //     }
        // },
        {
            key: 'turnInto',
            text: intl.formatMessage({ id: 'turn_into' }),
            icon: <div style={styles.icon}><ArrowRepeatAll size={17} /></div>,
            submenu: turnIntoMenuContent
        },
        {
            text: intl.formatMessage({ id: 'cmd_block_above' }),
            icon: <div style={styles.icon}><ArrowHookUpRight size={18} /></div>,
            onClick: () => {
                exitBreak(editor, true, path);
            }
        },
        {
            text: intl.formatMessage({ id: 'cmd_block_below' }),
            icon: <div style={styles.icon}><ArrowHookDownRight size={18} /></div>,
            onClick: () => {
                exitBreak(editor, false, path);
            }
        }
    ]

    if (![ELEMENT_DB_EMBED, ELEMENT_SLIDES_EMBED, ELEMENT_MEDIA_EMBED, ELEMENT_IMAGE, ELEMENT_SUB_PAGE].includes(element.type)) {
        items.push({
            type: 'divider',
        });

        items.push({
            key: 'colorSelector',
            text: intl.formatMessage({ id: 'color' }),
            icon: <div style={styles.icon}><ColorLens size={17} /></div>,
            submenu: <ColorSelector onChange={handleSetColor} />
        });
    }

    return <div className='hoverStand'
        style={{
            paddingLeft: 0,
            paddingRight: 0,
            paddingTop:3,
            paddingBottom: 3
        }}
    >
        <Icons.dragHandle
            style={{
                width: 20,
                height: 20,
                color: 'rgba(55, 53, 47, 0.3)',
            }}

            onClick={(e) => {
                // select(editor, path);
                // focusEditor(editor);
                setAnchorEl(e.currentTarget);
            }}
        />
        <Popover
            open={Boolean(anchorEl)}
            onClose={handleClose}

            anchorEl={anchorEl}
            anchorOrigin={{
                vertical: 'top',
                horizontal: 'right',
            }}
            transformOrigin={{
                vertical: 'top',
                horizontal: 'left',
            }}
        >
            <div style={{ width: '200px', padding: '6px' }}>
                {
                    items.map((item, index) => {
                        if (item.type === 'divider') {
                            return <Divider key={index} style={{ marginTop: 6, marginBottom: 6 }} />
                        }

                        let menuContent = <div key={index}
                            style={{
                                display: 'flex',
                                flexDirection: 'row',
                                alignItems: 'center',
                                fontSize: 14,
                                backgroundColor: hoveredIndex === index ? '#eee' : 'white',
                                padding: '2px 6px 2px 6px',
                                borderRadius: '4px',
                                cursor: 'pointer',
                            }}
                            onMouseEnter={() => {
                                setHoveredIndex(index);
                            }}
                            onMouseLeave={() => {
                                setHoveredIndex(-1);
                            }}
                            onClick={(e) => {
                                item.onClick(e);
                                handleClose();
                            }}
                        >
                            {item.icon}
                            {item.text}
                        </div>;

                        if (item.submenu) {
                            return <Tippy
                                key={index}
                                ref={colorSelector}
                                content={item.submenu}
                                interactive={true}
                                placement="right"
                                appendTo={document.body}
                                theme='light'
                                hideOnClick={false}
                            >
                                {menuContent}
                            </Tippy>
                        }

                        return menuContent
                    })
                }
            </div>
        </Popover>
    </div>
}

const styles = {
    menuItem: {
        display: 'flex',
        width: '160px',
        margin: '0px',
        flexDirection: 'row',
        justifyContent: 'flex-start',
        alignContent: 'center',
        alignItems: 'center'
    },
    icon: {
        width: '26px',
        height: '26px',
        alignItems: 'center',
        justifyContent: 'center',
        display: 'flex'
    }
}