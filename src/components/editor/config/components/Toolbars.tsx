import 'tippy.js/animations/scale.css'
import 'tippy.js/dist/tippy.css'
import React, { useRef } from 'react'
import { CodeAlt } from '@styled-icons/boxicons-regular/CodeAlt'
import { CodeBlock } from '@styled-icons/boxicons-regular/CodeBlock'
import { Highlight } from '@styled-icons/boxicons-regular/Highlight'
import { Subscript } from '@styled-icons/foundation/Subscript'
import { Superscript } from '@styled-icons/foundation/Superscript'
import { BorderAll } from '@styled-icons/material/BorderAll'
import { BorderClear } from '@styled-icons/material/BorderClear'
import { TableInsertColumn } from '@styled-icons/fluentui-system-regular/TableInsertColumn'
import { TableInsertRow } from '@styled-icons/fluentui-system-regular/TableInsertRow'
import { TableDeleteColumn } from '@styled-icons/fluentui-system-regular/TableDeleteColumn'
import { TableDeleteRow } from '@styled-icons/fluentui-system-regular/TableDeleteRow'
import { TableChart } from '@styled-icons/material-outlined/TableChart'
import { FormatAlignCenter } from '@styled-icons/material/FormatAlignCenter'
import { FormatAlignJustify } from '@styled-icons/material/FormatAlignJustify'
import { FormatAlignLeft } from '@styled-icons/material/FormatAlignLeft'
import { FormatAlignRight } from '@styled-icons/material/FormatAlignRight'
import { FormatBold } from '@styled-icons/material/FormatBold'
import { FormatIndentDecrease } from '@styled-icons/material/FormatIndentDecrease'
import { FormatIndentIncrease } from '@styled-icons/material/FormatIndentIncrease'
import { FormatItalic } from '@styled-icons/material/FormatItalic'
import { FormatListBulleted } from '@styled-icons/material/FormatListBulleted'
import { FormatListNumbered } from '@styled-icons/material/FormatListNumbered'
import { FormatQuote } from '@styled-icons/material/FormatQuote'
import { H1 } from '@styled-icons/remix-editor/H1'
import { H2 } from '@styled-icons/remix-editor/H2'
import { H3 } from '@styled-icons/remix-editor/H3'
import { AddTask } from '@styled-icons/material/AddTask'
import { HorizontalRule } from '@styled-icons/material/HorizontalRule'
import { FileSlides } from '@styled-icons/bootstrap/FileSlides'
import { FileSpreadsheet } from '@styled-icons/bootstrap/FileSpreadsheet'
import { FileText } from '@styled-icons/bootstrap/FileText'
import { Kanban } from '@styled-icons/bootstrap/Kanban';
import { Table } from '@styled-icons/bootstrap/Table';
import { ListAlt } from '@styled-icons/material-outlined/ListAlt';
import { GridView } from '@styled-icons/material-outlined/GridView';
import { Calendar4Event } from '@styled-icons/bootstrap/Calendar4Event';
import { MathFormula } from '@styled-icons/fluentui-system-filled';
import { EmojiEmotions } from '@styled-icons/material-outlined/EmojiEmotions';
import { MathFormatProfessional } from '@styled-icons/fluentui-system-filled/MathFormatProfessional';
import { Pencil } from '@styled-icons/bootstrap/Pencil';
import { Chat } from '@styled-icons/material/Chat';
import { Magic } from '@styled-icons/bootstrap/Magic'
import { Wand } from '@styled-icons/fluentui-system-filled/Wand'
import { TextGrammarWand } from '@styled-icons/fluentui-system-regular/TextGrammarWand'
import { TippyProps } from '@tippyjs/react'

import { Link } from '@styled-icons/material/Link'
import { Image } from '@styled-icons/fluentui-system-regular/Image'
import { OndemandVideo } from '@styled-icons/material/OndemandVideo'
import { TComboboxItem } from '@udecode/plate-combobox'
import { Editor, Transforms, Path } from 'slate';
import { ReturnUpBack } from '@styled-icons/ionicons-sharp/ReturnUpBack'
import { insertSlidesEmbed } from 'src/plate-plugins/slides-embeded'
import { insertDBEmbed } from 'src/plate-plugins/db-embeded'
import { insertSubPageLink } from 'src/plate-plugins/sub-page-link/transforms'
import { ELEMENT_MATH_EQUATION_INLINE, insertMathEquationBlock, insertMathEquationInline } from 'src/plate-plugins/math-eqaution'
import { getAboveNode, getParentNode, getPluginType, insertNodes, setNodes, toggleMark, toggleNodeType, useEditorRef } from '@udecode/plate-common'
import { ELEMENT_PARAGRAPH } from '@udecode/plate-paragraph'
import { triggerFloatingLink } from '@udecode/plate-link'
import { insertMediaEmbed } from '@udecode/plate-media'
import { insertEmptyCodeBlock } from '@udecode/plate-code-block'
import { indent, outdent } from '@udecode/plate-indent'
import { outdentList, toggleIndentList } from '@udecode/plate-indent-list'
import { setAlign } from '@udecode/plate-alignment'
import { ELEMENT_TD, ELEMENT_TH, ELEMENT_TR, deleteColumn, deleteRow, insertTable, insertTableColumn, insertTableRow } from '@udecode/plate-table'
import { MARK_HIGHLIGHT } from '@udecode/plate-highlight'
import { Icons } from '@/components/icons';
import { FileMedical } from '@styled-icons/bootstrap'


export const ToolbarButtons = () => {}
// (
//   <>
//     <BasicElementToolbarButtons />
//     <ListToolbarButtons />
//     {/* <IndentToolbarButtons /> */}
//     <BasicMarkToolbarButtons />
//     <KbdToolbarButton />
//     <HighlightToolbarButton />
//     <ColorPickerToolbarDropdown
//       pluginKey={MARK_COLOR}
//       icon={<FormatColorText />}
//       selectedIcon={<Check />}
//       tooltip={{ content: 'Text color' }}
//     />
//     <ColorPickerToolbarDropdown
//       pluginKey={MARK_BG_COLOR}
//       icon={<FontDownload />}
//       selectedIcon={<Check />}
//       tooltip={{ content: 'Highlight color' }}
//     />
//     <AlignToolbarButtons />
//     <LinkToolbarButton icon={<Link />} />
//     <ImageToolbarButton icon={<Image />} />
//     <MediaEmbedToolbarButton icon={<OndemandVideo />} />
//     <TableToolbarButtons />
//   </>
// )

const iconStyle = {
  width: '30px',
  height: '30px',
  color: '#666',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
}

export const exitBreak = (editor, before, at) => {
  let insertPath = at;

  if (!at) {
    const selectionPath = Editor.path(editor, editor.selection);
    insertPath = selectionPath.slice(0, 1);
  }

  if (!before) {
    insertPath = Path.next(insertPath);
  }

  insertNodes(
    editor,
    { type: ELEMENT_PARAGRAPH, children: [{ text: '' }] },
    {
      at: insertPath,
      select: true,
    }
  );
}

export const CommandItems: TComboboxItem<{ icon: Object, desc?: String, handler: Function, args: Object }>[] = 
[{
  key: 'cmd_ai_continue',
  text: 'cmd_ai_continue',
  data: {
    icon: <Wand style={{...iconStyle, color: 'dodgerblue', width: '26px', height: '26px'}} />,
    handler: null,
    args: null
  }
}, {
  key: 'cmd_ai_assistant',
  text: 'cmd_ai_assistant',
  data: {
    icon: <Magic style={{...iconStyle, color: 'dodgerblue', width: '26px', height: '26px'}} />,
    handler: null,
    args: null
  }
}, {
  key: 'cmd_h1',
  text: 'cmd_h1',
  data: {
    icon: <Icons.h1 style={iconStyle} />,
    handler: toggleNodeType,
    args: { activeType: 'h1' }
  }
}, {
  key: 'cmd_h2',
  text: 'cmd_h2',
  data: {
    icon: <Icons.h2 style={iconStyle} />,
    handler: toggleNodeType,
    args: { activeType: 'h2' }
  }
}, {
  key: 'cmd_h3',
  text: 'cmd_h3',
  data: {
    icon: <Icons.h3 style={iconStyle} />,
    handler: toggleNodeType,
    args: { activeType: 'h3' }
  }
  // }, {
  //   key: 'cmd_h4',
  //   text: 'cmd_h4',
  //   data: {
  //     icon: <H4 style={iconStyle} />,
  //     handler: toggleNodeType,
  //     args: { activeType: 'h4' }
  //   }
}, {
  key: 'cmd_blockquote',
  text: 'cmd_blockquote',
  data: {
    icon: <Icons.blockquote style={{...iconStyle, width: '26px', height: '26px'}} />,
    handler: toggleNodeType,
    args: { activeType: 'blockquote' }
  }
}, {
  key: 'cmd_new_document',
  text: 'cmd_new_document',
  data: {
    icon: <FileText style={iconStyle} />,
    handler: null,
    args: null
  }
}, {
  key: 'cmd_link_to_page',
  text: 'cmd_link_to_page',
  data: {
    icon: <FileText style={iconStyle} />,
    handler: (editor) => {
      insertSubPageLink(editor, {
        hid: '',
        docType: '',
        title: '',
      });
    },
    args: null
  }
}, {
  key: 'cmd_new_flow',
  text: 'cmd_new_flow',
  data: {
    icon: <FileMedical style={iconStyle} />,
    handler: null,
    args: null
  }
}, {
  key: 'cmd_new_xslides',
  text: 'cmd_new_xslides',
  data: {
    icon: <FileSlides style={iconStyle} />,
    handler: null,
    args: null
  }
}, {
  key: 'cmd_new_xslides_inline',
  text: 'cmd_new_xslides_inline',
  data: {
    icon: <FileSlides style={iconStyle} />,
    handler: (editor) => {
      insertSlidesEmbed(editor, { hid: '' });
    },
    args: null
  }
}, {
  key: 'cmd_new_db',
  text: 'cmd_new_db',
  data: {
    icon: <FileSpreadsheet style={iconStyle} />,
    handler: null,
    args: null
  }
}, {
  key: 'cmd_new_db_inline',
  text: 'cmd_new_db_inline',
  data: {
    icon: <FileSpreadsheet style={iconStyle} />,
    handler: (editor) => {
      insertDBEmbed(editor, { hid: '', isNewDataSource: true });
    },
    args: null
  }
}, {
  key: 'cmd_new_db_view_table',
  text: 'cmd_new_db_view_table',
  data: {
    icon: <Table style={{ ...iconStyle, width: 26, height: 26 }} />,
    handler: (editor) => {
      insertDBEmbed(editor, { hid: '', isNewDataSource: false, view: 'table' });
    },
    args: null
  }
}, {
  key: 'cmd_new_db_view_board',
  text: 'cmd_new_db_view_board',
  data: {
    icon: <Kanban style={{ ...iconStyle, width: 26, height: 26 }} />,
    handler: (editor) => {
      insertDBEmbed(editor, { hid: '', isNewDataSource: false, view: 'board' });
    },
    args: null
  }
}, {
  key: 'cmd_new_db_view_list',
  text: 'cmd_new_db_view_list',
  data: {
    icon: <ListAlt style={{ ...iconStyle, width: 34, height: 34 }} />,
    handler: (editor) => {
      insertDBEmbed(editor, { hid: '', isNewDataSource: false, view: 'list' });
    },
    args: null
  }
}, {
  key: 'cmd_new_db_view_timeline',
  text: 'cmd_new_db_view_timline',
  data: {
    icon: <Calendar4Event style={{ ...iconStyle, width: 26, height: 26 }} />,
    handler: (editor) => {
      insertDBEmbed(editor, { hid: '', isNewDataSource: false, view: 'timeline' });
    },
    args: null
  }
}, {
  key: 'cmd_new_db_view_gallery',
  text: 'cmd_new_db_view_gallery',
  data: {
    icon: <GridView style={{ ...iconStyle, width: 32, height: 32 }} />,
    handler: (editor) => {
      insertDBEmbed(editor, { hid: '', isNewDataSource: false, view: 'gallery' });
    },
    args: null
  }
}, {
  key: 'cmd_image',
  text: 'cmd_image',
  data: {
    icon: <Icons.image style={{ ...iconStyle, width: 33, height: 33 }} />,
    handler: null,
    args: {}
  }
// }, {
//   key: 'cmd_image_link',
//   text: 'cmd_image_link',
//   data: {
//     icon: <Image style={{ ...iconStyle, width: 34, height: 34 }} />,
//     handler: (editor) => {
//       let url = window.prompt('Enter the URL of the image:');
//       if (!url) return;

//       insertImage(editor, url);
//       setNodes(editor, { width: '88%' });
//     },
//     args: {}
//   }
}, {
  key: 'cmd_media_embed',
  text: 'cmd_media_embed',
  data: {
    icon: <Icons.embed style={iconStyle} />,
    handler: (editor) => {
      let url;
      // if (getEmbedUrl) {
      //   url = await getEmbedUrl();
      // } else {
      url = window.prompt('Enter the URL of the embed:');
      // }
      if (!url) return;

      insertMediaEmbed(editor, { url });
    },
    args: {}
  }
}, {
  key: 'cmd_link',
  text: 'cmd_link',
  data: {
    icon: <Icons.link style={iconStyle} />,
    handler: triggerFloatingLink,
    args: { focused: true }
  }
}, {
  key: 'cmd_emoji',
  text: 'cmd_emoji',
  data: {
    icon: <Icons.emoji style={iconStyle} />,
    handler: null,
    args: {}
  }
}, {
  key: 'cmd_mathblock',
  text: 'cmd_mathblock',
  data: {
    icon: <MathFormula style={iconStyle} />,
    handler: insertMathEquationBlock,
    args: {}
  }
}, {
  key: 'cmd_mathline',
  text: 'cmd_mathline',
  data: {
    icon: <MathFormatProfessional style={iconStyle} />,
    handler: insertMathEquationInline,
    args: {}
  }
}, {
  key: 'cmd_codeblock',
  text: 'cmd_codeblock',
  data: {
    icon: <Icons.codeblock style={iconStyle} />,
    handler: insertEmptyCodeBlock,
    args: { insertNodesOptions: { select: true } }
  }
}, {
  key: 'cmd_codeline',
  text: 'cmd_codeline',
  data: {
    icon: <Icons.code style={iconStyle} />,
    handler: toggleMark,
    args: { key: 'code' }
  }
}, {
  key: 'cmd_hr',
  text: 'cmd_hr',
  data: {
    icon: <Icons.hr style={iconStyle} />,
    handler: (editor) => {
      insertNodes(editor, {
        type: 'hr',
        children: [{ text: '' }],
      });
    },
    args: {}
  }
}, {
  key: 'cmd_line_break',
  text: 'cmd_line_break',
  data: {
    // icon: <SubdirectoryArrowLeft style={iconStyle} />,
    icon: <div style={iconStyle}>⇧⏎</div>,
    handler: (editor) => {
      editor.insertText('\n');
    },
    args: {}
  }
}, {
  key: 'cmd_block_below',
  text: 'cmd_block_below',
  data: {
    icon: <div style={iconStyle}>⌘⏎</div>,
    handler: (editor) => exitBreak(editor, false, null),
    args: {}
  }
}, {
  key: 'cmd_block_above',
  text: 'cmd_block_above',
  data: {
    icon: <ReturnUpBack style={iconStyle} />,
    handler: (editor) => exitBreak(editor, true, null),
    args: {}
  }
// }, {
//   key: 'cmd_highlight',
//   text: 'cmd_highlight',
//   data: {
//     icon: <Highlight style={iconStyle} />,
//     handler: toggleMark,
//     args: { key: MARK_HIGHLIGHT }
//   }
// }, {
//   key: 'cmd_superscript',
//   text: 'cmd_superscript',
//   data: {
//     icon: <Superscript style={{ ...iconStyle, width: 38, height: 38 }} />,
//     handler: toggleMark,
//     args: { key: MARK_SUPERSCRIPT, clear: MARK_SUBSCRIPT }
//   }
// }, {
//   key: 'cmd_subscript',
//   text: 'cmd_subscript',
//   data: {
//     icon: <Subscript style={{ ...iconStyle, width: 38, height: 38 }} />,
//     handler: toggleMark,
//     args: { key: MARK_SUBSCRIPT, clear: MARK_SUPERSCRIPT }
//   }
// }, {
//   key: 'cmd_indent',
//   text: 'cmd_indent',
//   data: {
//     icon: <FormatIndentIncrease style={iconStyle} />,
//     handler: indent,
//     args: {}
//   }
// }, {
//   key: 'cmd_outdent',
//   text: 'cmd_outdent',
//   data: {
//     icon: <FormatIndentDecrease style={iconStyle} />,
//     handler: (editor) => {
//       let [node] = getParentNode(editor, editor.selection.anchor.path);
//       if (node && node.type == 'lic') {
//         return outdentList(editor);
//       }
//       outdent(editor);
//     },
//     args: {}
//   }
}, {
  key: 'cmd_bulleted_list',
  text: 'cmd_bulleted_list',
  data: {
    icon: <Icons.ul style={iconStyle} />,
    // handler: toggleList,
    // args: { type: 'ul' }
    handler: toggleIndentList,
    args: {
      listStyleType: 'disc',
    }
  }
}, {
  key: 'cmd_ordered_list',
  text: 'cmd_ordered_list',
  data: {
    icon: <Icons.ol style={iconStyle} />,
    // handler: toggleList,
    // args: { type: 'ol' }
    handler: toggleIndentList,
    args: {
      listStyleType: 'decimal',
    }
  }
}, {
  key: 'cmd_todo_list',
  text: 'cmd_todo_list',
  data: {
    icon: <AddTask style={iconStyle} />,
    handler: toggleNodeType,
    args: { activeType: 'action_item' }
  }
}, {
  key: 'cmd_align_left',
  text: 'cmd_align_left',
  data: {
    icon: <Icons.alignLeft style={iconStyle} />,
    handler: setAlign,
    args: {
      value: 'left',
      key: 'align',
    }
  }
}, {
  key: 'cmd_align_center',
  text: 'cmd_align_center',
  data: {
    icon: <Icons.alignCenter style={iconStyle} />,
    handler: setAlign,
    args: {
      value: 'center',
      key: 'align',
    }
  }
}, {
  key: 'cmd_align_right',
  text: 'cmd_align_right',
  data: {
    icon: <Icons.alignRight style={iconStyle} />,
    handler: setAlign,
    args: {
      value: 'right',
      key: 'align',
    }
  }
}, {
  key: 'cmd_align_justify',
  text: 'cmd_align_justify',
  data: {
    icon: <Icons.alignJustify style={iconStyle} />,
    handler: setAlign,
    args: {
      value: 'justify',
      key: 'align',
    }
  }
}, {
  key: 'cmd_table',
  text: 'cmd_table',
  data: {
    icon: <Icons.table style={iconStyle} />,
    handler: insertTable,
    args: {}
  }
}, {
  key: 'cmd_table_row',
  text: 'cmd_table_row',
  data: {
    icon: <TableInsertRow style={iconStyle} />,
    handler: insertTableRow,
    args: {}
  }
}, {
  key: 'cmd_table_column',
  text: 'cmd_table_column',
  data: {
    icon: <TableInsertColumn style={iconStyle} />,
    handler: insertTableColumn,
    args: {}
  }
}, {
  key: 'cmd_table_header',
  text: 'cmd_table_header',
  data: {
    icon: <TableChart style={iconStyle} />,
    handler: (editor) => {
      const currentRowItem = getAboveNode(editor, {
        match: { type: getPluginType(editor, ELEMENT_TR) },
      });

      if(!currentRowItem || !currentRowItem[0] || !currentRowItem[0].children) {
        return;
      }

      const children = currentRowItem[0].children;
      children.forEach((child, index) => {
        setNodes(editor, { type: child.type ===ELEMENT_TH ? ELEMENT_TD: ELEMENT_TH}, {at: currentRowItem[1].concat([index])})
      })
    },
    args: {}
  }
}, {
  key: 'cmd_table_delete_row',
  text: 'cmd_table_delete_row',
  data: {
    icon: <TableDeleteRow style={iconStyle} />,
    handler: deleteRow,
    args: {}
  }
}, {
  key: 'cmd_table_delete_column',
  text: 'cmd_table_delete_column',
  data: {
    icon: <TableDeleteColumn style={iconStyle} />,
    handler: deleteColumn,
    args: {}
  }
}];
