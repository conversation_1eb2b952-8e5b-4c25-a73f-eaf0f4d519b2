import { MARK_BG_COLOR, MARK_COLOR } from '@udecode/plate-font';
import React, { useState } from 'react'
import { useIntl } from 'react-intl';

export const ColorSelector = ({ onChange }) => {
    const intl = useIntl();
    const [hoveredItem, setHoveredItem] = useState(null);

    const textColorItems = [
        {
            text: intl.formatMessage({ id: 'default' }),
            value: undefined,
        },
        {
            text: intl.formatMessage({ id: 'color_gray' }),
            value: 'rgb(120, 119, 116)',
        },
        {
            text: intl.formatMessage({ id: 'color_brown' }),
            value: 'rgb(159, 107, 83)',
        },
        {
            text: intl.formatMessage({ id: 'color_orange' }),
            value: 'rgb(217, 115, 13)',
        },
        {
            text: intl.formatMessage({ id: 'color_yellow' }),
            value: 'rgb(203, 145, 47)',
        },
        {
            text: intl.formatMessage({ id: 'color_green' }),
            value: 'rgb(50, 205, 50)',
        },
        {
            text: intl.formatMessage({ id: 'color_blue' }),
            value: 'rgb(30, 144, 255)',
        },
        {
            text: intl.formatMessage({ id: 'color_purple' }),
            value: 'rgb(144, 101, 176)',
        },
        {
            text: intl.formatMessage({ id: 'color_pink' }),
            value: 'rgb(193, 76, 138)',
        },
        {
            text: intl.formatMessage({ id: 'color_red' }),
            value: 'rgb(212, 76, 71)',
        }
    ];

    const bgColorItems = [
        {
            text: intl.formatMessage({ id: 'default' }),
            value: '#fff',
        },
        {
            text: intl.formatMessage({ id: 'color_gray' }),
            value: 'rgb(241, 241, 239)',
        },
        {
            text: intl.formatMessage({ id: 'color_brown' }),
            value: 'rgb(244, 238, 238)',
        },
        {
            text: intl.formatMessage({ id: 'color_orange' }),
            value: 'rgb(251, 236, 221)',
        },
        {
            text: intl.formatMessage({ id: 'color_yellow' }),
            value: 'rgb(251, 243, 219)',
        },
        {
            text: intl.formatMessage({ id: 'color_green' }),
            value: 'rgb(237, 243, 236)',
        },
        {
            text: intl.formatMessage({ id: 'color_blue' }),
            value: 'rgb(231, 243, 248)',
        },
        {
            text: intl.formatMessage({ id: 'color_purple' }),
            value: 'rgba(244, 240, 247, 0.8)',
        },
        {
            text: intl.formatMessage({ id: 'color_pink' }),
            value: 'rgba(249, 238, 243, 0.8)',
        },
        {
            text: intl.formatMessage({ id: 'color_red' }),
            value: 'rgb(253, 235, 236)',
        }
    ];

    return <div style={{ width: '180px' }}>
        <div
            key={'text_color'}
            style={{
                fontSize: '13px',
                color: '#999',
                fontWeight: 500,
                padding: '6px 0px 6px 0px',
            }}
        >
            {intl.formatMessage({ id: 'text_color' })}
        </div>
        {
            textColorItems.map((item, index) => {
                return <div key={index}
                    style={{
                        display: 'flex',
                        flexDirection: 'row',
                        alignItems: 'center',
                        backgroundColor: hoveredItem === 'text' + index ? '#eee' : 'white',
                        padding: '2px 6px 2px 6px',
                        borderRadius: '4px',
                        cursor: 'pointer',
                    }}
                    onMouseEnter={() => {
                        setHoveredItem('text' + index);
                    }}
                    onMouseLeave={() => {
                        setHoveredItem(null);
                    }}
                    onClick={() => {
                        onChange(MARK_COLOR, item.value);
                    }}
                >
                    <div style={{
                        width: '20px',
                        height: '20px',
                        borderRadius: '4px',
                        border: '1px solid #ccc',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        fontWeight: 'bold',
                        color: item.value,
                        marginRight: '6px',
                    }}>A</div>
                    {item.text}
                </div>
            })
        }
        <div
            key={'bg_color'}
            style={{
                fontSize: '13px',
                color: '#999',
                fontWeight: 500,
                padding: '6px 0px 6px 0px',
            }}
        >
            {intl.formatMessage({ id: 'highlight_color' })}
        </div>
        {
            bgColorItems.map((item, index) => {
                return <div key={item.value}
                    style={{
                        display: 'flex',
                        flexDirection: 'row',
                        alignItems: 'center',
                        backgroundColor: hoveredItem === 'bg' + index ? '#eee' : 'white',
                        padding: '2px 6px 2px 6px',
                        borderRadius: '4px',
                        cursor: 'pointer',
                    }}
                    onMouseEnter={() => {
                        setHoveredItem('bg' + index);
                    }}
                    onMouseLeave={() => {
                        setHoveredItem(null);
                    }}
                    onClick={() => {
                        onChange(MARK_BG_COLOR, item.value);
                    }}
                >
                    <div style={{
                        width: '20px',
                        height: '20px',
                        borderRadius: '4px',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        fontWeight: 'bold',
                        backgroundColor: item.value,
                        marginRight: '6px',
                    }}>A</div>
                    {item.text}
                </div>
            })
        }
    </div>
}

const styles = {
    menuItem: {
        display: 'flex',
        width: '160px',
        margin: '0px',
        flexDirection: 'row',
        justifyContent: 'flex-start',
        alignContent: 'center',
        alignItems: 'center'
    },
    icon: {
        width: '26px',
        height: '26px',
    }
}