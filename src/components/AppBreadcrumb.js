import { But<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Typo<PERSON>, Tooltip } from '@mui/material';
import React, { useEffect, useLayoutEffect, useState } from 'react'
import { useSelector, useDispatch } from 'react-redux';
import { useLocation, NavLink } from 'react-router-dom';
import { useIntl } from 'react-intl';
import { getDocIgnoreContent } from 'src/actions/ticketAction';
import { DOC_HISTORY_DIALOG, DOC_PATH_ADD, DOC_PATH_CLEAR } from 'src/constants/actionTypes';
import { DOC_PERMISSION } from 'src/constants/constants';
import DocHistoryModal from './header/DocHistoryModal';
import Share from './header/Share';

import { History } from '@styled-icons/fluentui-system-regular/History';
import { Slideshow } from '@styled-icons/boxicons-regular/Slideshow';
import DataEditorModal from './dbeditor/DataEditorModal';
import { getState } from 'src/reducers/listReducer';
import EditorModal from './editor/EditorModal';
import DBEditorModal from './dbeditor/DBEditorModal';
import Slides from './header/Slides';
import MoreMenu from './header/MoreMenu';
import ImportModal from './header/ImportModal';
import { linkToPage } from 'src/utils/PageLinkMaker';

const AppBreadcrumb = () => {
  const currentLocation = useLocation();
  const params = new Proxy(new URLSearchParams(currentLocation.search), {
    get: (searchParams, prop) => searchParams.get(prop),
  });

  const routeState = currentLocation.state || {};

  const dispatch = useDispatch();
  const intl = useIntl();

  const docPath = useSelector(state => state.uiState.docPath);
  const docs = useSelector(state => state.docs);
  const dbdata = useSelector(state => getState(state.dbdata_lists, params.hid));
  // const doc = docs.byId[params.hid];
  const [doc, setDoc] = useState(null);

  useEffect(() => {
    if (!docs) {
      return;
    }

    if (currentLocation.pathname == '/data' && params.dataId && dbdata) {
      const dataItem = dbdata.items.find(item => item._id == params.dataId);
      if (dataItem) {
        setDoc(docs.byId[dataItem.doc]);
      }
      return;
    }

    if (params.hid) {
      setDoc(docs.byId[params.hid]);
    }
  }, [docs, params.hid, params.dataId, dbdata]);

  useEffect(() => {
    if (!routeState.title) {
      dispatch({ type: DOC_PATH_CLEAR });
      makeDocPath(doc);
    }
  }, [params.hid, dispatch, routeState.title, doc && doc.parent, doc && doc.title]); //use doc.parent to make sure docPath is updated only when doc.parent is changed


  const makeDocPath = (doc) => {
    if (!doc) {
      return;
    }

    let linkTo = linkToPage(doc);

    dispatch({
      type: DOC_PATH_ADD, value: {
        pathname: linkTo,
        name: doc.title,
        hid: doc.hid
      }
    })

    const parentHid = doc.parent;
    if (parentHid && parentHid !== 'root') {
      const parent = docs.byId[parentHid];
      if (!parent) {
        return dispatch(getDocIgnoreContent({ hid: parentHid }, (docReceived) => {
          makeDocPath(docReceived);
        }, 'appbreadcrumb'));
      } else {
        return makeDocPath(parent, docPath);
      }
    }
  }

  return (
    <div className="header-breadcrumb">
      <div style={{ display: 'flex', alignItems: 'center' }}>
        {
          routeState.title
        }
        {
          !routeState.title && docPath.map((breadcrumb, index) => {
            return (
              <div key={index} style={{ color: 'rgb(0, 120, 212)' }}>
                {
                  !!index && <span style={{ padding: '3px' }}>/</span>
                }
                <NavLink
                  style={{ textDecoration: 'none', color: 'rgb(0, 120, 212)' }}
                  to={breadcrumb.pathname}
                  key={index}
                >
                  {breadcrumb.name}
                </NavLink>
              </div>
            )
          })
        }
      </div>
      <div>
        {
          doc &&
          <div style={{ flexDirection: 'row', display: 'flex' }}>
            {
              !['slides', 'db', 'flow', 'brainstorming'].includes(doc?.type) &&
              <Slides doc={doc} />
            }
            {
              doc.permission === DOC_PERMISSION.full_access &&
              <Share item={doc} fromSpace={params.space} />
            }
            {
              !['slides', 'db', 'flow', 'brainstorming'].includes(doc?.type) &&
              <Tooltip title={intl.formatMessage({ id: 'doc_history' })} placement="top">
                <IconButton color="primary"
                  onClick={() => dispatch({ type: DOC_HISTORY_DIALOG, value: { visible: true, hid: doc.hid } })}
                >
                  <History size={22} />
                </IconButton>
              </Tooltip>
            }
            {
              !['flow', 'brainstorming'].includes(doc?.type) &&
              <MoreMenu doc={doc} />
            }
            <DocHistoryModal />
            <DataEditorModal />
            <EditorModal />
            <DBEditorModal />
            <ImportModal />
          </div>
        }
      </div>
    </div>
  )
}

export default React.memo(AppBreadcrumb)
