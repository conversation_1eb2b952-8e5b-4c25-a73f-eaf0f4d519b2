import React, { Suspense } from 'react'
import { Redirect, Route, Switch } from 'react-router-dom'
import CircularProgress from '@mui/material/CircularProgress';
// import 'prismjs/themes/prism.css';

// routes config
import routes from '../routes'
import { useSelector } from 'react-redux';
import GlobalDndContext from './DndContext';

const EmbeddedContent = () => {
  const loginUser = useSelector(state => state.loginIn.user);

  return (
    <div className="app-content">
      <GlobalDndContext>
        <Suspense fallback={<CircularProgress />}>
          <Switch>
            {routes.map((route, idx) => {
              return (
                route.component && (
                  <Route
                    key={idx}
                    path={route.path}
                    exact={route.exact}
                    name={route.name}
                    render={(props) => {
                      // return !!loginUser && loginUser._id ? <route.component {...props} /> : <Redirect to={'/login'} />
                      return <route.component {...props} />
                    }}
                  />
                )
              )
            })}
            <Redirect from="/home" to="/" />
          </Switch>
        </Suspense>
      </GlobalDndContext>
    </div>
  )
}

export default React.memo(EmbeddedContent)
