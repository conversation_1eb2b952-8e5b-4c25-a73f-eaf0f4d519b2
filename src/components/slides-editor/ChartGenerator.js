
import { KeyboardReturn } from '@styled-icons/material/KeyboardReturn';
import { useEffect, useRef, useState } from 'react';
import { Button, Dialog, DialogActions, DialogContent, FormControl, FormControlLabel, FormLabel, Popover, Radio, RadioGroup, TextareaAutosize } from "@mui/material";
import { useIntl } from 'react-intl';
import { useDispatch, useSelector } from 'react-redux';
import { CHART_GENERATOR_DIALOG, POLL_GENERATOR_DIALOG } from 'src/constants/actionTypes';
import Papa from 'papaparse';
import * as XLSX from 'xlsx';

export const ChartGenerator = ({ }) => {
    const intl = useIntl();
    const dialogState = useSelector(state => state.uiState.chartGeneratorDialog) || { visible: false };
    const dispatch = useDispatch();

    const [data, setData] = useState({
        source: 'data',
        chart: 'bar'
    });
    const [error, setError] = useState();

    useEffect(() => {
        if (dialogState.visible) {
            setData({
                source: 'data',
                chart: 'bar'
            });

            setError(null);
        }
    }, [dialogState.visible])

    const handleClose = () => {
        dispatch({ type: CHART_GENERATOR_DIALOG, value: { visible: false } });
    }

    const handleConfirm = () => {
        if (data.source === 'url' && !data.url || !data.csv || data.csv.split('\n').filter(l => !!l?.trim()).length < 2) {
            return setError('data_invalid');
        }

        dispatch({
            type: CHART_GENERATOR_DIALOG,
            value: {
                ...dialogState,
                visible: false,
                data
            }
        })
    }

    const parseCSV = (content) => {
        console.log('parse csv........................')
        Papa.parse(content, {
            complete: (results) => {
                console.log('parse csv........................', results)
                const csvString = results.data.map(row => row.join(',')).join('\n');
                setCSVData(csvString);
            },
            error: (error) => {
                console.error('CSV parsing error:', error);
            }
        });
    };

    const setCSVData = (content) => {
        setData({
            ...data,
            csv: content
        })
    }

    return (
        <Dialog
            open={!!dialogState && dialogState.visible}
            onClose={handleClose}
            scroll='paper'
            aria-labelledby="scroll-dialog-title"
            aria-describedby="scroll-dialog-description"
            maxWidth='md'
            style={{
                zIndex: 100,
            }}

            onKeyDown={(event) => {
                if (event.key === 'Enter') {
                    handleConfirm();
                }
                event.preventDefault();
                event.stopPropagation();
                return true;
            }}
        >
            <DialogContent dividers={true} style={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-start', padding: 0, backgroundColor: 'white' }}>

                <div style={{ margin: '20px', rowGap: '28px', display: 'flex', flexDirection: 'column', justifyContent: 'flex-start', width: '600px' }}>
                    <div style={{ display: 'flex', flexDirection: 'column', rowGap: '10px', width: '-webkit-fill-available' }}>
                        <div>{intl.formatMessage({ id: 'data_source' })}</div>
                        <FormControl>
                            {/* <FormLabel id="demo-row-radio-buttons-group-label">{intl.formatMessage({ id: 'ai_providers' })}</FormLabel> */}
                            <RadioGroup
                                row
                                aria-labelledby="demo-row-radio-buttons-group-label"
                                name="row-radio-buttons-group"
                                value={data.source}
                                onChange={(event) => {
                                    setData({
                                        ...data,
                                        source: event.target.value
                                    })
                                }}
                            >
                                <FormControlLabel value="data" control={<Radio />} label={intl.formatMessage({ id: 'csv_data' })} />
                                <FormControlLabel value="file" control={<Radio />} label={intl.formatMessage({ id: 'csv_file' })} />
                                <FormControlLabel value="url" control={<Radio />} label={intl.formatMessage({ id: 'csv_url' })} />
                            </RadioGroup>
                        </FormControl>

                        {
                            data.source === 'file' &&
                            <input
                                type="file"
                                accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
                                // value={image.file || ''}
                                onChange={(event) => {
                                    const file = event.target.files[0];
                                    const reader = new FileReader();
                                    const isExcelFile = file.type === 'application/vnd.ms-excel' || file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';

                                    if (isExcelFile) {

                                        reader.onload = (e) => {
                                            const content = e.target.result;
                                            const workbook = XLSX.read(content, { type: 'binary' });
                                            const sheetName = workbook.SheetNames[0];
                                            const worksheet = workbook.Sheets[sheetName];
                                            const csv = XLSX.utils.sheet_to_csv(worksheet);
                                            setCSVData(csv);
                                        };

                                        reader.readAsBinaryString(file);
                                    } else {
                                        reader.onload = (e) => {
                                            const content = e.target.result;
                                            setCSVData(content);
                                        };

                                        reader.readAsText(file);
                                    }
                                }}
                            />
                        }
                        {
                            ['data', 'file'].includes(data.source) &&
                            <textarea
                                id="data"
                                rows={10}
                                value={data.csv || ''}
                                onChange={(event) => setData({
                                    ...data,
                                    csv: event.target.value
                                })}
                                style={{
                                    padding: '4px',
                                    width: '-webkit-fill-available',
                                    outline: 'none',
                                    border: 'solid 1px lightgray', borderRadius: '3px'
                                }}
                            />
                        }
                        {
                            data.source === 'data' &&
                            <Button variant='text'
                                onClick={() => {
                                    setCSVData(
                                        "Month, January, February, March, April, May, June, July\n" +
                                        "My first dataset, 65, 59, 80, 81, 56, 55, 40\n" +
                                        "My second dataset, 28, 48, 40, 19, 86, 27, 90"
                                    )
                                }}
                            >
                                {intl.formatMessage({ id: 'show_csv_example' })}
                            </Button>
                        }
                        {
                            data.source === 'url' &&
                            <input
                                id="link"
                                type="url"
                                value={data.url || ''}
                                onChange={(event) => setData({
                                    ...data,
                                    url: event.target.value
                                })}
                                style={{
                                    padding: '4px',
                                    width: '-webkit-fill-available',
                                    outline: 'none',
                                    border: 'solid 1px lightgray', borderRadius: '3px'
                                }}
                            />
                        }

                    </div>


                    <div style={{ display: 'flex', flexDirection: 'column', rowGap: '10px' }}>
                        <div>{intl.formatMessage({ id: 'chart_type' })}</div>
                        <FormControl>
                            {/* <FormLabel id="demo-row-radio-buttons-group-label">{intl.formatMessage({ id: 'ai_providers' })}</FormLabel> */}
                            <RadioGroup
                                row
                                aria-labelledby="demo-row-radio-buttons-group-label"
                                name="row-radio-buttons-group"
                                value={data.chart}
                                onChange={(event) => {
                                    setData({
                                        ...data,
                                        chart: event.target.value
                                    })
                                }}
                            >
                                <FormControlLabel value="line" control={<Radio />} label={intl.formatMessage({ id: 'line' })} />
                                <FormControlLabel value="bar" control={<Radio />} label={intl.formatMessage({ id: 'bar' })} />
                                <FormControlLabel value="pie" control={<Radio />} label={intl.formatMessage({ id: 'pie' })} />
                            </RadioGroup>
                        </FormControl>
                    </div>

                    {
                        error &&
                        <div style={{ color: 'red' }}>{intl.formatMessage({ id: error })}</div>
                    }
                </div>
            </DialogContent>
            <DialogActions>
                <div style={{ paddingRight: '16px' }}>
                    <Button variant='text' onClick={handleClose}>{intl.formatMessage({ id: 'cancel' })}</Button>
                    <Button variant='contained' onClick={handleConfirm}>{intl.formatMessage({ id: 'confirm' })}</Button>
                </div>
            </DialogActions>
        </Dialog>
    );
}