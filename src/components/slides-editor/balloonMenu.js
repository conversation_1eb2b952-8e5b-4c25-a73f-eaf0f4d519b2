
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import './cmdMenu.css'
import { Magic } from '@styled-icons/bootstrap/Magic'
import { Wand } from '@styled-icons/fluentui-system-filled/Wand'
import { Tooltip } from '@mui/material';
import { useIntl } from 'react-intl';
import { AI_ASSISTANT_DIALOG, IMAGE_UPLOAD_DIALOG, LINK_INPUT_DIALOG, OPERATION_SUCCESS, TABLE_BUILDER_DIALOG } from 'src/constants/actionTypes';
import { TextGrammarWand } from '@styled-icons/fluentui-system-filled';
import { Icons } from '@/components/icons';

export const BalloonMenu = ({ hid, state, editorView, onClose, insert }) => {
    const intl = useIntl();
    const { isVisible, position, lineElement } = state;
    const dispatch = useDispatch();
    const ref = useRef();
    const shouldUseDarkColors = useSelector(state => state.uiState.should_use_dark_colors);

    const performAction = (item) => {
        if (item.action && typeof item.action === 'function') {
            item.action();
        }

        onClose && onClose();
    }

    const getSelectedText = () => {
        let range = editorView.state.selection.ranges[0];
        if (!range) {
            return '';
        }

        return editorView.state.doc.toString().substring(range.from, range.to);
    }

    const mark = (prefix, suffix) => {
        let range = editorView.state.selection.ranges[0];
        editorView.dispatch({
            changes: [{ from: range.from, insert: prefix },
            { from: range.to, insert: suffix }],
            selection: { anchor: range.to + prefix.length + suffix.length }
        })
    }

    const iconColor = shouldUseDarkColors ? '#777' : '#888';

    const items = [{
        id: 'bold',
        action: () => {
            mark('**', '**')
        },
        icon: <Icons.bold color={iconColor} style={iconStyle} />
    }, {
        id: 'italic',
        action: () => {
            mark('*', '*')
        },
        icon: <Icons.italic color={iconColor} style={iconStyle} />
    }, {
        id: 'strikethrough',
        action: () => {
            mark('~~', '~~')
        },
        icon: <Icons.strikethrough color={iconColor} style={iconStyle} />
    }, {
        id: 'bulleted_list',
        icon: <Icons.ul color={iconColor} style={iconStyle} />,
        action: () => insert(-1, `\n- `, 0)
    }, {
        id: 'ordered_list',
        icon: <Icons.ol color={iconColor} style={iconStyle} />,
        action: () => insert(-1, `\n1. `, 0)
    }, {
        id: 'table',
        icon: <Icons.table color={iconColor} style={iconStyle} />,
        action: () => dispatch({ type: TABLE_BUILDER_DIALOG, value: { visible: true, data: { columns: 3, rows: 2 } } })
    }, {
        id: 'blockquote',
        icon: <Icons.blockquote color={iconColor} style={iconStyle} />,
        action: () => insert(-1, '\n> ', 0)
    }, {
        id: 'image',
        icon: <Icons.image color={iconColor} style={{ ...iconStyle, width: 22, height: 22 }} />,
        action: () => {
            dispatch({ type: IMAGE_UPLOAD_DIALOG, value: { visible: true, trigger: 'slides_editor', hid } });
        }
    }, {
        id: 'link',
        icon: <Icons.link color={iconColor} style={iconStyle} />,
        // action: () => insert(-1, `[text here](link here)`, 1)
        action: () => dispatch({
            type: LINK_INPUT_DIALOG,
            value: {
                visible: true,
                textEnabled: true,
                trigger: 'link',
                data: {
                    text: getSelectedText(),
                },
            }
        })
    }, {
        id: 'copy',
        action: () => {
            navigator.clipboard.writeText(getSelectedText());

            dispatch({
                type: OPERATION_SUCCESS,
                message: 'Copied',
            });
        },
        icon: <Icons.copy color={iconColor} style={iconStyle} />
    }, {
        id: 'ai_optimize',
        action: () => {
            dispatch({
                type: AI_ASSISTANT_DIALOG,
                value: { caller: 'slides', objType: 'slides', visible: true, trigger: 'balloonToolbar', action: 'optimize', anchorEl: lineElement, selectedText: getSelectedText() }
            });
        },
        icon: <TextGrammarWand color={'dodgerblue'} style={iconStyle} />
    }, {
        id: 'ai_continue',
        action: () => {
            dispatch({
                type: AI_ASSISTANT_DIALOG,
                value: { caller: 'slides', objType: 'slides', visible: true, trigger: 'balloonToolbar', action: 'continue', anchorEl: lineElement, selectedText: getSelectedText() }
            });
        },
        icon: <Wand color={'dodgerblue'} style={iconStyle} />
    }, {
        id: 'ai_assistant',
        action: () => {
            dispatch({
                type: AI_ASSISTANT_DIALOG,
                value: { caller: 'slides', objType: 'slides', visible: true, trigger: 'balloonToolbar', anchorEl: lineElement, selectedText: getSelectedText(), pageContent: editorView.state.doc.toString() }
            });
        },
        icon: <Magic color={'dodgerblue'} style={iconStyle} />
    }];

    if (!isVisible || !position) {
        return <></>
    }

    return <div
        className={`popup_wrapper_${shouldUseDarkColors ? 'dark' : 'light'}`}
        style={{ ...position, position: 'absolute', display: 'flex' }}
        ref={ref}
    >
        {
            items?.map((item, index) => {
                return <Tooltip
                    key={index + ''}
                    title={intl.formatMessage({ id: `cmd_${item.id}_desc` })}
                    placement="bottom-start"
                >
                    <div
                        className={`hoverStand_${shouldUseDarkColors ? 'dark' : 'light'}`}
                        style={{
                            width: '36px',
                            height: '36px',
                            padding: 0,
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            borderRadius: '4px'
                        }}
                        onClick={(e) => {
                            performAction(item);
                            e.preventDefault();
                            e.stopPropagation();
                        }}

                        onMouseUp={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                        }}
                    >{item.icon}</div>
                </Tooltip>
            })
        }
    </div>
}

const iconStyle = {
    // color: '#666',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    width: '20px',
    height: '20px'
}