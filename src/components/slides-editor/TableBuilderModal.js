
import { KeyboardReturn } from '@styled-icons/material/KeyboardReturn';
import { useEffect, useRef, useState } from 'react';
import { Button, Dialog, DialogActions, DialogContent, FormControl, FormControlLabel, FormLabel, Popover, Radio, RadioGroup, TextareaAutosize } from "@mui/material";
import { useIntl } from 'react-intl';
import { useDispatch, useSelector } from 'react-redux';
import { TABLE_BUILDER_DIALOG } from 'src/constants/actionTypes';

export const TableBuilderModal = ({ }) => {
    const intl = useIntl();
    const dialogState = useSelector(state => state.uiState.tableBuilderDialog) || { visible: false };
    const dispatch = useDispatch();

    const [data, setData] = useState({});
    const [error, setError] = useState();

    const handleClose = () => {
        dispatch({ type: TABLE_BUILDER_DIALOG, value: { visible: false } });
    }

    const handleConfirm = () => {
        if (!data?.columns || !data?.rows) {
            return;
        }
        dispatch({
            type: TABLE_BUILDER_DIALOG,
            value: {
                ...dialogState,
                visible: false,
                data
            }
        })
    }

    useEffect(() => {
        if (dialogState.visible) {
            setData(dialogState.data);

            setError(null);
        }
    }, [dialogState.visible])

    return (
        <Dialog
            open={!!dialogState && dialogState.visible}
            onClose={handleClose}
            scroll='paper'
            aria-labelledby="scroll-dialog-title"
            aria-describedby="scroll-dialog-description"
            maxWidth='md'
            style={{
                zIndex: 100,
            }}

            onKeyDown={(event) => {
                if (event.key === 'Enter') {
                    handleConfirm();
                    event.preventDefault();
                    event.stopPropagation();
                    return true;
                }
            }}
        >
            <DialogContent dividers={true} style={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-start', width: 220, padding: 0, backgroundColor: 'white' }}>
                <div style={{ margin: '20px', rowGap: '20px', display: 'flex', flexDirection: 'column', justifyContent: 'flex-start', width: '-webkit-fill-available' }}>
                    <div style={{ display: 'flex', flexDirection: 'row', columnGap: '6px', alignItems: 'center' }}>
                        <div style={{ width: 100, textAlign: 'right' }}>
                            {intl.formatMessage({ id: 'columns' })}
                        </div>
                        <input
                            value={data.columns}
                            type='number'
                            min="1" max="10"
                            required
                            onChange={(event) => setData({
                                ...data,
                                columns: event.target.value
                            })}
                            style={{
                                padding: '4px',
                                width: '-webkit-fill-available',
                                outline: 'none',
                                border: 'solid 1px lightgray', borderRadius: '3px'
                            }}
                            autoFocus={true}
                        />
                    </div>
                    <div style={{ display: 'flex', flexDirection: 'row', columnGap: '6px', alignItems: 'center' }}>
                        <div style={{ width: 100, textAlign: 'right' }}>{intl.formatMessage({ id: 'rows' })}</div>
                        <input
                            type="number"
                            min="1" max="20"
                            required
                            value={data.rows}
                            onChange={(event) => setData({
                                ...data,
                                rows: event.target.value
                            })}
                            style={{
                                padding: '4px',
                                width: '-webkit-fill-available',
                                outline: 'none',
                                border: 'solid 1px lightgray', borderRadius: '3px'
                            }}
                        />
                    </div>
                </div>
            </DialogContent>
            <DialogActions>
                <div style={{ paddingRight: '16px' }}>
                    <Button variant='text' onClick={handleClose}>{intl.formatMessage({ id: 'cancel' })}</Button>
                    <Button variant='contained' onClick={handleConfirm}>{intl.formatMessage({ id: 'confirm' })}</Button>
                </div>
            </DialogActions>
        </Dialog>
    );
}