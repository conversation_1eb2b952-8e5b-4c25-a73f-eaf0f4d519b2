.popup_wrapper_light {
  border: 1px solid #d8d8d8;
  z-index: 999;
  background-color: white;
  -webkit-box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.3);
  -moz-box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.3);
  box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.3);
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -ms-border-radius: 5px;
  -o-border-radius: 5px;
  border-radius: 5px;
  margin: 5px;
}

.popup_wrapper_dark {
  border: 1px solid #272727;
  z-index: 999;
  background-color: #282c34;
  -webkit-box-shadow: 0px 0px 8px rgba(255, 255, 255, 0.3);
  -moz-box-shadow: 0px 0px 8px rgba(255, 255, 255, 0.3);
  box-shadow: 0px 0px 8px rgba(255, 255, 255, 0.3);
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -ms-border-radius: 5px;
  -o-border-radius: 5px;
  border-radius: 5px;
  margin: 5px;
}

.hoverStand_light {
  cursor: pointer;
  padding: 8px 6px;
  margin: 0;
  display: flex;
  align-items: center;
  font-size: 14px;
}

.hoverStand_light:hover {
  background-color: #f1f1f1;
}

.hoverStand_dark {
  cursor: pointer;
  padding: 8px 6px;
  margin: 0;
  display: flex;
  align-items: center;
  font-size: 14px;
}

.hoverStand_dark:hover {
  background-color: #0e0e0e;
}

.icon_dark {
  border: 1px solid #666;
  color: #1f2023,
}

.icon_light {
  border: 1px solid #ddd;
  color: #ddd,
}

.item_dark {
  color: #dadada;
  font-size: 14px;
}

.item_light {
  color: #1f2023;
  font-size: 14px;
}

.warning_area_dark {
  color: #aaa;
  background-color: #444;
}

.warning_area_light {
  color: gray;
  background-color: #f5f5f5;
}