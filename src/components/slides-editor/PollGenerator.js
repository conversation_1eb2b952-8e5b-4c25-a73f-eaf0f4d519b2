
import { KeyboardReturn } from '@styled-icons/material/KeyboardReturn';
import { useEffect, useRef, useState } from 'react';
import { Button, Dialog, DialogActions, DialogContent, FormControl, FormControlLabel, FormLabel, Popover, Radio, RadioGroup, TextareaAutosize } from "@mui/material";
import { useIntl } from 'react-intl';
import { useDispatch, useSelector } from 'react-redux';
import { POLL_GENERATOR_DIALOG } from 'src/constants/actionTypes';

export const PollGenerator = ({ }) => {
    const intl = useIntl();
    const dialogState = useSelector(state => state.uiState.pollGeneratorDialog) || { visible: false };
    const dispatch = useDispatch();

    const [data, setData] = useState({
        options: ['', ''],
        chart: 'bar'
    });
    const [error, setError] = useState();

    useEffect(() => {
        if (dialogState.visible) {
            setData({
                options: ['', ''],
                chart: 'bar'
            });

            setError(null);
        }
    }, [dialogState.visible])

    const handleClose = () => {
        dispatch({ type: POLL_GENERATOR_DIALOG, value: { visible: false } });
    }

    const handleConfirm = () => {
        if (data.options.filter(q => !!q?.trim()).length < 2) {
            return setError('no_enough_options');
        }
        dispatch({
            type: POLL_GENERATOR_DIALOG,
            value: {
                ...dialogState,
                visible: false,
                data
            }
        })
    }

    return (
        <Dialog
            open={!!dialogState && dialogState.visible}
            onClose={handleClose}
            scroll='paper'
            aria-labelledby="scroll-dialog-title"
            aria-describedby="scroll-dialog-description"
            maxWidth='md'
            style={{
                zIndex: 100,
            }}

            onKeyDown={(event) => {
                if (event.key === 'Enter') {
                    handleConfirm();
                    event.preventDefault();
                    event.stopPropagation();
                    return true;
                }
            }}
        >
            <DialogContent dividers={true} style={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-start', padding: 0, backgroundColor: 'white' }}>
                <div style={{ margin: '20px', rowGap: '28px', display: 'flex', flexDirection: 'column', justifyContent: 'flex-start', width: '360px' }}>
                    <div style={{ display: 'flex', flexDirection: 'column', rowGap: '10px' }}>
                        <div>{intl.formatMessage({ id: 'poll_desc' })}</div>
                        <textarea
                            id="poll_desc"
                            rows={3}
                            value={data.desc || ''}
                            onChange={(event) => setData({
                                ...data,
                                desc: event.target.value
                            })}
                            style={{
                                padding: '4px',
                                width: '-webkit-fill-available',
                                outline: 'none',
                                border: 'solid 1px lightgray', borderRadius: '3px'
                            }}
                        />
                    </div>
                    <div style={{ display: 'flex', flexDirection: 'column', rowGap: '10px' }}>
                        <div>{intl.formatMessage({ id: 'select_options' })}</div>
                        {
                            data.options.map((option, index) => {
                                return <input
                                    value={option}
                                    key={index + ''}
                                    onChange={(event) => {
                                        let options = [...data.options];
                                        options[index] = event.target.value;
                                        setData({
                                            ...data,
                                            options
                                        })
                                    }}
                                    style={{
                                        padding: '4px',
                                        width: '-webkit-fill-available',
                                        outline: 'none',
                                        border: 'solid 1px lightgray', borderRadius: '3px'
                                    }}
                                />
                            })
                        }

                        <Button variant='contained' onClick={() => {
                            let options = [...data.options];
                            options.push('');
                            setData({
                                ...data,
                                options
                            })
                        }}>
                            {intl.formatMessage({ id: 'add_option' })}
                        </Button>

                    </div>

                    <div style={{ display: 'flex', flexDirection: 'column', rowGap: '10px' }}>
                        <div>{intl.formatMessage({ id: 'chart_type' })}</div>
                        <FormControl>
                            {/* <FormLabel id="demo-row-radio-buttons-group-label">{intl.formatMessage({ id: 'ai_providers' })}</FormLabel> */}
                            <RadioGroup
                                row
                                aria-labelledby="demo-row-radio-buttons-group-label"
                                name="row-radio-buttons-group"
                                value={data.chart}
                                onChange={(event) => {
                                    setData({
                                        ...data,
                                        chart: event.target.value
                                    })
                                }}
                            >
                                <FormControlLabel value="bar" control={<Radio />} label={intl.formatMessage({ id: 'bar' })} />
                                <FormControlLabel value="pie" control={<Radio />} label={intl.formatMessage({ id: 'pie' })} />
                            </RadioGroup>
                        </FormControl>
                    </div>

                    {
                        error &&
                        <div style={{ color: 'red' }}>{intl.formatMessage({ id: error })}</div>
                    }
                </div>
            </DialogContent>
            <DialogActions>
                <div style={{ paddingRight: '16px' }}>
                    <Button variant='text' onClick={handleClose}>{intl.formatMessage({ id: 'cancel' })}</Button>
                    <Button variant='contained' onClick={handleConfirm}>{intl.formatMessage({ id: 'confirm' })}</Button>
                </div>
            </DialogActions>
        </Dialog>
    );
}