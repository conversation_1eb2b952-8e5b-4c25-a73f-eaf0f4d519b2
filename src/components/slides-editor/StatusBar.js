import React, { useCallback, useEffect, useRef, useState } from 'react';
import { useIntl } from 'react-intl';
import { useSelector } from 'react-redux';
import { Question } from '@styled-icons/bootstrap/Question';
import { Play } from '@styled-icons/fluentui-system-regular/Play';
import './statusBar.css'
import { Tooltip } from '@mui/material';
import { useLocation, useHistory } from 'react-router-dom'
import { get_server_host } from 'src/utils/serverAPIUtil';
import { getLocale } from 'src/utils/Intl';
import { getMainDomain } from '@/utils/constants';

export const StatusBar = ({ slides, message }) => {
    const location = useLocation();
    const intl = useIntl();
    const lng = useSelector(state => state.uiState.lng);

    const params = new Proxy(new URLSearchParams(location.search), {
        get: (searchParams, prop) => searchParams.get(prop),
    });

    return <div
        style={{
            height: '32px', display: 'flex', flexDirection: 'row', alignItems: 'center',
            fontSize: 13, color: '#6c6c6c', backgroundColor: '#f5f5f5',
            borderTop: '1px solid #ddd',
            paddingRight: '10px'
        }}
    >
        <div style={{ width: 100, paddingLeft: 8, borderRight: '1px solid #ddd' }}>
            {`H: ${slides.h + 1}, V: ${slides.v + 1}`}
        </div>

        <div style={{ paddingLeft: 8, width: '100%' }}>
            {intl.formatMessage({ id: message || 'editor_slash_hint' })}
        </div>
        <div style={{ display: 'flex', flexDirection: 'row', columnGap: '8px' }}>
            <Tooltip title={intl.formatMessage({ id: 'slide_present_tooltip' })} arrow placement="top">
                <div className='action_button' style={{ width: '26px', height: '26px', alignItems: 'center', justifyContent: 'center', display: 'flex' }}
                    onClick={() => {
                        get_server_host().then((server) => {
                            var url = server;
                            url += `present.html?hid=${location.state?.hid || params.hid}`;


                            let newTab = window.open();
                            newTab.location.href = url;
                        })

                    }}
                >
                    <Play size={22} />
                </div>
            </Tooltip>
            <Tooltip title={intl.formatMessage({ id: 'slides_tutor_tooltip' })} arrow placement="top">
                <div className='action_button' style={{ width: '26px', height: '26px' }}
                    onClick={() => {
                        let isCN = lng === 'cn' || getLocale() === 'cn';
                        window.open(`https://service.funblocks.net/present.html?hid=slides_edit_tutor&showNotes=true`);
                        // window.open(`https://service.${getMainDomain()}/present.html?hid=xslides_edit_tutor${isCN ? '' : '_en'}&showNotes=true`);
                    }}
                >
                    <Question size={24} />
                </div>
            </Tooltip>
        </div>
    </div>
}
