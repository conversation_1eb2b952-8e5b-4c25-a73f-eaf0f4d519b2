import React from 'react'
import { useEffect, useState, useRef } from 'react'

import { EditorState, EditorSelection, Prec } from '@codemirror/state'
import { EditorView, keymap, lineNumbers, placeholder } from '@codemirror/view'
import { cursorLineDown, cursorLineUp, defaultKeymap } from '@codemirror/commands'
import { markdown, markdownLanguage } from '@codemirror/lang-markdown'
import { languages } from '@codemirror/language-data'
import { oneDark } from '@codemirror/theme-one-dark'
import { basicSetup, minimalSetup } from 'codemirror'
import { content, dom } from '@uiw/codemirror-extensions-events';
import { useSelector } from 'react-redux'

export const transparentTheme = EditorView.theme({
  '&': {
    backgroundColor: 'transparent !important',
    height: '100%'
  }
})


const useCodeMirror = (
  props
) => {
  const refContainer = useRef(null)
  const [editorView, setEditorView] = useState();
  const { onChange, onInput, onClick, initialDoc, editorReadyCallback, shouldBlockEnter } = props;
  const shouldUseDarkColors = useSelector(state => state.uiState.should_use_dark_colors);

  const domEvent = dom({
    click: (event) => {
      onClick && onClick(event);
    },
    // keypress: (event) => {
    //   onKeyPress && onKeyPress(event);
    // }
  })

  useEffect(() => {
    if (!refContainer.current) return

    const startState = EditorState.create({
      doc: initialDoc?.markdown,

      selection: EditorSelection.create([
        EditorSelection.range(0, 0),
        EditorSelection.cursor(0)
      ]),

      extensions: [
        // keymap.of(defaultKeymap),
        basicSetup,
        domEvent,
        // lineNumbers(),

        markdown({
          base: markdownLanguage,
          codeLanguages: languages,
          addKeymap: true
        }),
        shouldUseDarkColors ? oneDark : undefined,
        placeholder(`Type '/' for commands`),
        EditorView.lineWrapping,
        EditorView.updateListener.of(update => {
          if (update.changes) {
            onChange && onChange(update.state)
          }
        }),
        EditorView.inputHandler.of(onInput),
        Prec.highest(
          keymap.of([{
            key: "ArrowDown",
            run: (view) => {
              return true
            }
          }, {
            key: "ArrowUp",
            run: (view) => {
              return true
            }
          }, {
            key: "Enter",
            run: (view) => {
              // 当 slash menu 或 AI dialog 可见时，屏蔽 Enter 键
              console.log('should block enter?..........', shouldBlockEnter && shouldBlockEnter())
              if (shouldBlockEnter && shouldBlockEnter()) {
                return true; // 阻止默认行为
              }
              return false; // 允许默认行为
            }
          }])
        )
      ].filter(ext => !!ext)
    })

    const view = new EditorView({
      state: startState,
      parent: refContainer.current
    })
    setEditorView(view)

    !!editorReadyCallback && editorReadyCallback(view);

    if (document.getElementsByClassName('cm-editor')[0]) {
      document.getElementsByClassName('cm-editor')[0].style.outline = 'none';
    }

    return () => {
      view.destroy()
    }
  }, [refContainer, initialDoc?.hid, shouldUseDarkColors])

  return [refContainer, editorView]
}

export default useCodeMirror
