/* @flow */

import React, { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useHistory, Link } from 'react-router-dom';

// import ArticleItem from "./ArticleItem";
// import ArticleActionBar from "./ArticleActionBar";
import * as TimeFormater from '../../utils/timeFormater';
import { LIST_ITEM_SELECTED } from "src/constants/actionTypes";
import SlidesActionBar from "./SlidesActionBar";


const SlidesItem = ({ item, changeTitleModalRef }) => {
  const itemSelected = useSelector(state => state.uiState.list_item_selected);
  const pressed = item._id === itemSelected;
  // const articleItem = <ArticleItem
  //   item={{ ...item.article, title: item.title, tags: item.tags, createdAt: item.createdAt }}
  // />;

  const [hovering, setHovering] = useState(false);
  const dispatch = useDispatch();

  const history = useHistory();

  const open = () => {
    history.push({ pathname: '/slidesViewer', search: '?hid=' + item.hid, state: { title: item.title, hid: item.hid, item } })
    dispatch({ type: LIST_ITEM_SELECTED, value: item._id })
  }

  const time = item.updatedAt && TimeFormater.formatFromNow(item.createdAt, 'MM-DD') || 'just now';
  return (
    <div
      style={Object.assign({}, styles.container, pressed && styles.pressedItem)}
      onMouseOver={() => setHovering(true)}
      onMouseLeave={() => setHovering(false)}
      onClick={open}
    >
      <div
        style={{
          display: 'flex', flexDirection: 'column', flex: 1, justifyContent: 'space-between',
          paddingTop: 8,
          paddingBottom: 4,
        }}
      >
        <span style={{
          textOverflow: 'ellipsis',
          // position: 'relative'
          paddingBottom: 4,
        }}>
          {
            item.title
          }
        </span>
        <div
          style={styles.sourceArea}
        >
          <span style={styles.source}>{time} </span>
        </div>
      </div>
      {
        hovering &&
        <SlidesActionBar
          item={item}
          style={{ position: 'relative', bottom: '28px', right: '10px', height: '0px' }}
        />
      }
      <div style={styles.seperator} />
    </div>
  )
}

const styles = {
  container: {
    backgroundColor: "white",
    alignContent: 'center',
    paddingLeft: '6px',
    paddingRight: '4px',
    cursor: 'pointer',
    display: 'flex',
    flex: 1,
    flexDirection: 'column',

  },
  itemContent: {
    paddingHorizontal: 12,
  },
  pressedItem: {
    backgroundColor: '#F3F3F3'
  },
  barIcon: {
    color: 'dodgerblue'
  },
  seperator: {
    height: 1,
    backgroundColor: 'lightgray',
  },
  popupMenu: {
    justifyContent: 'center',
  },
  modalContent: {
    width: '70%',
    backgroundColor: 'white',
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'center',
    borderRadius: 4,
    borderColor: 'rgba(0, 0, 0, 0.1)',
  },
  sourceArea: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    justifyContent: 'space-between',
    paddingRight: 4
  },
  source: {
    fontSize: 11,
    color: '#CCCCCC',
  },
};

export default SlidesItem
