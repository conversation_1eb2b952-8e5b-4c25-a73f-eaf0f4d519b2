import { useDispatch } from "react-redux";
import { deleteSlides, moveRILFolder, restoreSlides } from '../../actions/ticketAction';
import { DOC_PERMISSION, RIL_FOLDER, SLIDES_FOLDER } from "../../constants/constants";

import { Edit } from "@styled-icons/fluentui-system-regular/Edit";
import { PlayBtn } from '@styled-icons/bootstrap/PlayBtn';
import { ShareScreenPerson } from '@styled-icons/fluentui-system-regular/ShareScreenPerson';
import { useEffect, useState } from "react";
import { get_server_host } from '../../utils/serverAPIUtil';
import { LIST_ITEM_SELECTED } from "src/constants/actionTypes";
import { useHistory, Link } from 'react-router-dom';
import { FileText } from "@styled-icons/bootstrap/FileText";
import { Tooltip } from '@mui/material';
import { useIntl } from "react-intl";
import { linkToPage } from "src/utils/PageLinkMaker";

const SlidesActionBar = ({ item, style, mode }) => {
  const dispatch = useDispatch();
  const history = useHistory();
  const intl = useIntl();

  const [server_host, setServer_host] = useState();

  useEffect(() => {
    get_server_host().then((value) => setServer_host(value));
  }, [])

  const presentation = () => {
    let uri = server_host + 'present.html?hid=' + item.hid;
    window.open(uri, "_blank")
    return;
  }

  const seminar = () => {
    let uri = server_host + 'seminar.html?hid=' + item.hid;
    window.open(uri, "_blank")
    return;
  }

  const edit = () => {
    // const { navigation, i18n } = this.props;

    // let uri = server_host + 'editor.html?hid=' + item.hid
    // // + '&lng=' + i18n.language;
    // window.open(uri, "_blank")
    history.push({ pathname: '/slidesEditor?hid=' + item.hid, state: { hid: item.hid, hideHeader: true } });
  }

  const read = (event) => {
    history.push(linkToPage(item))
    dispatch({ type: LIST_ITEM_SELECTED, value: item._id });

    event.preventDefault();
    event.stopPropagation();
  }

  let buttonStyle = { ...styles.iconTouchable };
  if (mode === 'fixed_at_viewer') {
    buttonStyle.display = 'flex';
    buttonStyle.justifyContent = 'center';
    buttonStyle.paddingLeft = 0;
    buttonStyle.width = 64
  }

  if (item && item.type === 'slides') {
    style.marginLeft = 58;
  }

  return (
    <div
      style={Object.assign({}, styles.container, style)}
    >
      {
        item && item.type === 'doc' &&
        <div
          style={buttonStyle}
          onClick={read}
        >
          <FileText
            size={36}
            color={'gray'}
          />
        </div>
      }
      {
        item && item.permission >= DOC_PERMISSION.edit &&
        <Tooltip title={intl.formatMessage({ id: 'slide_edit_tooltip' })} placement="bottom-start">
          {/* <div
            style={buttonStyle}
            onClick={edit}
          > */}
          <Link
            style={buttonStyle}
            to={'/slidesEditor?hid=' + item.hid}
          >
            <Edit
              size={mode !== 'fixed_at_viewer' ? 18 : 36}
              color={mode !== 'fixed_at_viewer' ? "rgba(0, 0, 0, 0.38)" : 'gray'}
            />
          </Link>
          {/* </div> */}
        </Tooltip>
      }
      <Tooltip title={intl.formatMessage({ id: 'slide_present_tooltip' })} placement="bottom-start">
        <div
          style={buttonStyle}
          onClick={presentation}
        >
          <PlayBtn
            color={mode !== 'fixed_at_viewer' ? "rgba(0, 0, 0, 0.38)" : 'gray'}
            size={mode !== 'fixed_at_viewer' ? 18 : 36}
          />
        </div>
      </Tooltip>

      {/* <Tooltip title={intl.formatMessage({ id: 'slide_seminar_tooltip' })} placement="bottom-start">
        <div
          style={buttonStyle}
          onClick={seminar}
        >
          <ShareScreenPerson
            color={mode !== 'fixed_at_viewer' ? "rgba(0, 0, 0, 0.38)" : 'gray'}
            size={mode !== 'fixed_at_viewer' ? 18 : 36}
          />
        </div>
      </Tooltip> */}
    </div>
  )
}

const styles = {
  container: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },

  iconTouchable: {
    paddingLeft: 15,
    width: 30,
    alignItems: 'center',
    cursor: 'pointer'
  },
};

export default SlidesActionBar;
