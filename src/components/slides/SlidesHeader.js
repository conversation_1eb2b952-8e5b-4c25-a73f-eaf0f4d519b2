import React, { useState } from "react";
import { Refresh } from '@styled-icons/material/Refresh'
import { useDispatch, useSelector } from "react-redux";
import { REFRESH_SLIDES, SLIDES_FOLDER } from "src/constants/actionTypes";
import { FormControl, MenuItem, Select } from "@mui/material";

const SlidesHeader = () => {
    const dispatch = useDispatch();
    const refresh = () => {
        dispatch({ type: REFRESH_SLIDES });
    }

    const folder = useSelector(state => state.uiState.rilsFolder);
    const folderNames = ['Slides', 'Slides Trashbin'];
    const [folderSelected, setFolderSelected] = useState(folder);


    return <div
        style={{
            display: 'flex',
            backgroundColor: '#f0f0f0',
            width: '100%',
            height: '100%',
            alignItems: 'center',
            justifyContent: 'space-between',
            paddingLeft: '6px',
            paddingRight: '6px'

        }}
    >
        <FormControl variant="standard">
            <Select
                labelId="demo-multiple-chip-label"
                id="demo-multiple-chip"
                value={folderSelected}
                onChange={(event) => {
                    setFolderSelected(event.target.value);
                    dispatch({ type: SLIDES_FOLDER, value: event.target.value })
                }}
            >
                {folderNames.map((name, index) => (
                    <MenuItem
                        key={name}
                        value={index}
                    >
                        {name}
                    </MenuItem>
                ))}
            </Select>
        </FormControl>
        <Refresh
            size={20}
            style={{ paddingRight: 10, paddingLeft: 10, cursor: 'pointer' }}
            onClick={refresh}
        />
    </div>
}

export default SlidesHeader;