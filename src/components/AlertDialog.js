import { useIntl } from 'react-intl';
import { useDispatch, useSelector } from 'react-redux';

import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogTitle from '@mui/material/DialogTitle';
import { ALERT_DIALOG } from 'src/constants/actionTypes';
import { useEffect, useRef } from 'react';

const AlertDialog = () => {
  const dispatch = useDispatch();
  const intl = useIntl();
  const dialogState = useSelector(state => state.uiState.alertDialog) || { visible: false };
  const handleClose = () => {
    if (dialogState.onClose) {
      dialogState.onClose();
    }

    dispatch({ type: ALERT_DIALOG, value: { visible: false } });
  }

  const confirm_btn_ref = useRef();

  useEffect(() => {
    confirm_btn_ref?.current?.focus(); // 设置左按钮初始焦点
  }, [dialogState, confirm_btn_ref]);

  const handleKeyDown = (event) => {
    if (event.key !== 'Enter') {
      event.preventDefault();
      confirm_btn_ref.current.focus();
    }
  };

  return (
    <Dialog
      open={dialogState.visible}
      onClose={handleClose}
      scroll='paper'
      aria-labelledby="scroll-dialog-title"
      aria-describedby="scroll-dialog-description"
      onKeyDown={handleKeyDown}
      tabIndex={0}
    >
      <DialogContent style={{ width: '280px' }} dividers={true}>
        {dialogState.content}
      </DialogContent>
      <DialogActions>
        <Button ref={confirm_btn_ref} onClick={handleClose}>{intl.formatMessage({ id: 'confirm' })}</Button>
      </DialogActions>
    </Dialog>
  );
};


export default AlertDialog;
