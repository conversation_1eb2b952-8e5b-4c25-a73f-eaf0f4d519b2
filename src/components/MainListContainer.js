import React, { Suspense } from 'react'
import { Redirect, Route, Switch } from 'react-router-dom'
import CircularProgress from '@mui/material/CircularProgress';

// routes config
import routes from '../listroutes'
import Header from './mainlist/Header';

const MainListContainer = ({ pathname }) => {
  const route = routes.find(r => r.path === pathname);

  if(!route) {
    return <div></div>
  }
  
  return (
    <div style={{ display: 'flex', flexDirection: 'column' }}>
      <Header>{route.header}</Header>
      <div className="main-list" style={{ flex: 1 }}>
        <Suspense fallback={<CircularProgress />}>
          {
            route && route.component && (

              <route.component />
            )
          }
        </Suspense>
      </div>
    </div>
  )
}

export default React.memo(MainListContainer)
