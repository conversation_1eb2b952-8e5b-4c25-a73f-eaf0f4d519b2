import { Button } from '@mui/material';
import React, { useEffect, useState } from 'react';
import { useIntl } from 'react-intl';
import { useDispatch, useSelector } from 'react-redux';
import { refreshList } from 'src/actions/ticketAction';


const CommonList = ({ list_data, fetcherProps, itemRender }) => {
  const dispatch = useDispatch();
  const [fetchState, setFetchState] = useState();
  const intl = useIntl();

  useEffect(() => {
    setFetchState(fetcherProps);
  }, [fetcherProps])

  useEffect(() => {
    if (!fetchState) {
      return;
    }

    const { list_key, data_fetcher, fetch_params, invalidate } = fetchState;
    fetchState && fetchState.fetch_params && dispatch(refreshList(list_data, data_fetcher, fetch_params, list_key, invalidate))
  }, [fetchState]);

  const handleLoadMore = (list_data) => {
    const { fetch_params } = fetchState;

    if (list_data.isFetching || list_data.isEnd || !list_data.items || list_data.items.length === 0) {
      return;
    }

    let pageFrom = null;
    if (fetch_params && fetch_params.pageBy) {
      pageFrom = list_data.items[list_data.items.length - 1][fetch_params.pageBy];
    }

    setFetchState(prevState => {
      return {
        ...prevState,
        fetch_params: {
          ...fetch_params,
          pageFrom,
        }
      }
    })
  };

  return <div style={{ display: 'flex', flexDirection: 'column', width: '100%' }}>
    {
      list_data.items.map(item => itemRender(item))
    }
    {
      !list_data.isEnd &&
      <div style={{ color: '#999' }}>
        <div
          className="hoverStand1"
          style={{
            textAlign: 'center',
            alignItems: 'center',
            justifyContent: 'center',
            display: 'flex',
            paddingTop: 6,
            paddingBottom: 6
          }}
          onClick={() => handleLoadMore(list_data)}>
          {intl.formatMessage({ id: 'loadmore' })}
        </div>
      </div>
    }
  </div>;
};


export default CommonList
