import React, { useState } from "react";
import { useIntl } from "react-intl";
import { Refresh } from '@styled-icons/material/Refresh'
import { useDispatch, useSelector } from "react-redux";
import { REFRESH_RILS, RILS_FOLDER } from "src/constants/actionTypes";
import { FormControl, MenuItem, OutlinedInput, Select } from "@mui/material";

const RilsHeader = () => {
    const intl = useIntl();
    const dispatch = useDispatch();
    const refresh = () => {
        dispatch({ type: REFRESH_RILS });
    }

    const folder = useSelector(state => state.uiState.rilsFolder);
    const folderNames = [intl.formatMessage({id: 'to_read'}), intl.formatMessage({id: 'read'}) , intl.formatMessage({id: 'trashbin'})];
    const [folderSelected, setFolderSelected] = useState(folder);


    return <div
        style={{
            display: 'flex',
            backgroundColor: '#f0f0f0',
            width: '100%',
            height: '100%',
            alignItems: 'center',
            justifyContent: 'space-between',
            paddingLeft: '6px',
            paddingRight: '6px'

        }}
    >
        <FormControl variant="standard">
            <Select
                labelId="demo-multiple-chip-label"
                id="demo-multiple-chip"
                value={folderSelected}
                onChange={(event) => {
                    setFolderSelected(event.target.value);
                    dispatch({ type: RILS_FOLDER, value: event.target.value })
                }}
            >
                {folderNames.map((name, index) => (
                    <MenuItem
                        key={name}
                        value={index}
                    >
                        {name}
                    </MenuItem>
                ))}
            </Select>
        </FormControl>
        <Refresh
            size={20}
            style={{ paddingRight: 10, paddingLeft: 10, cursor: 'pointer' }}
            onClick={refresh}
        />
    </div>
}

export default RilsHeader;