import React, { Component } from "react";
import { useDispatch } from "react-redux";
import { moveRILFolder } from '../../actions/ticketAction';
import { RIL_FOLDER } from "../../constants/constants";

import { CheckBoxOutlineBlank } from '@styled-icons/material/CheckBoxOutlineBlank'
import { CheckBox } from '@styled-icons/material-outlined/CheckBox'
import { Edit } from '@styled-icons/material/Edit'
import { DeleteOutline } from '@styled-icons/material/DeleteOutline'
import { RestoreFromTrash } from '@styled-icons/material-outlined/RestoreFromTrash'
import { RIL_TITLE_DIALOG } from "src/constants/actionTypes";

const ArticleActionBar = ({ item, readStatusMarked, style, closeMenu, mode }) => {
  const dispatch = useDispatch();

  const markReadStatus = () => {
    let toFolder = RIL_FOLDER.read;
    if (item.folder === RIL_FOLDER.read) {
      toFolder = RIL_FOLDER.ril;
    }

    dispatch(moveRILFolder({ _id: item._id, folder: item.folder }, toFolder, readStatusMarked, 'articlebar'));
  }


  const tagIt = () => {
    // this.props.tagModalRef.toggle(true, item.tags, item._id, this.closeThisMenu);
  }

  const editTitle = () => {
    dispatch({ type: RIL_TITLE_DIALOG, value: { visible: true, item } })
  }

  const closeThisMenu = () => {
    if (closeMenu) {
      closeMenu();
    }
  }

  const trashBinAction = () => {
    let toFolder = RIL_FOLDER.trashbin;
    if (item.folder === RIL_FOLDER.trashbin) {
      toFolder = RIL_FOLDER.ril;
    }

    dispatch(moveRILFolder(item, toFolder, null, 'articlebar'));
  }

  return (
    <div
      style={Object.assign({}, styles.container, style)}
    >
      {
        item.folder === RIL_FOLDER.ril &&
        <div
          style={styles.iconTouchable}
          onClick={markReadStatus}
        >
          <CheckBoxOutlineBlank
            size={18}
            color="rgba(0, 0, 0, 0.38)"
          />
        </div>
      }
      {
        item.folder === RIL_FOLDER.read &&
        <div
          style={styles.iconTouchable}
          onClick={markReadStatus}
        >
          <CheckBox
            size={18}
            color="dodgerblue"
          />
        </div>
      }

      {/* <TouchableItem
          style={styles.iconTouchable}
          onPress={tagIt}
        >
          <View style={{ flexDirection: 'row' }}>
            {
              mode === BAR_MODE.actionBar && !!item.tags && item.tags.length > 0 &&
              <Icon
                name="tag-outline"
                type="material-community"
                color="dodgerblue"
                size={20}
                style={{ backgroundColor: 'transparent' }}
              />
            }
            {
              (mode !== BAR_MODE.actionBar || !item.tags || item.tags.length == 0) &&
              <Icon
                name="tag-outline"
                type="material-community"
                color="rgba(0, 0, 0, 0.38)"
                size={20}
                style={{ backgroundColor: 'transparent' }}
              />
            }
          </View>
        </TouchableItem> */}

      {
        mode !== BAR_MODE.actionBar &&
        <div
          style={styles.iconTouchable}
          onClick={editTitle}
        >
          <Edit
            size={18}
            color="rgba(0, 0, 0, 0.38)"
          />
        </div>
      }
      {
        mode !== BAR_MODE.actionBar &&
        item.folder !== RIL_FOLDER.trashbin &&
        <div
          style={styles.iconTouchable}
          onClick={trashBinAction}
        >
          <DeleteOutline
            size={19}
            color="rgba(0, 0, 0, 0.38)"
          />
        </div>
      }
      {
        mode !== BAR_MODE.actionBar &&
        item.folder === RIL_FOLDER.trashbin &&
        <div
          style={styles.iconTouchable}
          onClick={trashBinAction}
        >
          <RestoreFromTrash
            size={19}
            color="dodgerblue"
          />
        </div>
      }

    </div>

  )
}

const styles = {
  container: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'flex-end',
    paddingHorizontal: 6
  },
  popupStyle: {
    flexDirection: "column",
    paddingVertical: 10
  },
  iconTouchable: {
    paddingLeft: 15,
    width: 30,
    alignItems: 'center',
    cursor: 'pointer'
  },
  sibling: {
    position: 'absolute',
    left: 0,
    height: 200,
    // width: Dimensions.get('window').width / 2,
    backgroundColor: 'blue',
    opacity: 0.5
  }
};

export const BAR_MODE = {
  itemMenu: 1,
  popupMenu: 2,
  actionBar: 3,
}

export default ArticleActionBar;
