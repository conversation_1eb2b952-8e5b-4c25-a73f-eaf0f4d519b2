import React from "react";
import { View, StyleSheet, Text } from "react-native";
import { MarkdownStyle } from "../styles/MarkdownStyle";

class HighlightItem extends React.Component {
  render() {
    const { item } = this.props;

    return <View style={styles.container}>
      <View style={styles.quoteArea}>
        <View style={{ width: MarkdownStyle.blockquote.borderLeftWidth, backgroundColor: MarkdownStyle.blockquote.borderLeftColor }} />
        <Text style={styles.quoteText}>{item.highlight.content}</Text>
      </View>
      {
        item.note &&
        <Text style={styles.note}>
          {item.note.note}
        </Text>
      }
      {
        this.props.bottomBar
      }
    </View>

  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'flex-start',
    paddingBottom: 10,
  },
  note: {
    color: '#333333',
    paddingTop: 2,
  },
  quoteArea: {
    flex: 1,
    backgroundColor: MarkdownStyle.blockquote.backgroundColor,
    flexDirection: 'row',
  },
  quoteText: {
    flex: 1,
    color: '#333333',
    paddingVertical: 4,
    paddingHorizontal: 2
  }
});

export default HighlightItem;
