/* @flow */

import * as React from 'react';
import { <PERSON>alog, DialogTitle, DialogContent, TextField, Button, DialogActions } from '@mui/material';

import { useDispatch, useSelector } from 'react-redux';
import { NOTE_DIALOG } from 'src/constants/actionTypes';
import { addHighlightNote } from 'src/actions/ticketAction';
import { getState } from 'src/reducers/listReducer';
import { useIntl } from 'react-intl';

const HighlightNoteEditorModal = ({ articleId, noteAdded }) => {
  const dispatch = useDispatch();
  const intl = useIntl();
  const noteDialogState = useSelector(state => state.uiState.noteDialog);
  const article_highlights = useSelector(state => getState(state.article_highlight_lists, articleId));

  const [note, setNote] = React.useState();
  const [highlight, setHighlight] = React.useState();
  const [noteValid, setNoteValid] = React.useState(true);

  React.useEffect(() => {
    if (!noteDialogState.item) {
      return;
    }

    const hls = getSavedHighlights(noteDialogState.item);
    if (!hls || !hls.length) {
      return;
    }

    setHighlight(hls[0]);
    setNote(hls[0].note && hls[0].note.note);

  }, [noteDialogState.item]);

  const getSavedHighlights = React.useCallback((highlight) => {
    if (!highlight) {
      return;
    }

    const hls = article_highlights.items.filter(item => {
      if (!item.highlight) {
        return;
      }

      if (highlight.start) {
        return String(item.highlight.start) === String(highlight.start) && String(item.highlight.end) === String(highlight.end);
      } else {
        return String(item.highlight.end) === String(highlight.end);
      }
    });

    return hls;
  }, [articleId, article_highlights]);

  const addNote = () => {
    let isNoteValid = note.length > 0;
    setNoteValid(isNoteValid);

    if (!isNoteValid) {
      return;
    }

    dispatch(addHighlightNote({ articleId, note, start: highlight.highlight.start, end: highlight.highlight.end }, (item) => {
      noteAdded(item);
      close();
    }, 'Modal'));
  };

  const close = () => {
    dispatch({ type: NOTE_DIALOG, value: { visible: false, item: {} } })
  }

  return (
    <Dialog
      open={!!noteDialogState && noteDialogState.visible}
      onClose={close}
      scroll='paper'
      aria-labelledby="scroll-dialog-title"
      aria-describedby="scroll-dialog-description"
    >
      <DialogTitle id="scroll-dialog-title" style={{ display: 'flex', justifyContent: 'space-between' }}>
        <span>Add note</span>
      </DialogTitle>
      <DialogContent dividers={true}>
        <TextField
          required
          id="note"
          label="Note"
          multiline={true}
          rows={8}
          style={{ width: '480px' }}
          value={note}
          onChange={(e) => setNote(e.target.value)}
          error={!noteValid}
        />
      </DialogContent>
      <DialogActions>
        <Button onClick={close}> {intl.formatMessage({ id: 'cancel' })} </Button>
        <Button onClick={addNote}>{intl.formatMessage({ id: 'confirm' })} </Button>
      </DialogActions>
    </Dialog>
  )
}

export default HighlightNoteEditorModal;
