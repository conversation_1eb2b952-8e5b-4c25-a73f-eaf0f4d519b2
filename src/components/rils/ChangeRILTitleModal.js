import { <PERSON><PERSON>, DialogTitle, <PERSON>alog<PERSON>ontent, TextField, Button, DialogActions } from '@mui/material';
import * as React from 'react';
import { connect, useDispatch, useSelector } from 'react-redux';
import { updateRIL } from 'src/actions/ticketAction';
import { RIL_TITLE_DIALOG } from 'src/constants/actionTypes';

const ChangeRILTitleModal = ({ changeTitle }) => {
  const rilTitleDialogState = useSelector(state => state.uiState.rilTitleDialog);
  const dispatch = useDispatch();
  const [title, setTitle] = React.useState('');
  const [titleValid, setTitleValid] = React.useState(true);

  const changeRilTitle = () => {
    if(!title || title.length<2) {
      setTitleValid(false);
      return;
    }
    dispatch(updateRIL({ _id: rilTitleDialogState.item._id, data: { title } }, handleClose, 'rilTitle'))
  }

  const handleClose = () => {
    dispatch({ type: RIL_TITLE_DIALOG, value: { visible: false } });
  }

  return (
    <Dialog
      open={!!rilTitleDialogState && rilTitleDialogState.visible}
      onClose={handleClose}
      scroll='paper'
      aria-labelledby="scroll-dialog-title"
      aria-describedby="scroll-dialog-description"
    >
      <DialogTitle id="scroll-dialog-title" style={{ display: 'flex', justifyContent: 'space-between' }}>
        <span>Change Title</span>
      </DialogTitle>
      <DialogContent dividers={true}>
        <TextField
          required
          id="title"
          label="Title"
          style={{width: '360px'}}
          defaultValue={rilTitleDialogState && rilTitleDialogState.item && rilTitleDialogState.item.title}
          onChange={(e) => setTitle(e.target.value)}
          error={!titleValid}
        />
      </DialogContent>
      <DialogActions>
        <Button onClick={handleClose}>Cancel</Button>
        <Button onClick={changeRilTitle}>Confirm</Button>
      </DialogActions>
    </Dialog>
  )
}


export default ChangeRILTitleModal;
