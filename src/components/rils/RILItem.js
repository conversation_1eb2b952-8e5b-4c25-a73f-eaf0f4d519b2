/* @flow */

import React, { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useHistory, Link } from 'react-router-dom';

import ArticleItem from "./ArticleItem";
import ArticleActionBar from "./ArticleActionBar";
import { LIST_ITEM_SELECTED } from "src/constants/actionTypes";


const RILItem = ({ item, changeTitleModalRef }) => {
  const itemSelected = useSelector(state => state.uiState.list_item_selected);
  const pressed = item._id === itemSelected;
  const articleItem = <ArticleItem
    item={{ ...item.article, title: item.title, tags: item.tags, createdAt: item.createdAt }}
    wrapper={item}
  />;

  const [hovering, setHovering] = useState(false);
  const dispatch = useDispatch();

  const history = useHistory();

  const openArticle = () => {
    history.push({ pathname: '/article', search: '?id=' + item.article._id, state: { title: item.title, id: item.article._id, item } })
    dispatch({type: LIST_ITEM_SELECTED, value: item._id})
  }

  const hasTitleIcon = item.article && item.article.imgext && item.article.imgext.length > 0;
  return (
    <div
      style={Object.assign({}, styles.container, pressed && styles.pressedItem)}
      onMouseOver={() => setHovering(true)}
      onMouseLeave={() => setHovering(false)}
      onClick={openArticle}
    >
      {articleItem}
      {
        hovering &&
        <ArticleActionBar
          item={item}
          style={{ position: 'relative', bottom: '28px', right: hasTitleIcon? '100px': '10px', height: '0px' }}
        />
      }
      <div style={styles.seperator} />
    </div>
  )
}

const styles = {
  container: {
    backgroundColor: "white",
    alignContent: 'center',
    paddingLeft: '6px',
    paddingRight: '4px',
    cursor: 'pointer',
  },
  itemContent: {
    paddingHorizontal: 12,
  },
  pressedItem: {
    backgroundColor: '#F3F3F3'
  },
  barIcon: {
    color: 'dodgerblue'
  },
  seperator: {
    height: 1,
    backgroundColor: 'lightgray',
  },
  popupMenu: {
    justifyContent: 'center',
  },
  modalContent: {
    width: '70%',
    backgroundColor: 'white',
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'center',
    borderRadius: 4,
    borderColor: 'rgba(0, 0, 0, 0.1)',
  },

  standaloneRowFront: {
    alignItems: 'center',
    backgroundColor: 'white',
    justifyContent: 'center',
  },
  standaloneRowBack: {
    alignItems: 'center',
    backgroundColor: 'powderblue',
    flex: 1,
    flexDirection: 'row',
    alignContent: 'flex-end',
    justifyContent: 'space-between',
  },
};

export default RILItem
