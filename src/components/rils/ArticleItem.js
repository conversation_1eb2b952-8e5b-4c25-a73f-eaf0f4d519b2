import React, { useState } from "react";

import * as TimeFormater from '../../utils/timeFormater';
import { ARTICLE_STATUS, isWeb } from "../../constants/constants";
import Tags from './Tags'

const ArticleItem = ({ item, mode }) => {

  let source = item.source || 'Unknown source';
  if (parseInt(item.status) === ARTICLE_STATUS.gathering) {
    source = '采集中';
  } else if (parseInt(item.status) === ARTICLE_STATUS.failed) {
    source = '原网页';
  }

  const time = item.updatedAt && TimeFormater.formatFromNow(item.createdAt, 'MM-DD') || 'just now';
  const isQuoted = mode === 'quoted';
  const hasTitleIcon = item.imgext && item.imgext.length > 0;

  return <div
    style={Object.assign({}, styles.container, isQuoted && styles.quotedContainer)}
  >
    <div
      style={Object.assign({ display: 'flex', flexDirection: 'column', flex: 1, justifyContent: 'space-between' }, isQuoted && { alignSelf: 'center' })}
    >
      <span style={{
        textOverflow: 'ellipsis',
        position: 'relative'
      }}>
        {
          item.title
        }
      </span>
      {
        !isQuoted &&
        <div
          style={styles.sourceArea}
        >
          <span
            style={styles.source}
          >{`${source} · ${time}`}</span>
          {
            item.tags &&
            <Tags tags={item.tags} />
          }
        </div>
      }
    </div>
    {
      hasTitleIcon &&
      <img
        style={{ width: 100, height: 64, objectFit: 'cover' }}
        src={item.imgext[0]}
      />
    }
  </div>
}

const styles = {
  container: {
    display: 'flex',
    flex: 1,
    flexDirection: 'row',
    paddingHorizontal: 12,
    paddingTop: 12,
    paddingBottom: 8,
  },
  quotedContainer: {
    backgroundColor: '#f2f2f2',
    flexDirection: 'row-reverse',
    paddingHorizontal: 0,
    paddingTop: 0,
    paddingBottom: 0
  },
  title: {
    color: '#333333',
  },
  sourceArea: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    justifyContent: 'space-between',
    paddingRight: 4
  },
  source: {
    fontSize: 11,
    color: '#CCCCCC',
  },
  imageStyle: {
    width: 100,
    height: 60
  }
};

export default ArticleItem;
