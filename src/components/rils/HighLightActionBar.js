import { useDispatch } from 'react-redux';
import { deleteHighlight } from '../../actions/ticketAction';
import { useIntl } from 'react-intl';

const HighLightActionBar = ({ articleId, savedHighlight, addNote, highlight, highlightRemoved, copied }) => {

  const dispatch = useDispatch();
  const intl= useIntl();

  const remove = () => {
    let selector = null;
    if (savedHighlight && savedHighlight._id) {
      selector = { articleId, highlightId: savedHighlight._id };
    } else {
      selector = { articleId, highlight };
    }

    dispatch(deleteHighlight(selector, highlightRemoved, 'highlightbar'));
  }

  const addSomeNotes = () => {
    addNote(savedHighlight);
  }

  const share = () => {

  }

  const copy = () => {
    navigator.clipboard.writeText(savedHighlight.highlight.content)
    copied && copied();
  }

  const styles = {
    barIcon: {
      color: 'dodgerblue'
    },
    barStyle: {
    }
  };

  return (
    <div style={{ display: 'flex', flexDirection: 'row', justifyContent: 'space-evenly' }}>
      <div
        style={{ cursor: 'pointer' }}
        onClick={remove}
      > {intl.formatMessage({id: 'delete'})} </div>

      <div
        style={{ cursor: 'pointer' }}
        onClick={addSomeNotes}
      >{intl.formatMessage({id: 'note'})} </div>

      <div
        style={{ cursor: 'pointer' }}
        onClick={copy}
      >{intl.formatMessage({id: 'copy'})}</div>

      {/* <div
        style={{ cursor: 'pointer' }}
        onClick={share}
      >
        {intl.formatMessage({id: 'share'})}
      </div> */}
    </div>
  )
}

export default HighLightActionBar;
