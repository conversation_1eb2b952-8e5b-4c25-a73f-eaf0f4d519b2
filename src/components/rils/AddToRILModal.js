import { useDispatch, useSelector } from 'react-redux';
import * as TicketAction from '../../actions/ticketAction';
import * as Validator from '../../utils/validator';
import { SHOWN_CLIPBOARD_URL } from '../../constants/actionTypes';
import { useState } from 'react';
import { RIL_DIALOG } from 'src/constants/actionTypes';
import { Button, Dialog, DialogActions, DialogContent, DialogTitle, TextField } from '@mui/material';
import { useIntl } from 'react-intl';

const AddToRILModal = (props) => {
  const screen = 'popup';
  const dispatch = useDispatch();

  const { trigger, addedToRIL } = props;

  const [urlValid, setUrlValid] = useState(true);
  const [titleValid, setTitleValid] = useState(true);
  const [gettingTitle, setGettingTitle] = useState(false);
  const [title, setTitle] = useState(props.title);
  const [titleChanged, setTitleChanged] = useState();
  const [loading, setLoading] = useState(false);
  const rilModalState = useSelector(state => state.uiState.rilDialog);
  const [url, setUrl] = useState(rilModalState && rilModalState.url);
  const intl = useIntl();

  const getTitle = () => {
    let isUrlValid = Validator.validateUrl(url);
    setUrlValid(isUrlValid);

    if (!isUrlValid) {
      return;
    }

    setTitle(null);
    setGettingTitle(true);

    dispatch(TicketAction.getArticleTitle({ url }, titleGot, () => {
      setGettingTitle(false);
      // showMessage('Failed to get title');
    }, screen));
  };

  const titleGot = (item) => {
    setTitle(item.title);
    setGettingTitle(false);
  }

  const addToRIL = () => {
    let isUrlValid = Validator.validateUrl(url);
    setUrlValid(isUrlValid);

    if (!isUrlValid) {
      return;
    }

    setLoading(true);
    dispatch(TicketAction.addURLToRIL({ url, title }, (item) => {
      if (addedToRIL) {
        addedToRIL(item);
      }
      setLoading(false);
      handleClose();
    }, failedToRIL, screen));
  }


  const failedToRIL = (msg) => {
    // showMessage(msg);
    setLoading(false);
  }

  const handleClose = () => {

    if (trigger === 'clipboard' && url) {
      dispatch({ type: SHOWN_CLIPBOARD_URL, value: url });
    }

    setTitleChanged(false);
    setUrl(null);
    setTitle(null);
    setLoading(false);

    dispatch({ type: RIL_DIALOG, value: { visible: false, url: '' } });
  }

  const triggeredFromClipboard = trigger === 'clipboard';
  const modalTitle = triggeredFromClipboard ? '剪切板中发现文章链接，保存到稍后阅读？' : '保存文章到稍后阅读'

  return (
    <Dialog
      open={!!rilModalState && rilModalState.visible}
      onClose={handleClose}
      scroll='paper'
      aria-labelledby="scroll-dialog-title"
      aria-describedby="scroll-dialog-description"
    >
      <DialogTitle id="scroll-dialog-title" style={{ display: 'flex', justifyContent: 'space-between' }}>
        <span>{modalTitle}</span>
      </DialogTitle>
      <DialogContent dividers={true}>
        {
          triggeredFromClipboard &&
          <div style={styles.form}>
            <span style={styles.inputTitle}>{intl.formatMessage({ id: 'type_url' })} </span>
            <span style={{ flex: 1 }}>{this.props.url}</span>
          </div>
        }
        {
          !triggeredFromClipboard &&
          <div style={styles.form}>
            <span style={styles.inputTitle}>{intl.formatMessage({ id: 'type_url' })} </span>
            <TextField
              required
              id="url"
              label="Url"
              style={styles.input}
              defaultValue={url}
              onChange={(e) => setUrl(e.target.value)}
              // onBlur={getTitle}
              error={!urlValid}
            />
            {/* <Button
              variant="outlined"
              onClick={getTitle}
            // loadingPosition="start"
            // loading={gettingTitle}
            >
              {intl.formatMessage({ id: 'get_title' })}
            </Button> */}
          </div>
        }
        {/* <div style={styles.form}>
          <span style={styles.inputTitle}> {intl.formatMessage({ id: 'title' })} </span>
          <TextField
            required={false}
            id="title"
            label="Title"
            style={styles.input}
            value={title}
            onChange={(e) => { setTitle(e.target.value); setTitleChanged(true) }}
            error={!titleValid}
          />
        </div> */}
      </DialogContent>
      <DialogActions>
        <Button onClick={handleClose}>{intl.formatMessage({ id: 'cancel' })}</Button>
        <Button
          onClick={addToRIL}
        // loadingPosition="start"
        // loading={loading}
        >
          {intl.formatMessage({ id: 'confirm' })}
        </Button>
      </DialogActions>
    </Dialog>
  )
}

const styles = {
  bottomModal: {
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: 'white',
    paddingTop: 16,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 4,
    borderColor: 'rgba(0, 0, 0, 0.1)',
  },
  form: {
    marginBottom: 16,
    marginHorizontal: 16,
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start'
  },
  inputTitle: {
    color: 'dodgerblue',
    fontSize: 16,
    fontWeight: '600'
  },
  input: {
    flex: 1,
    borderWidth: 1,
    width: '360px',
    marginLeft: '10px',
    marginRight: '10px'
  },
  button: {
    alignSelf: 'center',
    paddingHorizontal: 20,
    paddingVertical: 5,
    borderRadius: 20
  },
};


export default AddToRILModal;
