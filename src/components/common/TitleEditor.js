import { useEffect, useLayoutEffect, useState, createRef } from "react";
import { useIntl } from "react-intl";
import { useDispatch, useSelector } from "react-redux";
import { upsertDoc } from "src/actions/ticketAction";
import { TITLE_SELECTION } from "src/constants/actionTypes";

export const TitleEditor = ({ containerStyle, inputStyle, doc, title, placeholder, readOnly, onChange, onSubmit, onKeyPress, onKeyDown, onFocus, inputRef, space, saveTitleExternal, autoFocus }) => {
    const dispatch = useDispatch();
    const [value, setValue] = useState();
    const loginUser = useSelector(state => state.loginIn.user);
    const titleSelected = useSelector(state => state.uiState.titleSelected);
    const intl = useIntl();

    if (!inputRef) {
        inputRef = createRef();
    }

    useLayoutEffect(() => {
        setValue(title);
        titleSelected && dispatch({
            type: TITLE_SELECTION,
            value: false
        })
    }, [title]);

    useEffect(() => {
        if (inputRef && inputRef.current) {
            inputRef.current.style.height = "inherit";
            inputRef.current.style.height = `${inputRef.current.scrollHeight}px`;
        }
    }, [value]);

    useEffect(() => {
        if (inputRef && inputRef.current) {
            if (titleSelected) {
                inputRef.current.style.backgroundColor = "rgba(79, 144, 242, 0.3)";
            } else {
                inputRef.current.style.backgroundColor = "transparent";
            }
        }
    }, [titleSelected]);

    const handleChange = (e) => {
        setValue(e.target.value);
        if (onChange) {
            onChange(e.target.value);
        }
    }

    const saveTitle = () => {
        if (!!doc?.hid && !saveTitleExternal && value != doc.title) {
            dispatch(upsertDoc({ data: { doc: { title: value, hid: doc.hid }, space, orgId: loginUser.workingOrgId } }, (updatedDoc) => {
                console.log('doc saved for title');
            }))
        }
    }

    const handleSubmit = (e) => {
        e.preventDefault();
        e.stopPropagation();

        saveTitle();

        if (onSubmit) {
            onSubmit(value);
        }
    }

    const handleKeyDown = (e) => {
        if ((e.metaKey || e.ctrlKey) && e.key === 's') {
            saveTitle();
            e.preventDefault();
            e.stopPropagation();
        }

        if (onKeyDown) {
            onKeyDown(e);
        }

        if (!inputRef) {
            e.currentTarget.style.height = "inherit";
            e.currentTarget.style.height = `${e.currentTarget.scrollHeight}px`;
        }
    }

    const handleKeyPress = (e) => {
        if (onKeyPress) {
            onKeyPress(e);
        }

        if (e.key === 'Enter') {
            e.preventDefault();
            e.stopPropagation();
        }

        if (!inputRef) {
            e.currentTarget.style.height = "inherit";
            e.currentTarget.style.height = `${e.currentTarget.scrollHeight}px`;
        }
    }

    return <div style={{
        paddingTop: 44,
        paddingBottom: 8,
        paddingLeft: 48,
        paddingRight: 16,
        height: 'fit-content',
        width: '-webkit-fill-available',
        alignSelf: 'center',
        maxWidth: '800px',
        fontSize: '3em',
        fontWeight: 'bold',
        ...containerStyle,
    }}>
        {
            readOnly && value
        }
        {
            !readOnly &&
        <textarea
            ref={inputRef}
            value={value}
            style={{
                border: 'none',
                outline: 'none',
                width: '-webkit-fill-available',
                fontFamily: 'inherit',
                // lineHeight: 1.2,
                resize: "none",
                ...inputStyle,
            }}
            rows={1}
            placeholder={placeholder || intl.formatMessage({ id: 'title' })}
            onChange={handleChange}
            onBlur={handleSubmit}
            onKeyDown={handleKeyDown}
            onKeyPress={handleKeyPress}
            onFocus={onFocus}
            autoFocus={autoFocus}
        />
    }
    </div>
}