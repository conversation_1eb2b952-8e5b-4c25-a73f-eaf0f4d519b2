import { useEffect } from 'react';

export function usePreventHistoryGesture() {
  useEffect(() => {
    // 阻止 popstate（防止历史后退/前进）
    const onPopState = () => {
      window.history.pushState(null, '', window.location.href);
    };

    // 阻止横向滚动触发（减少手势引发历史切换）
    const preventHorizontalScroll = (e) => {
      if (Math.abs(e.deltaX) > Math.abs(e.deltaY)) {
        e.preventDefault();
      }
    };

    // 初始化一条 history 记录
    window.history.pushState(null, '', window.location.href);
    window.addEventListener('popstate', onPopState);
    window.addEventListener('wheel', preventHorizontalScroll, { passive: false });

    return () => {
      window.removeEventListener('popstate', onPopState);
      window.removeEventListener('wheel', preventHorizontalScroll);
    };
  }, []);
}
