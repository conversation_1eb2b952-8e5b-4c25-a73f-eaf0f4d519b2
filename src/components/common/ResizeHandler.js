import React from "react";

export const ResizeHandler = ({ onResize, onResizeEnd, style }) => {
    const [hovered, setHovered] = React.useState(false);

    const handler = (mouseDownEvent) => {
        const startPoint = { x: mouseDownEvent.pageX, y: mouseDownEvent.pageY };

        function onMouseMove(mouseMoveEvent) {
            onResize({
                x:  mouseMoveEvent.pageX - startPoint.x,
            });
        }
        function onMouseUp(mouseMoveEvent) {
            document.body.removeEventListener("mousemove", onMouseMove);
            onResizeEnd({ x: mouseMoveEvent.pageX - startPoint.x });
        }

        document.body.addEventListener("mousemove", onMouseMove);
        document.body.addEventListener("mouseup", onMouseUp, { once: true });
    };

    return (
        <div
            style={{
                width: 6,
                height: 24,
                backgroundColor: hovered ? '#999' : 'transparent',
                cursor: 'col-resize',
                zIndex: 5,
                ...style,
            }}

            onMouseEnter={() => setHovered(true)}
            onMouseLeave={() => setHovered(false)}

            onMouseDown={handler}
        />
    );
}