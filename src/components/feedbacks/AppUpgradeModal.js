import * as React from 'react';
import { APP_UPGRADE_DIALOG } from 'src/constants/actionTypes';
import { useDispatch, useSelector } from 'react-redux';
import { DialogContent, Dialog, Button, DialogTitle, Fab, Divider } from '@mui/material';


import { useIntl } from 'react-intl';

const AppUpgradeModal = () => {
    const intl = useIntl();
    const dialogState = useSelector(state => state.uiState.appUpgradeDialog) || { visible: false };
    const dispatch = useDispatch();

    const handleClose = () => {
        dispatch({ type: APP_UPGRADE_DIALOG, value: { visible: false } });
    }

    return (
        <Dialog
            open={!!dialogState && dialogState.visible}
            onClose={handleClose}
            scroll='paper'
            aria-labelledby="scroll-dialog-title"
            aria-describedby="scroll-dialog-description"
            maxWidth='md'
            style={{
                zIndex: 100,
            }}
        >
            <div style={{ width: '100%', display: 'flex', flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
                <div style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', paddingLeft: 8 }}>
                    {intl.formatMessage({ id: "notice" })}
                </div>
                <Button variant='text' onClick={handleClose}>{intl.formatMessage({ id: 'close' })}</Button>
            </div>
            <DialogContent dividers={true} style={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-start', width: 500, padding: 0, backgroundColor: 'white' }}>
                <div style={{ margin: 10, marginTop: 20, display: 'flex', flexDirection: 'column', rowGap: 6 }}>{intl.formatMessage({ id: dialogState?.reason ==='err' ? 'err_catched' : 'new_version_available' })}</div>

                <Button variant='contained' style={{ margin: 20, marginTop: 10, alignSelf: 'center' }} onClick={() => { window.location.reload(); handleClose(); }}>{intl.formatMessage({ id: dialogState?.reason ==='err' ? 'reload' : 'upgrade_now' })}</Button>
            </DialogContent>
        </Dialog>
    );
}

export default AppUpgradeModal;
