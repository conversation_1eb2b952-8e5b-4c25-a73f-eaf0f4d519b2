import * as React from 'react';
import { FEEDBACKS_DIALOG } from 'src/constants/actionTypes';
import { useDispatch, useSelector } from 'react-redux';
import { DialogContent, Dialog, Button, DialogTitle, Fab, Divider } from '@mui/material';
import { Question } from "@styled-icons/bootstrap/Question";
import { Message } from '@styled-icons/material/Message';
import { Send } from '@styled-icons/material/Send';
import { KeyboardArrowLeft } from '@styled-icons/material/KeyboardArrowLeft';

import { useIntl } from 'react-intl';

import { fetchFeedbacks, feedback as submitFeedback, getFeedbackTypeOptions } from 'src/actions/ticketAction';
import { getStateByUser } from 'src/reducers/listReducer';
import EditorCore from '../editor/EditorCore';
import { Selector } from '../common/Selector';

const FeedbackItem = ({ feedback }) => {
    const intl = useIntl();

    return <div style={{
        marginTop: 4,
        marginBottom: 4,
        padding: 8,
        paddingLeft: 12,
        paddingRight: 12,
        backgroundColor: 'white',
        width: '-webkit-fill-available'
    }}>
        {
            !feedback.content && feedback.title
        }
        {
            !!feedback.content &&
            <EditorCore
                content={feedback.content}
                forceTitle={false}
                readOnly={true}
            />
        }
        {
            !!feedback.reply &&
            <div style={{ paddingTop: 8 }}>
                <Divider />
                <div style={{ paddingTop: 8, color: '#666', fontSize: 13 }}>
                    {intl.formatMessage({ id: 'reply' }) + ": " + feedback.reply}
                </div>
            </div>
        }
    </div>
}

const FeedbackEditor = ({ onContentChange, onTypeChange, value }) => {
    const dispatch = useDispatch();
    const intl = useIntl();

    const [options, setOptions] = React.useState();

    React.useEffect(() => {
        dispatch(getFeedbackTypeOptions({}, (options) => {
            setOptions(options);
        }))
    }, []);

    return <>
        {
            options &&
            <div style={{ display: 'flex', flexDirection: 'row', justifyContent: 'flex-start', alignItems: 'center', width: '-webkit-fill-available', padding: 4, paddingLeft: 12, columnGap: 8 }}>
                {intl.formatMessage({ id: 'type' })}
                <Selector
                    onChange={onTypeChange}
                    options={options}
                    value={value.type}
                />
            </div>
        }

        <EditorCore
            forceTitle={false}
            onChange={onContentChange}
        />
    </>
}

const FeedbacksModal = () => {
    const intl = useIntl();
    const dialogState = useSelector(state => state.uiState.feedbacksDialog) || { visible: false };
    const dispatch = useDispatch();
    const user = useSelector(state => state.loginIn.user);
    const feedbacks = useSelector(state => getStateByUser(state.feedback_lists, user));

    const handleClose = () => {
        dispatch({ type: FEEDBACKS_DIALOG, value: { visible: false } });
    }

    React.useEffect(() => {
        dispatch(fetchFeedbacks({}))
    }, [dialogState.visible]);

    const [editMode, setEditMode] = React.useState(false);
    const [value, setValue] = React.useState({});
    const sendFeedback = () => {
        if (!value || !value.content || !value.content.length) {
            return;
        }

        const validContent = value.content.filter(b => !(b.type == 'p' && !b.children[0]?.text?.trim()));
        if (!validContent.length) {
            return;
        }

        dispatch(submitFeedback({
            data: {
                ...value,
                username: user?.nickname,
                app: 'funblocks_web'
            }
        }, () => {
            setEditMode(false);
        }, 'feedback'));
    }

    return (
        <Dialog
            open={!!dialogState && dialogState.visible}
            onClose={handleClose}
            scroll='paper'
            aria-labelledby="scroll-dialog-title"
            aria-describedby="scroll-dialog-description"
            maxWidth='md'
            style={{
                zIndex: 100,
            }}
        >
            <div style={{ width: '100%', display: 'flex', flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
                <div style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', paddingLeft: 4 }}>
                    {
                        !!editMode &&
                        <div style={{ padding: 4 }} onClick={() => setEditMode(!editMode)}>
                            <KeyboardArrowLeft size={24} />
                        </div>
                    }
                    {intl.formatMessage({ id: "feedback" })}
                </div>
                <Button variant='text' onClick={handleClose}>{intl.formatMessage({ id: 'close' })}</Button>
            </div>
            <DialogContent dividers={true} style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', height: 820, width: 600, padding: 0, backgroundColor: 'whitesmoke' }}>
                {
                    !editMode && feedbacks?.items.map((feedback, i) => <FeedbackItem key={i + ''} feedback={feedback} />)
                }
                {
                    editMode && <FeedbackEditor
                        onContentChange={(content) => {
                            setValue({
                                ...value,
                                content
                            })
                        }}
                        onTypeChange={(type) => {
                            setValue({
                                ...value,
                                type
                            })
                        }}

                        value={value}
                    />
                }
            </DialogContent>


            <div style={{
                position: 'absolute',
                bottom: 20,
                right: 16,
            }}>
                <Fab style={{ backgroundColor: 'white', color: 'dodgerblue' }} size='medium' aria-label="add"
                    onClick={(e) => {
                        if (editMode) {
                            sendFeedback();
                        } else {

                            setEditMode(!editMode);
                        }
                    }}
                >
                    {
                        !editMode &&
                        <Message size={30} />
                    }
                    {
                        editMode &&
                        <Send size={30} />
                    }

                </Fab>
            </div>
        </Dialog>
    );
}

export default FeedbacksModal;
