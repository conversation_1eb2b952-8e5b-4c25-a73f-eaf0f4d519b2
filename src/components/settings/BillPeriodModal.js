import React, { useState } from 'react';
import ReactDOM from 'react-dom';
import { useDispatch, useSelector } from 'react-redux';
import { Button, Dialog, DialogActions, DialogContent, Divider } from '@mui/material';
import { BILL_PERIOD_DIALOG, STRIPE_CHECKOUT_DIALOG } from 'src/constants/actionTypes';
import { useIntl } from 'react-intl';
import LoadingButton from '@mui/lab/LoadingButton/LoadingButton';
import { getServingProducts, initSubscription } from 'src/actions/ticketAction';

export const BillPeriodModal = () => {
    const dispatch = useDispatch();
    const dialogState = useSelector(state => state.uiState.billPeriodDialog) || {};
    const available_products = useSelector(state => state.uiState.available_products);
    const intl = useIntl();
    const [loading, setLoading] = useState();
    const { service, sku, visible, trial_days } = dialogState;

    const title = service && intl.formatMessage({ id: 'checkout_form_title_' + service }, { level: sku.service_level });

    const [bill_period, set_bill_period] = useState('Yearly');

    const bill_periods = [{
        period: 'Monthly',
    }, {
        period: 'Yearly',
        desc: '20% off'
    }]

    const handleClose = () => {
        dispatch({
            type: BILL_PERIOD_DIALOG,
            value: {
                visible: false
            }
        })
    }

    const handleSubscription = (service, title) => {
        setLoading({ service })
        let sku = available_products?.skus.find(sku => sku.productId.indexOf(service) > -1 && sku.recurring?.toLowerCase() == bill_period.toLowerCase())
        dispatch(initSubscription({ productId: sku.productId, channel: 'stripe', service }, (paymentIntent) => {
            setLoading(null);

            if (paymentIntent?.refreshServingProducts) {
                dispatch(getServingProducts({ services: ['funblocks', 'aiplus'] }, null, 'servicesubscribe'));
            }

            if (paymentIntent?.clientSecret) {
                dispatch({
                    type: STRIPE_CHECKOUT_DIALOG,
                    value: {
                        visible: true,
                        paymentIntent,
                        skus: [sku],
                        service,
                        title
                    }
                })
            }

            handleClose();
        }, () => setLoading(null)))
    }

    return (
        <Dialog
            open={visible}
            onClose={handleClose}
            scroll='paper'
            aria-labelledby="scroll-dialog-title"
            aria-describedby="scroll-dialog-description"

        >
            <DialogContent dividers={true} style={{ display: 'flex', flexDirection: 'column', rowGap: '20px' }}>
                <div style={{ fontSize: 18, fontWeight: 'bold' }}>
                    {
                        title
                    }
                </div>
                <div style={{ fontSize: 14, color: '#666' }}>
                    {
                        intl.formatMessage({ id: 'billed_period' })
                    }
                </div>
                <div style={{ display: 'flex', flexDirection: 'row', columnGap: 10 }}>
                    {
                        bill_periods.map((bp, index) => {
                            let sku = available_products?.skus.find(sku => sku.productId.indexOf(service) > -1 && sku.recurring?.toLowerCase() == bp.period.toLowerCase())
                            if (!sku) {
                                return <></>
                            }

                            return <div
                                className='hoverStand'
                                style={{
                                    display: 'flex',
                                    flexDirection: 'column',
                                    width: 140,
                                    justifyContent: 'center',
                                    border: bp.period == bill_period ? '1px solid dodgerblue' : '1px solid #ddd'
                                }}
                                onClick={() => {
                                    set_bill_period(bp.period)
                                }}
                            >
                                <div
                                    className='hoverStand'
                                    style={{
                                        display: 'flex',
                                        flexDirection: 'row',
                                        columnGap: '6px',
                                    }}

                                    key={index}
                                >

                                    <span style={{ color: bp.period == bill_period ? 'black' : 'gray', fontSize: 18, fontWeight: bp.period == bill_period ? 'bold' : 'normal' }}>{bp.period}</span>
                                    {
                                        bp.desc &&
                                        <span style={{ color: 'green', fontSize: 14 }}>{bp.desc}</span>
                                    }
                                </div>
                                <div style={{ color: bp.period == bill_period ? 'black' : 'gray', fontSize: 18 }}>
                                    {'$' + sku.price}
                                </div>
                            </div>
                        })
                    }
                </div>
                {
                    trial_days &&
                    <div>
                        {intl.formatMessage({ id: 'trial_desc' }, { trial_days })}
                    </div>
                }
            </DialogContent>
            <DialogActions>
                <Button
                    variant="text"
                    onClick={handleClose}
                >
                    {
                        intl.formatMessage({ id: 'cancel' })
                    }
                </Button>
                <LoadingButton
                    variant="contained"
                    color={"primary"}
                    style={{ marginTop: 10, marginBottom: 10, textTransform: 'capitalize' }}
                    disabled={!!loading}
                    loading={loading?.service === service}
                    onClick={() => handleSubscription(service, title)}
                >
                    {intl.formatMessage({ id: trial_days ? 'trial_vip' : 'confirm' }, {trial_days})}
                </LoadingButton>
            </DialogActions>
        </Dialog>
    );
};

