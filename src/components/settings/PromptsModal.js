import * as React from 'react';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import { useIntl } from 'react-intl';
import { TabPanel, a11yProps } from '../tab/TabPanel';
import PromptsMine from './PromptsMine';
import PromptsPublic from './PromptsPublic';
import { Button, Dialog, DialogActions, DialogContent, DialogTitle } from '@mui/material';
import { useDispatch, useSelector } from 'react-redux';
import { PROMPTS_DIALOG } from 'src/constants/actionTypes';
import PromptsDrafter from './PromptsDrafter';
import { PromptModal } from './PromptModal';
import PromptsEduTools from './PromptsEduTools';

const PromptsModal = ({ }) => {
    const intl = useIntl();
    const dispatch = useDispatch();
    const dialogState = useSelector(state => state.uiState.promptsDialog) || {};

    const user = useSelector(state => state.loginIn.user);

    const [tab, setTab] = React.useState(0);
    const handleChange = (event, newValue) => {
        setTab(newValue);
    };

    const handleClose = () => {
        dispatch({
            type: PROMPTS_DIALOG,
            value: {
                visible: false
            }
        })
    }

    return (
        <Dialog
            open={!!dialogState && !!dialogState.visible && !dialogState.prompt}
            onClose={handleClose}
            scroll='paper'
            aria-labelledby="scroll-dialog-title"
            aria-describedby="scroll-dialog-description"
            maxWidth='lg'
        >
            <DialogContent dividers={true} style={{ display: 'flex', flexDirection: 'column', height: 760, width: 920, padding: '0px', paddingLeft: '20px', paddingRight: '20px' }}>
                <Box sx={{ width: '100%' }}>
                    <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
                        <Tabs value={tab} onChange={handleChange} aria-label="basic tabs example">
                            {user.occupation === 'teacher' && <Tab label={intl.formatMessage({ id: 'prompts_edutools' })} {...a11yProps(0)} />}
                            <Tab label={intl.formatMessage({ id: 'prompts_drafter' })} {...a11yProps(1)} />
                            <Tab label={intl.formatMessage({ id: 'prompts_mine' })} {...a11yProps(2)} />
                            <Tab label={intl.formatMessage({ id: 'prompts_public' })} {...a11yProps(3)} />
                            {/* <Tab label={intl.formatMessage({ id: 'guest' })} {...a11yProps(2)} /> */}
                        </Tabs>
                    </Box>
                    {user.occupation === 'teacher' &&
                        <TabPanel value={tab} index={0}>
                            <div style={{ paddingTop: '10px' }}>
                                <PromptsEduTools container="drafter_panel" />
                            </div>
                        </TabPanel>
                    }
                    <TabPanel value={tab} index={1}>
                        <div style={{ paddingTop: '10px' }}>
                            <PromptsDrafter container="drafter_panel" />
                        </div>
                    </TabPanel>
                    <TabPanel value={tab} index={2}>
                        <div style={{ paddingTop: '10px' }}>
                            <PromptsMine container="drafter_panel" />
                        </div>
                    </TabPanel>
                    <TabPanel value={tab} index={3}>
                        <div style={{ paddingTop: '10px' }}>
                            <PromptsPublic container="drafter_panel" />
                        </div>
                    </TabPanel>
                </Box>
            </DialogContent>
            <div style={{ position: 'absolute', top: '6px', right: '24px' }}>
                <Button onClick={handleClose}>{intl.formatMessage({ id: 'close' })}</Button>
            </div>
            <PromptModal />
        </Dialog>
    );
}

export default PromptsModal;