import { <PERSON><PERSON>, <PERSON>, <PERSON>A<PERSON>, Card<PERSON>ontent, Divider, MenuItem, Select, TextField, Typography } from "@mui/material";
import { useDispatch, useSelector } from "react-redux";
import { getAICoinsProducts, initPayment, checkPaymentStatus } from "src/actions/ticketAction";
import { useIntl } from "react-intl";
import { useEffect, useState } from "react";
import { AI_COINS_PRODUCTS, INVITE_FRIENDS_DIALOG, SETTINGS_DIALOG, STRIPE_CHECKOUT_DIALOG } from "src/constants/actionTypes";
import LoadingButton from "@mui/lab/LoadingButton/LoadingButton";
import { isCNDomain } from "src/utils/constants";

const Market = () => {
    const dispatch = useDispatch();
    const intl = useIntl();
    const ai_coins_products = useSelector(state => state.uiState.ai_coins_products);
    const checkoutState = useSelector(state => state.uiState.stripeCheckoutDialog)
    const [loading, setLoading] = useState(false);
    const [paying, setPaying] = useState();
    const [spUpdated, setSpUpdated] = useState();
    const service = isCNDomain() ? 'ai' : 'aiplus';

    useEffect(() => {
        dispatch(getAICoinsProducts({ service }));
    }, []);

    useEffect(() => {
        if (!checkoutState || checkoutState.visible) return;

        if (checkoutState.paid) {
            setPaying(null);
            setSpUpdated(true);
            setTimeout(() => setSpUpdated(null), 30 * 1000);

            dispatch({
                type: STRIPE_CHECKOUT_DIALOG,
                value: {
                    visible: false
                }
            })
        }
    }, [checkoutState]);

    const initPay = (sku, channel) => {
        setLoading({ service, productId: sku.productId, action: 'initPay' });
        dispatch(initPayment({ productId: sku.productId, channel, service, fromWeb: true }, (paymentIntent) => {
            if (channel === 'alipay') {
                let popup = window.open(paymentIntent.url)

                if (popup === null || typeof popup === 'undefined') {
                    alert('浏览器阻止了弹出窗口，请允许弹出窗口来打开新网页完成支付。');
                }

                setLoading(null);

                let paying = {
                    service,
                    orderId: paymentIntent.orderId,
                    channel
                }
                setPaying(paying);
                setTimeout(() => checkPayStatus(paying), 30 * 1000);
            } else if (channel === 'stripe') {
                setLoading(null);
                dispatch({
                    type: STRIPE_CHECKOUT_DIALOG,
                    value: {
                        visible: true,
                        paymentIntent,
                        skus: [sku],
                        service,
                        title: 'Coins market'
                    }
                });
            }
        }))
    }

    const checkPayStatus = (paying, userInited) => {
        userInited && setLoading({ action: 'checkPayStatus' })
        dispatch(checkPaymentStatus({ payInfo: JSON.stringify(paying), userInited }, (item) => {
            userInited && setLoading(null);

            if (!item) {
                !userInited && setTimeout(() => checkPayStatus(paying), 10 * 1000);
                return;
            }

            if (item) {
                dispatch({
                    type: AI_COINS_PRODUCTS,
                    item
                })
            }

            //show message to user
            setPaying(null);
            setSpUpdated(true);
            setTimeout(() => setSpUpdated(null), 30 * 1000);
        }))
    }

    return (
        <div style={{ paddingTop: '20px', columnGap: '10px', display: 'flex', flexDirection: 'row', alignItems: 'center' }} >
            {
                !ai_coins_products &&
                <Button onClick={() => {
                    dispatch(getAICoinsProducts({ service }));
                }}>{intl.formatMessage({ id: 'refresh' })}</Button>
            }
            {
                ai_coins_products && <div style={{ paddingTop: '20px' }}>
                    <div>{ai_coins_products.desc}</div>
                    <div style={{ paddingTop: '10px' }}>{ai_coins_products.balance}</div>
                    {
                        paying &&
                        <div style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', columnGap: '6px' }}>
                            <div style={{ fontSize: 14, fontWeight: 'bold' }}>{intl.formatMessage({ id: 'paid_to_check' })}</div>

                            <LoadingButton loading={loading?.action === 'checkPayStatus'} disabled={!!loading} variant="outlined" onClick={() => checkPayStatus(paying, true)} >{intl.formatMessage({ id: 'refresh' })}</LoadingButton>
                        </div>
                    }
                    {
                        spUpdated &&
                        <div style={{ color: 'green', fontWeight: 'bold', padding: '6px' }}>
                            {intl.formatMessage({ id: 'aicoins_status_updated' })}
                        </div>
                    }
                    <div style={{ paddingTop: '20px', fontSize: 14, color: '#666' }}>{ai_coins_products.buy_title}</div>
                    <div style={{ display: 'grid', gridTemplateColumns: 'auto auto auto' }}>
                        {
                            ai_coins_products.products?.map(product => {
                                return <div
                                    style={{
                                        height: '80px',
                                        margin: '10px',
                                        display: 'flex',
                                        flexDirection: 'row',
                                        alignItems: 'center',
                                        justifyContent: 'space-between',
                                        backgroundColor: '#eee',
                                        paddingLeft: '10px',
                                        paddingRight: '10px',
                                        borderRadius: '6px'
                                    }}>
                                    <div style={{display: 'flex', flexDirection: 'column', rowGap: '12px'}}>
                                        <div>
                                            {intl.formatMessage({ id: 'num' }) + ': ' + product.coins}
                                        </div>
                                        <div>
                                            {intl.formatMessage({ id: 'price' }) + ': ' + (isCNDomain() ? product.price + ' 元' : '$' + product.price)}
                                        </div>
                                    </div>
                                    <LoadingButton loading={loading?.action === 'initPay' && loading.productId == product.productId} disabled={!!loading} variant="contained" onClick={() => initPay(product, isCNDomain() ? 'alipay' : 'stripe')}>{intl.formatMessage({ id: "buy" })}</LoadingButton>
                                </div>
                            })
                        }
                    </div>
                    <div style={{ paddingTop: '60px', fontSize: 14, color: '#666' }}>{ai_coins_products.earn_desc}</div>
                    <div style={{ paddingTop: '10px', width: '100%', display: 'flex', alignItems: 'center', flexDirection: 'row', justifyContent: 'center' }}>
                        <Button variant="outlined" onClick={() => {
                            dispatch({
                                type: SETTINGS_DIALOG,
                                value: {
                                    visible: false
                                }
                            })
                            dispatch({
                                type: INVITE_FRIENDS_DIALOG,
                                value: {
                                    visible: true
                                }
                            })

                        }}>{intl.formatMessage({ id: "invite_friends" })}</Button>
                    </div>
                </div>
            }
        </div>
    );
}

export default Market;