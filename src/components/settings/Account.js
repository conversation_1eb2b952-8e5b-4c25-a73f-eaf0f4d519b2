import { Divider, Text<PERSON><PERSON> } from "@mui/material";
import { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { updateUserName } from "src/actions/ticketAction";
import BottomActionBar from "./BottomActionBar";
import { useIntl } from "react-intl";

const Account = () => {
    const dispatch = useDispatch();
    const loginUser = useSelector(state => state.loginIn.user);
    const intl = useIntl();

    const [name, setName] = useState(loginUser.nickname);
    const [nameValid, setNameValid] = useState(true);

    const handleConfirm = (callback) => {
        const isNameValid = name && name.length > 2;
        setNameValid(isNameValid);

        if (isNameValid) {
            dispatch(updateUserName({ name }, callback, "org"));
        }
    }

    return (
        <div>
            <TextField
                required
                id="name"
                label={intl.formatMessage({ id: "nickname" })}
                style={{ width: '360px', marginTop: '20px' }}
                value={name}
                onChange={(e) => setName(e.target.value)}
                error={!nameValid}
            />
            <BottomActionBar handleConfirm={handleConfirm} />
        </div>
    );
}

export default Account;