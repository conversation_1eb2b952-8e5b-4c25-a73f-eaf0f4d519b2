import { CircularP<PERSON><PERSON>, Divider, FormControl, FormControlLabel, Radio, RadioGroup } from "@mui/material";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { getAIModels, getServingPrivilege, updateOrg } from "src/actions/ticketAction";
import { getStateByUser } from "src/reducers/listReducer";
import { Button, Switch } from '@mui/material';
import { useIntl } from "react-intl";
import { AI_SETTINGS, SETTINGS_DIALOG } from "src/constants/actionTypes";
import ReactMarkdown from "react-markdown";
import { Selector } from "../common/Selector";

export const llm_providers = {
    openai: { label: 'OpenAI (ChatGPT)', name: 'Openai', endpoint: 'https://api.openai.com/v1', url: 'https://platform.openai.com/account/api-keys' },
    anthropic: { label: '<PERSON><PERSON><PERSON> (<PERSON>)', name: 'Anthropic', endpoint: 'https://api.anthropic.com/v1', url: 'https://console.anthropic.com/account/keys' },
    gemini: { label: 'Google (Gemini)', name: 'Gemini', endpoint: 'https://generativelanguage.googleapis.com/v1beta', url: 'https://makersuite.google.com/app/apikey' },
    groq: { label: 'Groq', name: 'Groq', endpoint: 'https://api.groq.com/openai/v1', url: 'https://console.groq.com/keys' }
}

const APISettings = () => {
    const intl = useIntl();
    const dispatch = useDispatch();

    const aiSettings = useSelector(state => state.uiState.aiSettings) || {};
    const appConfigs = useSelector(state => state.uiState.app_config) || {};
    const settingsDialogState = useSelector(state => state.uiState.settingsDialog) || {};
    const aiProvider = aiSettings.aiProvider || 'funblocks';
    const usingAPI = aiSettings.usingAPI;

    const [privileged, setPrivileged] = useState(false);
    const operationStatus = useSelector(state => state.operationStatus);
    const [loading, setLoading] = useState();
    const [model_options, set_model_options] = useState();

    useEffect(() => {
        let options = aiSettings[aiProvider]?.models?.map(model => ({ label: model, value: model })) || [];
        options.push({ label: intl.formatMessage({ id: 'other' }), value: 'other' })
        set_model_options(options)
    }, [aiSettings[aiProvider]?.models, intl])

    useEffect(() => {
        if (!operationStatus?.inProgress) {
            setLoading(null);
        }
    }, [operationStatus])

    useEffect(() => {
        dispatch(getServingPrivilege({ privilege: 'usePrivateAIApi' }, (privileged) => {
            setPrivileged(privileged);
            if (!privileged) {
                dispatch({
                    type: AI_SETTINGS,
                    value: {
                        ...aiSettings,
                        usingAPI: false,
                        aiProvider: 'funblocks'
                    }
                })
            }
        }))
    }, [])

    const onTokenBlur = () => {
        if (!aiSettings[aiProvider]?.token) {
            return;
        }

        if (['openai', 'gemini'].includes(aiProvider)) {
            setLoading({ action: 'getAIModels' });
            dispatch(getAIModels(aiProvider, aiSettings[aiProvider].token, (list) => {
                if (!appConfigs.ai_providers || !list) {
                    return;
                }

                if (aiProvider === 'openai') {
                    list = list.filter(item => item.owned_by == 'openai');
                }

                if (appConfigs.ai_providers[aiProvider]?.models) {
                    list = list.filter(item =>
                        appConfigs.ai_providers[aiProvider]?.models?.some(model => item[aiProvider == 'openai' ? 'id' : 'name']?.startsWith(model))
                        && !appConfigs.ai_providers[aiProvider]?.excluded_models?.some(model => item[aiProvider == 'openai' ? 'id' : 'name']?.startsWith(model))
                    )
                }
                list = list.map(item => aiProvider === 'openai' ? item.id : item.name)

                if (!list || list.length == 0) {
                    return;
                }

                let settings = { ...aiSettings };
                settings[aiProvider] = {
                    ...settings[aiProvider],
                    models: list,
                    model: list[0]
                }
                dispatch({
                    type: AI_SETTINGS,
                    value: settings
                })
            }))
        }
    }

    if (!aiProvider) {
        return null;
    }

    // console.log('debug.............', aiProvider, !aiSettings[aiProvider]?.token, !usingAPI)

    return (
        <div style={{ width: '100%', alignItems: 'center', justifyContent: 'flex-start', paddingTop: 20 }}>
            <div style={styles.item}>
                <span style={styles.title}>
                    {intl.formatMessage({ id: "settings_api_enable_api" })}
                </span>
                <Switch
                    checked={usingAPI}
                    onChange={(event) => {
                        dispatch({
                            type: AI_SETTINGS,
                            value: {
                                ...aiSettings,
                                usingAPI: event.target.checked,
                                aiProvider: aiSettings.aiProvider || (event.target.checked ? 'openai' : 'funblocks')
                            }
                        })
                    }}
                    disabled={!privileged}
                    name="enableOpenAI"
                    size='small'
                />
                {
                    !privileged && <div style={{
                        fontSize: 12,
                        color: 'red'
                    }}>
                        {intl.formatMessage({ id: 'privileged_feature' })}
                    </div>
                }

                {
                    !privileged && <Button
                        onClick={() => {
                            dispatch({
                                type: SETTINGS_DIALOG,
                                value: {
                                    ...settingsDialogState,
                                    page: 'service_subscribe'
                                }
                            })
                        }}
                    >
                        {intl.formatMessage({ id: 'upgrade_plan' })}
                    </Button>
                }
            </div>

            <div style={styles.item}>
                <span style={styles.title}>
                    {intl.formatMessage({ id: "settings_api_llms" })}
                </span>
                <Selector
                    options={Object.values(llm_providers).map(privider => ({
                        label: privider.label,
                        value: privider.name.toLowerCase()
                    }))}
                    value={aiProvider || 'openai'}
                    inputStyle={{
                        border: '1px solid #ccc',
                        backgroundColor: usingAPI ? undefined : '#f9f9f9',
                        borderRadius: '3px',
                    }}
                    disabled={!usingAPI}
                    onChange={(value) => {
                        let newAiProvider = value;
                        let newAiSettings = {
                            ...aiSettings,
                            aiProvider: newAiProvider
                        }

                        if (!newAiSettings[newAiProvider]) {
                            newAiSettings[newAiProvider] = {
                                endpoint: llm_providers[newAiProvider].endpoint,
                            }
                        }
                        if (!newAiSettings[newAiProvider].endpoint) {
                            newAiSettings[newAiProvider].endpoint = llm_providers[newAiProvider].endpoint;
                        }

                        let llm_models = appConfigs.llm_models || { anthropic: [], groq: [] }

                        if (['anthropic', 'groq'].includes(newAiProvider)) {
                            newAiSettings[newAiProvider] = {
                                ...aiSettings[newAiProvider],
                                models: llm_models[newAiProvider],
                                model: (newAiSettings[newAiProvider]?.model || llm_models[newAiProvider][0])
                            }
                        }

                        dispatch({
                            type: AI_SETTINGS,
                            value: newAiSettings
                        })

                    }}
                />
            </div>

            <div style={styles.item}>
                <span style={styles.title}>
                    API Key
                </span>
                <input
                    type='text'
                    value={aiSettings[aiProvider]?.token || ''}
                    label={'key'}
                    style={styles.input}
                    required={true}
                    disabled={!usingAPI}
                    onChange={(e) => {
                        let settings = { ...aiSettings };
                        settings[aiProvider] = {
                            ...settings[aiProvider],
                            token: e.target.value
                        }
                        dispatch({
                            type: AI_SETTINGS,
                            value: settings
                        })
                    }}
                    onBlur={onTokenBlur}
                // onFocus={() => this.style.borderColor = 'transparent'}
                />

                {
                    aiProvider && llm_providers[aiProvider] &&
                    <div style={{ flexDirection: 'row', alignItems: 'center', display: 'flex' }}>
                        <div style={{ color: 'gray', fontSize: 13 }}>
                            {intl.formatMessage({ id: 'llm_api_token_guide' }, { aiProvider: llm_providers[aiProvider].name })}
                        </div>
                        <Button variant='clear' sx={{ textTransform: 'none', color: 'dodgerblue' }} onClick={() => {
                            window.open(llm_providers[aiProvider].url);
                        }}>
                            {llm_providers[aiProvider].name}
                        </Button>
                    </div>
                }

            </div>

            <div style={styles.item}>
                <label htmlFor="model-select" style={styles.title}>{intl.formatMessage({ id: 'settings_api_choose_model' })}</label>

                <Selector
                    inputStyle={{
                        border: '1px solid #ccc',
                        // backgroundColor: usingAPI && aiSettings[aiProvider]?.token ? undefined : '#f9f9f9',
                        borderRadius: '3px',
                    }}
                    // disabled={!aiSettings[aiProvider]?.token || !usingAPI}
                    onChange={(value) => {
                        let settings = { ...aiSettings };
                        settings[aiProvider] = {
                            ...settings[aiProvider],
                            model: value
                        }
                        dispatch({
                            type: AI_SETTINGS,
                            value: settings
                        })
                    }}
                    value={aiSettings[aiProvider]?.models?.includes(aiSettings[aiProvider]?.model) ? aiSettings[aiProvider]?.model : 'other'}
                    options={model_options}
                />
                {
                    !aiSettings[aiProvider]?.models?.includes(aiSettings[aiProvider]?.model) &&
                    <input
                        type='text'
                        value={aiSettings[aiProvider]?.model == 'other' ? '' : aiSettings[aiProvider]?.model}
                        label={'model'}
                        style={styles.input}
                        required={true}
                        onChange={(e) => {
                            let settings = { ...aiSettings };
                            settings[aiProvider] = {
                                ...settings[aiProvider],
                                model: e.target.value
                            }

                            dispatch({
                                type: AI_SETTINGS,
                                value: settings
                            })
                        }}
                    />
                }
                {
                    loading?.action == 'getAIModels' &&
                    <CircularProgress size={20} />
                }
            </div>

            <div style={styles.item}>
                <span style={styles.title}>
                    {intl.formatMessage({ id: 'settings_api_proxy' })}
                </span>
                <input
                    type='text'
                    value={aiSettings[aiProvider]?.endpoint}
                    label={'key'}
                    style={styles.input}
                    required={true}
                    disabled={!usingAPI}
                    onChange={(e) => {
                        let settings = { ...aiSettings };
                        settings[aiProvider] = {
                            ...settings[aiProvider],
                            endpoint: e.target.value
                        }

                        dispatch({
                            type: AI_SETTINGS,
                            value: settings
                        })
                    }}
                />
            </div>

            <div style={{ color: 'gray', fontSize: '14px', marginTop: 40 }}>
                <ReactMarkdown>{
                    intl.formatMessage({ id: 'settings_api_desc' })
                }</ReactMarkdown>
                {
                    aiSettings?.aiProvider == 'openai' &&
                    <ReactMarkdown>{
                        intl.formatMessage({ id: 'openai_compatible' })
                    }</ReactMarkdown>
                }
            </div>
        </div>
    );
}

const styles = {
    item: {
        flexDirection: 'row',
        alignItems: 'center',
        display: 'flex',
        paddingTop: '10px',
        columnGap: '10px'
    },
    title: {
        fontWeight: 500,
        fontSize: 14,
        color: '#333'
    },
    input: {
        padding: "2px 6px",
        border: '1px solid #ccc',
        borderRadius: '3px',
        outline: 'none',
        minWidth: '360px',
        fontSize: 14
    }
}

export default APISettings;