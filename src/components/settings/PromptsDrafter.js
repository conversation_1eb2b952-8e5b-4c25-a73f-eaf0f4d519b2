import { <PERSON><PERSON>, Di<PERSON>r, <PERSON>uItem, Select, Switch, TextField } from "@mui/material";
import { useIntl } from "react-intl";
import { useDispatch, useSelector } from "react-redux";
import { PromptModal } from "./PromptModal";
import { useEffect, useRef, useState } from "react";
import PromptCard from "./PromptCard";
import { PROMPTS_DIALOG } from "src/constants/actionTypes";

const PromptsDrafter = ({ container }) => {
    const dispatch = useDispatch();
    const intl = useIntl();

    const promptsDialogState = useSelector(state => state.uiState.promptsDialog) || {};
    const app_config = useSelector(state => state.uiState.app_config);
    const { assistant_items } = app_config || {};
    const [drafter, setDrafter] = useState();

    const itemRef = useRef(null);
    const [selectedIndex, setSelectedIndex] = useState(0);

    useEffect(() => {
        const handleKeyDown = (event) => {
            const itemsCount = drafter?.sub_items?.length;
            if (!itemsCount) {
                return;
            }

            const itemsPerRow = window.innerWidth > 768 ? 3 : 1;

            switch (event.key) {
                case 'ArrowUp':
                    setSelectedIndex((prevIndex) => {
                        return Math.max(0, prevIndex - itemsPerRow);
                    });
                    break;
                case 'ArrowDown':
                    setSelectedIndex((prevIndex) => {
                        return Math.min(itemsCount - 1, prevIndex + itemsPerRow);
                    });
                    break;
                case 'ArrowLeft':
                    setSelectedIndex((prevIndex) => {
                        return Math.max(0, prevIndex - 1);
                    });
                    break;
                case 'ArrowRight':
                    setSelectedIndex((prevIndex) => {
                        return Math.min(itemsCount - 1, prevIndex + 1);
                    });
                    break;
                case 'Enter':
                    runPrompt(drafter.sub_items[selectedIndex])
                    break;
            }

            event.preventDefault();
            event.stopPropagation();
        };

        window.addEventListener('keydown', handleKeyDown);
        return () => {
            window.removeEventListener('keydown', handleKeyDown);
        };
    }, [drafter?.sub_items?.length, selectedIndex])

    useEffect(() => {
        setDrafter(assistant_items.find(item => item.action == 'draft'));
    }, [assistant_items]);

    const runPrompt = (item) => {
        dispatch({
            type: PROMPTS_DIALOG,
            value: {
                ...promptsDialogState,
                visible: false,
                prompt: {
                    action: 'draft',
                    sub_item: item.value
                }
            }
        })
    }

    return (
        <div className="prompts-drafter">
            <div className="prompts-container">
                {drafter?.sub_items?.map((item, index) => (
                    <div
                        key={index}
                        ref={index === 0 ? itemRef : null}
                        className={`prompt-card ${selectedIndex === index ? 'selected' : ''}`}
                        onClick={() => runPrompt(item)}
                        style={{ fontSize: 20, fontWeight: 600, color: '#555' }}
                    >
                        {item.label}
                        {/* <p>{item.description || 'No description available.'}</p> */}
                    </div>
                ))}
            </div>
            <style jsx>{`
                .prompts-drafter {
                    padding: 20px 10px;
                }
                .prompts-container {
                    display: flex;
                    flex-wrap: wrap;
                    gap: 20px;
                }
                .prompt-card {
                    flex: 0 1 calc(33.333% - 20px);
                    box-sizing: border-box;
                    padding: 20px;
                    border-radius: 10px;
                    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                    background-color: white;
                    transition: all 0.3s ease;
                    cursor: pointer;
                }
                .prompt-card:hover {
                    transform: translateY(-5px);
                    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
                }
                .prompt-card.selected {
                    background-color: #e6f7ff;
                    border: 2px solid #1890ff;
                }
                .prompt-card h3 {
                    margin-top: 0;
                    margin-bottom: 10px;
                    font-size: 18px;
                    color: #333;
                }
                .prompt-card p {
                    margin: 0;
                    font-size: 14px;
                    color: #666;
                }
                @media (max-width: 768px) {
                    .prompt-card {
                        flex: 0 1 100%;
                    }
                }
            `}</style>
        </div>
    );
}

export default PromptsDrafter;