import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>uItem, Select, TextField } from "@mui/material";
import { useDispatch, useSelector } from "react-redux";
import { getServingProducts, getAvailableProducts, initSubscription, cancelSubscription } from "src/actions/ticketAction";
import { useIntl } from "react-intl";
import { useEffect, useState } from "react";
import { BILL_PERIOD_DIALOG, SERVING_PRODUCT, STRIPE_CHECKOUT_DIALOG } from "src/constants/actionTypes";
import { formatDate } from "src/utils/timeFormater";
import LoadingButton from "@mui/lab/LoadingButton/LoadingButton";
import { BillPeriodModal } from "./BillPeriodModal";

const ServiceSubscribeEN = () => {
    const dispatch = useDispatch();
    const intl = useIntl();
    const available_products = useSelector(state => state.uiState.available_products);
    const serving_products = useSelector(state => state.uiState.serving_products) || [];
    const loginUser = useSelector(state => state.loginIn && state.loginIn.user);
    const [products, setProducts] = useState([]);
    const [loading, setLoading] = useState();

    const services = ['funblocks', 'aiplus'];

    useEffect(() => {
        dispatch(getAvailableProducts({ services }, null, 'servicesubscribe'));
    }, []);

    useEffect(() => {
        loginUser?._id && dispatch(getServingProducts({ services }, null, 'servicesubscribe'));
    }, [loginUser]);

    useEffect(() => {
        if (!available_products?.products) {
            return;
        }

        const products = [{
            id: 'free',
            name: intl.formatMessage({ id: 'service_product_name' }, { service: intl.formatMessage({ id: 'service_name_funblocks' }), level: intl.formatMessage({ id: 'service_level_free' }) }),
            privileges: {}
        }];

        Object.keys(available_products.products).forEach(service => {
            if (service === 'aiplus') {
                return;
            }

            Object.keys(available_products.products[service]).forEach(level => {
                if (level == 'free') {
                    Object.keys(available_products.products[service][level].privileges).forEach(privilege => {
                        products[0].privileges[privilege] = available_products.products[service][level].privileges[privilege]
                    })
                    return;
                }

                let product = available_products.products[service][level];
                product.id = `${service}_${level}`;
                product.service = service;
                product.name = intl.formatMessage({ id: 'service_product_name' }, { service: intl.formatMessage({ id: 'service_name_' + service }), level: intl.formatMessage({ id: 'service_level_' + level }) })
                product.skus = available_products.skus.filter(sku => sku.productId.startsWith(service + '_' + level))
                products.push(product);
            })
        })

        setProducts(products);
    }, [available_products]);

    const handleSubscription = (service, sku, shouldTrial) => {
        if (!loginUser?._id) {
            const hash = window.location.hash;
            let queryString = '';
            const queryIndex = hash.indexOf('?');
            if (queryIndex !== -1) {
                queryString = hash.substring(queryIndex);
            }

            window.open(window.location.origin + '/#/login' + queryString, '_blank')
            return
        }

        dispatch({
            type: BILL_PERIOD_DIALOG,
            value: {
                visible: true,
                sku,
                service,
                trial_days: shouldTrial && sku.trial_days
            }
        })
    }
    const handleCancelSubscription = (service, subscriptionId) => {
        setLoading({ service })
        dispatch(cancelSubscription({ subscriptionId }, (servingProduct) => {
            setLoading(null);

            !!servingProduct && dispatch({
                type: SERVING_PRODUCT,
                item: servingProduct
            })
            // if (data?.refreshServingProducts) {
            //     return dispatch(getServingProducts({ services }, null, 'servicesubscribe'));
            // }
        }, () => setLoading(null)))
    }

    if (!available_products || !available_products.privileges) {
        return <></>
    }

    return (
        <div style={{ paddingTop: '20px', columnGap: '10px', display: 'flex', flexDirection: 'column', alignItems: 'center' }} >
            <div style={{
                display: 'flex',
                flexDirection: 'row',
                justifyContent: 'space-evenly',
                columnGap: '20px',
                paddingTop: '40px',
                paddingBottom: '20px'
            }}>
                {
                    services.map((service, index) => {
                        let serving_product = serving_products.find(sp => sp.service == service);
                        if (!serving_product) {
                            return null
                        }
                        let sku = available_products.skus.find(sku => sku.productId === (service === 'aiplus' ? 'aiplus_premium_1' : 'funblocks_plus_1'))
                        if (!sku) {
                            return null
                        }

                        let shouldTrial = serving_product.name == 'free' && !serving_product.latestSubscription && sku.trial_days;

                        return <div key={index + ''} style={{ minWidth: '280px', maxWidth: '400px' }}>
                            <div style={{ fontSize: 17, fontWeight: 'bold' }}> {intl.formatMessage({ id: 'service_name_' + service })} </div>
                            <div style={{ display: 'flex', flexDirection: 'row', paddingTop: '6px' }}>
                                <div style={{ fontSize: 13, color: '#666', padding: '4px', backgroundColor: '#e9e9e9', borderRadius: '5px', marginRight: '6px' }}>{intl.formatMessage({ id: 'current_plan' })}</div>
                                <div style={{ fontSize: 18, fontWeight: 'bold', textTransform: 'capitalize' }}>{intl.formatMessage({ id: 'service_level_' + serving_product.name })}</div>
                            </div>
                            {
                                serving_product.name !== 'free' &&
                                <div style={{ fontSize: 13, color: '#666', paddingTop: '4px', paddingBottom: '6px' }}> {intl.formatMessage({ id: 'plan_expire_at' }, { date: formatDate(serving_product.endAt, 'YYYY-MM-DD') })}</div>
                            }
                            {
                                serving_product.name == 'free' &&
                                <div style={{ fontSize: 13, color: '#666', paddingTop: '4px', paddingBottom: '6px' }}> {serving_product.promote_text}</div>
                            }
                            {
                                serving_product.name == 'free' && serving_product.latestSubscription &&
                                <div style={{ fontSize: 13, color: '#666', paddingTop: '4px', paddingBottom: '6px' }}> {intl.formatMessage({ id: 'last_plan_expired' }, { plan: serving_product.latestSubscription.productId, date: formatDate(serving_product.latestSubscription.endAt, 'YYYY-MM-DD') })}</div>
                            }
                            {
                                serving_product.name != 'free' &&
                                <LoadingButton
                                    variant="outlined"
                                    color={"primary"}
                                    style={{ marginTop: 10, marginBottom: 10, textTransform: 'capitalize' }}
                                    disabled={!!loading}
                                    loading={loading?.service === service}
                                    onClick={() => handleCancelSubscription(service, serving_product.subscriptionId)}
                                >
                                    {intl.formatMessage({ id: 'cancel_subscription' })}
                                </LoadingButton>
                            }
                            {
                                serving_product.name == 'free' &&
                                <LoadingButton
                                    variant="contained"
                                    color={"primary"}
                                    style={{ marginTop: 10, marginBottom: 10, textTransform: 'capitalize' }}
                                    disabled={!!loading}
                                    loading={loading?.service === service}
                                    onClick={() => handleSubscription(service, sku, shouldTrial)}
                                >
                                    {intl.formatMessage({ id: shouldTrial ? 'trial_vip' : (service === 'aiplus' ? 'purchase_ai' : 'upgrade_to_vip') }, { trial_days: sku.trial_days })}
                                </LoadingButton>
                            }
                        </div>
                    })
                }
            </div>

            <Divider />
            <div style={{ alignSelf: 'flex-start', paddingLeft: '6px', paddingBottom: '6px' }}>{intl.formatMessage({ id: 'buyable_plans' })}</div>
            <table style={{ borderCollapse: 'collapse' }}>
                <tbody>
                    <tr style={{ verticalAlign: 'bottom' }}>
                        <td style={{ textAlign: 'right', padding: '4px' }}>{intl.formatMessage({ id: 'privileges' })}</td>
                        {
                            products.map((product, i) => {
                                const skus = product.skus;
                                if (!skus) return <td style={{ minWidth: '80px', textAlign: 'right', padding: '4px' }}>
                                    {intl.formatMessage({ id: 'service_level_free' })}
                                </td>;

                                const servingProduct = serving_products.find(sp => sp.service == product.service);
                                let shouldTrial = servingProduct?.name == 'free' && !servingProduct?.latestSubscription && skus[0].trial_days;

                                return <td key={i + ''} style={{ minWidth: '200px', textAlign: 'right', padding: '4px' }}>
                                    <div style={{ fontWeight: 'bold', color: 'dodgerblue', fontSize: 15 }}>{product.name}</div>
                                    {
                                        servingProduct?.name === 'free' &&
                                        <div style={{ rowGap: '5px', display: 'flex', flexDirection: 'column', paddingTop: '4px' }}>
                                            <LoadingButton
                                                variant="contained"
                                                color={"primary"}
                                                style={{ marginTop: 10, marginBottom: 10, textTransform: 'capitalize' }}
                                                disabled={!!loading}
                                                loading={loading?.service === product.service}
                                                onClick={() => handleSubscription(product.service, skus[0])}
                                            >
                                                {intl.formatMessage({ id: shouldTrial ? 'trial_vip' : 'upgrade_plan' }, { trial_days: skus[0].trial_days })}
                                            </LoadingButton>
                                        </div>
                                    }
                                </td>
                            })
                        }
                    </tr>
                    {
                        Object.keys(available_products?.privileges)?.map((privilege, index) => {
                            return <tr key={index + ''} style={{ backgroundColor: '#f9f9f9' }}>
                                <td style={{ textAlign: 'right', padding: '4px' }}>
                                    {
                                        available_products.privileges[privilege]
                                    }
                                </td>
                                {
                                    products.map((product, i) => {
                                        return <td key={privilege + i + ''} style={{ textAlign: 'right', padding: '4px' }}>
                                            <div>
                                                {
                                                    product.privileges[privilege]
                                                }
                                            </div>
                                        </td>
                                    })
                                }
                            </tr>
                        })
                    }

                </tbody>
            </table>
            <BillPeriodModal />
        </div>
    );
}

export default ServiceSubscribeEN;