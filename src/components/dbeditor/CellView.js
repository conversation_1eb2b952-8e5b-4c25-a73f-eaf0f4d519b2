import { CheckBox } from "@styled-icons/material/CheckBox";
import { CheckBoxOutlineBlank } from "@styled-icons/material/CheckBoxOutlineBlank";
import { useSelector } from "react-redux";
import { getStateByUser } from "src/reducers/listReducer";
import { MemberChip } from "./MemberChip";
import { OptionChip } from "./OptionChip";
import { FileText } from "@styled-icons/bootstrap/FileText";
import { File } from "@styled-icons/bootstrap/File";
import { formatDate } from "src/utils/timeFormater";

const PersonCell = ({ value, style, members }) => {
    const loginUser = useSelector(state => state.loginIn && state.loginIn.user);
    const orgs = useSelector(state => getStateByUser(state.org_lists, loginUser));
    const workingSpace = orgs?.items?.find(org => org._id === loginUser?.workingOrgId) || { users: [] };
    if (!members) {
        members = workingSpace.users.filter(user => !!user.user).map(user => user.user);
    }

    const userIds = value && typeof value === 'object' ? value : []

    return <div style={{
        ...style,
    }}>{
            userIds.map(v => {
                const member = members.find(o => o._id === v);
                return member && <MemberChip key={v} member={member} />
            })
        }</div>
}

export const CellView = ({ style, property, value, aliginLeft, hasDocAttached, alwaysShowDocIcon, members, editMode }) => {
    if (!property) {
        return <div style={{
            ...style,
        }}>
            {value}
        </div>
    }

    if (property.type === 'Select' || property.type === 'MultiSelect') {
        const options = property.options || [];
        if (!value) {
            value = [];
        } else if (typeof value === 'string') {
            value = value.split(',');
        }

        return <div
            style={{
                display: 'flex',
                flexDirection: 'row',
                flexWrap: 'wrap',
                rowGap: '3px',
                columnGap: '3px',
                paddingTop: '3px',
                paddingBottom: '3px',
                ...style,
            }}
        >{
                (value || []).map(v => {
                    const option = options.find(o => o.value === v);
                    return option && <OptionChip key={v} option={option} />
                })
            }</div>
    } else if (property.type === 'Person') {

        return <PersonCell
            value={value}
            members={members}
            style={{
                display: 'flex',
                flexDirection: 'row',
                flexWrap: 'wrap',
                rowGap: '3px',
                columnGap: '3px',
                paddingTop: '3px',
                paddingBottom: '3px',
                ...style,
            }}
        />
    } else if (property.type === 'Number') {
        value = isNaN(value) ? '' : value;
        return <div style={{ textAlign: aliginLeft ? 'left' : 'right', ...style }}>{value}</div>
    } else if (property.type === 'Date') {
        let format = (property.dateFormat || 'YYYY/MM/DD') + (property.hasTime ? ' HH: mm: ss' : '');

        if (!property.hasEndDate) {
            if (Array.isArray(value)) {
                value = value[0];
            }

            if (value) {
                value = new Date(value);
            }

            if (!value || value === "Invalid Date" || isNaN(value)) {
                return null;
            }

            return <div style={{
                ...style,
            }}>{formatDate(value, format)}</div>
        } else {
            let startDate, endDate;

            if (!Array.isArray(value)) {
                value = new Date(value);
                startDate = value;
                endDate = value;
            } else {
                startDate = new Date(value[0]);
                endDate = new Date(value[1]);
            }

            if (!startDate || startDate === "Invalid Date" || isNaN(startDate)) {
                return null;
            }

            return <div style={{
                ...style,
            }}>{`${formatDate(startDate, format)} → ${formatDate(endDate, format)}`}</div>;
        }
    } else if (property.type === 'Checkbox') {
        return <div style={{
            ...style,
        }}>
            {value && <CheckBox size={20} style={{ color: 'dodgerblue' }} />}
            {!value && <CheckBoxOutlineBlank size={20} style={{ color: '#333' }} />}
        </div>
    } else if (property.type === 'Title') {
        return <div style={{
            fontSize: '14px',
            fontWeight: '500',
            color: '#333',
            width: 'inherit',
            ...style,
            whiteSpace: 'nowrap',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
        }}>
            {hasDocAttached && <FileText size={18} style={{ marginRight: 4 }} />}
            {!hasDocAttached && alwaysShowDocIcon && <File size={18} style={{ marginRight: 4 }} />}
            {value}
        </div>
    } else if (property.type === 'Url') {
        return <div
            style={{
                ...style,
                cursor: 'pointer',
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                textDecoration: 'underline',
                textDecorationColor: '#aaa'
            }}
            onClick={editMode? undefined: (e) =>{
                window.open(value);
                e.stopPropagation();
                e.preventDefault();
            }}
        >
            {value}
        </div>
    }

    return <div style={{
        fontSize: '14px',
        color: '#333',
        ...style,
    }}>
        {value}
    </div>
}