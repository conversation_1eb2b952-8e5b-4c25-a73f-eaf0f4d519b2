import { DB_PROPERTY_TYPES } from "src/constants/constants";
import { Menu } from '@mui/material';
import { useIntl } from "react-intl";

export const ViewPropertiesMenu = ({ properties, onChange, anchorEl, onClose }) => {
    const intl = useIntl();

    const handleClose = () => {
        onClose();
    }

    const handleItemClick = (property) => {
        onChange(property);
        onClose();
    }


    return <Menu
            id="basic-menu"
            anchorEl={anchorEl}
            open={Boolean(anchorEl)}
            onClose={handleClose}
            MenuListProps={{
                'aria-labelledby': 'basic-button',
            }}
        >

            {
                properties &&
                <div style={{
                    margin: '6px 14px 6px 14px',
                    minWidth: '180px',
                }}>
                    <div style={{
                        fontSize: 13,
                        color: 'gray'
                    }}
                    >
                        {intl.formatMessage({ id: 'all_properties' })}

                    </div>

                    {
                        properties.map((property, index) => {
                            return <div
                                className='hoverStand db_table_header'
                                onClick={() => handleItemClick(property)}
                                key={index}
                            >
                                {DB_PROPERTY_TYPES.find(t => t.value === property.type).icon}
                                {property.label}
                            </div>
                        })
                    }
                </div>
            }
        </Menu>
}