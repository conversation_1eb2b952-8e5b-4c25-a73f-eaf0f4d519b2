import { Plus } from '@styled-icons/boxicons-regular/Plus';
import { useEffect, useState, useCallback } from 'react';
import { useIntl } from 'react-intl';
import { Menu } from '@mui/material';
import { useDispatch, useSelector } from 'react-redux';

import { getViewProperties } from './DBUtil';
import { Filter } from '@styled-icons/fluentui-system-filled/Filter';

import { VIEW_FILTER_ACTIONS } from 'src/constants/actionTypes';
import { ViewPropertiesMenu } from './ViewPropertiesMenu';
import { getDefaultRuleOp } from 'src/constants/constants';

export const FilterMenu = ({ view }) => {
    const intl = useIntl();
    const dispatch = useDispatch();
    const dialogState = useSelector(state => state.viewFilters.byId[view._id]) || {};
    const [anchorEl, setAnchorEl] = useState(null);
    const [properties, setProperties] = useState([]);
    const doc = useSelector(state => state.docs.byId[view.dataSource]);

    const { filterGroup = {} } = dialogState;

    const handleClose = () => {
        setAnchorEl(null);
    }

    useEffect(() => {
        if (!doc || !doc.meta || !doc.meta.properties) return;

        setProperties(getViewProperties(doc.meta.properties, view).filter(p => !p.hide));
    }, [doc, view]);

    const handleItemClick = (property) => {
        setAnchorEl(null);

        const op = getDefaultRuleOp(property.type);
        dispatch({
            type: VIEW_FILTER_ACTIONS.updated,
            item: {
                viewId: view._id,
                filterGroup: {
                    type: 'group',
                    op: 'and',
                    filters: [{
                        type: 'rule',
                        op,
                        property: property.name,
                        value: null
                    }]
                } 
            }
        });

        setTimeout(() => {
            dispatch({
                type: VIEW_FILTER_ACTIONS.updated,
                item: {
                    viewId: view._id,
                    visible: true,
                    filterGroup: {
                        type: 'group',
                        op: 'and',
                        filters: [{
                            type: 'rule',
                            op,
                            property: property.name,
                            value: null
                        }]
                    }
                } 
            });
        }, 100);
    }

    let style = {
        ...styles.button,
    }

    if (filterGroup.type) {
        style.color = 'dodgerblue';
    }

    return (<>
        <div className='hoverStand'
            style={style}
            onClick={(e) => {
                if (filterGroup.type) {
                    dispatch({
                        type: VIEW_FILTER_ACTIONS.updated,
                        item: {
                            ...dialogState,
                            visible: !dialogState.visible,
                        }
                    })
                } else {
                    setAnchorEl(e.currentTarget);
                }
            }}
        >
            <Filter size={15} style={styles.icon} />
            {
                intl.formatMessage({ id: 'filter' })
            }
        </div>
        <ViewPropertiesMenu
            anchorEl={anchorEl}
            onClose={handleClose}
            onChange={handleItemClick}
            properties={properties}
        />
    </>
    )
}


const styles = {
    menuItem: {
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    menuContent: {
        display: 'flex',
        width: '130px',
        margin: '0px',
        flexDirection: 'row',
        justifyContent: 'flex-start',
        alignContent: 'center',
        alignItems: 'center'
    },

    button: {
        display: 'flex',
        width: 'max-content',
        margin: '0px',
        flexDirection: 'row',
        justifyContent: 'flex-start',
        alignContent: 'center',
        alignItems: 'center',
        cursor: 'pointer',
        color: 'gray',
        fontSize: '14px',
    },

    icon: {
        marginRight: 4
    }
}