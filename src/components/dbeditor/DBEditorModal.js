import * as React from 'react';
import { DB_EDITOR_DIALOG, REFRESH_DOC } from 'src/constants/actionTypes';
import { useDispatch, useSelector } from 'react-redux';
import { DialogContent, Dialog, Button, DialogTitle, DialogActions } from '@mui/material';
import { useIntl } from 'react-intl';
import { useHistory, useLocation } from 'react-router-dom';

import { OpenInFull } from '@styled-icons/material/OpenInFull';
import DBEditorFrame from './DBEditorFrame';

const DBEditorModal = () => {
    const intl = useIntl();
    const dialogState = useSelector(state => state.uiState.dbEditorDialog) || { visible: false };
    const { hid, parent, visible } = dialogState;
    const dispatch = useDispatch();
    const history = useHistory();

    const handleClose = () => {
        dispatch({ type: DB_EDITOR_DIALOG, value: { visible: false } });
        setTimeout(() => {
            dispatch({ type: REFRESH_DOC, value: parent });
        }, 100);
    }

    return (
        <Dialog
            open={visible}
            onClose={handleClose}
            scroll='paper'
            aria-labelledby="scroll-dialog-title"
            aria-describedby="scroll-dialog-description"
            maxWidth='lg'
            style={{
                zIndex: 100,
            }}
        >
            <DialogContent dividers={true} style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', height: 820, width: 920, padding: 0 }}>
                <DBEditorFrame
                    hid={hid}
                />
            </DialogContent>

            <div style={{
                position: 'absolute',
                bottom: 10,
                right: 10,
                zIndex: 999
            }}>
                <Button onClick={handleClose}>{intl.formatMessage({ id: 'done' })} </Button>
            </div>

            <div style={{
                position: 'absolute',
                top: 10,
                right: 20,
                zIndex: 999,
            }}>
                <OpenInFull
                    style={{
                        width: '20px',
                        height: '20px',
                        marginRight: '3px',
                        color: 'gray'
                    }}
                    className="hoverStand"
                    onClick={() => {
                        history.push(`/db?hid=${hid}`);
                        handleClose();
                    }}
                />
            </div>
        </Dialog>
    );
}

export default DBEditorModal;
