import React, { useCallback, useEffect, useMemo, useState } from 'react'
import ReactDOM from 'react-dom'

import { Plus } from '@styled-icons/boxicons-regular/Plus';
import { MoreHoriz } from '@styled-icons/material';
import { TriangleRight } from '@styled-icons/entypo/TriangleRight';
import { TriangleDown } from '@styled-icons/entypo/TriangleDown';

import { useDispatch, useSelector } from 'react-redux';
import { getDoc, getDocAsMarkdown, upsertViewDataOrder } from 'src/actions/ticketAction';
import { useIntl } from 'react-intl';
import { addDBRow, addOptionToProperty, getViewProperties, deleteDBRow, insertColumn, updateDBProperty, updateDocProperties, nonTagId, updateRowData, getGroupByOptions, filterDbData, fillDbData, sortDbData, confirmToRemoveSortSettings, fillAdvancedDbData, onDuplicateRow, onDeleteRow, onInsertRow } from './DBUtil';
import { getState, getStateByUser } from 'src/reducers/listReducer';
import { DATA_EDITOR_DIALOG, DOC_HTML_ACTIONS } from 'src/constants/actionTypes';
import { Collapse, Divider, Grid, List, ListItem, TextareaAutosize } from '@mui/material';
import { cloneDeep } from 'lodash';
import { Inbox } from '@styled-icons/bootstrap/Inbox';
import { CellView } from './CellView';
import { useDrag, useDrop } from 'react-dnd'
import { DataMenu } from './DataMenu';
import { KeyboardArrowDown } from '@styled-icons/material/KeyboardArrowDown';
import { KeyboardArrowUp } from '@styled-icons/material/KeyboardArrowUp';
import { PopoverInput } from '../common/PopoverInput';
// import { createPlateEditor, ELEMENT_CODE_BLOCK, CodeBlockElement } from '@udecode/plate-common';
// import { CONFIG } from '../editor/config/config';
// import { PLUGINS } from '../editor/config/plugins';
import { OptionChip } from './OptionChip';
import { createPlateEditor, createPlugins, withProps } from '@udecode/plate-common';
import { serializeHtml } from '@udecode/plate-serializer-html';
import { Editor } from '../plate-ui/editor';
import { ELEMENT_PARAGRAPH, createParagraphPlugin } from '@udecode/plate-paragraph';
import { ELEMENT_H1, ELEMENT_H2, ELEMENT_H3, ELEMENT_H4, ELEMENT_H5, ELEMENT_H6, createHeadingPlugin } from '@udecode/plate-heading';
import { ELEMENT_RIL_HIGHLIGHT, createRILHighlightPlugin } from '@/plate-plugins/ril-highlight/createRILHighlightPlugin';
import { ELEMENT_SUB_PAGE, createSubPageLinkPlugin } from '@/plate-plugins/sub-page-link/createSubPageLinkPlugin';
import { ELEMENT_CODE_BLOCK, ELEMENT_CODE_LINE, createCodeBlockPlugin } from '@udecode/plate-code-block';
import { ELEMENT_TABLE, ELEMENT_TD, ELEMENT_TH, createTablePlugin } from '@udecode/plate-table';
import { ELEMENT_BLOCKQUOTE, createBlockquotePlugin } from '@udecode/plate-block-quote';
import { ELEMENT_HR, createHorizontalRulePlugin } from '@udecode/plate-horizontal-rule';
import { ELEMENT_TODO_LI, createListPlugin, createTodoListPlugin } from '@udecode/plate-list';
import { ELEMENT_IMAGE, createImagePlugin, createMediaEmbedPlugin } from '@udecode/plate-media';
import { createLinkPlugin } from '@udecode/plate-link';
import { createAlignPlugin } from '@udecode/plate-alignment';
import { createBoldPlugin, createCodePlugin, createItalicPlugin, createStrikethroughPlugin, createSubscriptPlugin, createSuperscriptPlugin, createUnderlinePlugin } from '@udecode/plate-basic-marks';
import { createHighlightPlugin } from '@udecode/plate-highlight';
import { createFontBackgroundColorPlugin, createFontColorPlugin, createFontSizePlugin } from '@udecode/plate-font';
import { createKbdPlugin } from '@udecode/plate-kbd';
import { createIndentPlugin } from '@udecode/plate-indent';
import { createIndentListPlugin } from '@udecode/plate-indent-list';
import { createTrailingBlockPlugin } from '@udecode/plate-trailing-block';
import { RILHighlightElement } from '@/plate-plugins/ril-highlight/RILHighlightElement';
import { SubPageLinkElement } from '@/plate-plugins/sub-page-link/SubPageLinkElement';
import { BlockquoteElement } from '../plate-ui/blockquote-element';
import { CodeBlockElement } from '../plate-ui/code-block-element';
import { CodeLineElement } from '../plate-ui/code-line-element';
import { HeadingElement } from '../plate-ui/heading-element';
import { ImageElement } from '../plate-ui/image-element';
import { ParagraphElement } from '../plate-ui/paragraph-element';
import { TableElement } from '../plate-ui/table-element';
import { TableCellElement, TableCellHeaderElement } from '../plate-ui/table-cell-element';
import { HrElement } from '../plate-ui/hr-element';


import '../../scss/editor.css'
import { createNodeIdPlugin } from '@udecode/plate-node-id';
import { TodoListElement } from '../plate-ui/todo-list-element';

const components = {
  // customize your components by plugin key
  [ELEMENT_RIL_HIGHLIGHT]: RILHighlightElement,
  // [ELEMENT_SLIDES_EMBED]: SlidesEmbedElement,
  // [ELEMENT_DB_EMBED]: DBEmbedElement,
  [ELEMENT_SUB_PAGE]: SubPageLinkElement,
  [ELEMENT_BLOCKQUOTE]: BlockquoteElement,
  [ELEMENT_CODE_BLOCK]: CodeBlockElement,
  [ELEMENT_CODE_LINE]: CodeLineElement,
  [ELEMENT_HR]: HrElement,
  [ELEMENT_H1]: withProps(HeadingElement, { variant: 'h1' }),
  [ELEMENT_H2]: withProps(HeadingElement, { variant: 'h2' }),
  [ELEMENT_H3]: withProps(HeadingElement, { variant: 'h3' }),
  [ELEMENT_H4]: withProps(HeadingElement, { variant: 'h4' }),
  [ELEMENT_H5]: withProps(HeadingElement, { variant: 'h5' }),
  [ELEMENT_H6]: withProps(HeadingElement, { variant: 'h6' }),
  [ELEMENT_IMAGE]: ImageElement,
  [ELEMENT_PARAGRAPH]: ParagraphElement,
  [ELEMENT_TABLE]: TableElement,
  [ELEMENT_TD]: TableCellElement,
  [ELEMENT_TH]: TableCellHeaderElement,
};

const plugins = createPlugins(
  [
    createParagraphPlugin(),
    createBlockquotePlugin(),
    createTodoListPlugin(),
    createHeadingPlugin(),
    createImagePlugin(),
    createHorizontalRulePlugin(),
    createLinkPlugin(),
    createListPlugin(),
    createTablePlugin(),
    createMediaEmbedPlugin(),
    // createSlidesEmbedPlugin(),
    // createDBEmbedPlugin(),
    createNodeIdPlugin(),
    createCodeBlockPlugin(),
    createAlignPlugin(),
    createBoldPlugin(),
    createCodePlugin(),
    createItalicPlugin(),
    createHighlightPlugin(),
    createUnderlinePlugin(),
    createStrikethroughPlugin(),
    createSubscriptPlugin(),
    createSuperscriptPlugin(),
    createFontColorPlugin(),
    createFontBackgroundColorPlugin(),
    createFontSizePlugin(),
    createKbdPlugin(),
    createIndentPlugin(),
    createIndentListPlugin(),
    createTrailingBlockPlugin(),
    createSubPageLinkPlugin(),
    createRILHighlightPlugin({
      options: {
        readOnly: true,
      }
    })
  ],
  {
    components,
  }
)

const CustomListItem = ({
  onClick,
  setDndHoverState,
  properties,
  titleProperty,
  item,
  index,
  onDrop,
  onDeleteRow,
  onInsertRow,
  onDuplicateRow,
  laneId,
  style
}) => {
  const { id, title, metadata } = item;
  const intl = useIntl();

  const dropRef = React.useRef(null)
  const dragRef = React.useRef(null)

  const [{ opacity }, drag, preview] = useDrag(() => ({
    type: 'ROW',
    collect: (monitor) => ({
      opacity: monitor.isDragging() ? 0.4 : 1,
    }),
    item: () => {
      return { index, laneId, id }
    },
  }))

  const [, drop] = useDrop({
    accept: 'ROW',
    hover(item, monitor) {
      if (!dropRef.current) {
        return
      }
      const dragIndex = item.index
      const dragLaneId = item.laneId
      const hoverIndex = index
      const hoverLaneId = laneId
      // Don't replace items with themselves
      // if (dragIndex === hoverIndex && dragLaneId === hoverLaneId) {
      //   return
      // }
      // Determine rectangle on screen
      const hoverBoundingRect = dropRef.current.getBoundingClientRect()
      // Get vertical middle
      const hoverMiddleX =
        (hoverBoundingRect.right - hoverBoundingRect.left) / 2
      // Determine mouse position
      const clientOffset = monitor.getClientOffset()
      // Get pixels to the top
      const hoverClientX = clientOffset.x - hoverBoundingRect.left
      // Only perform the move when the mouse has crossed half of the items height
      // When dragging downwards, only move when the cursor is below 50%
      // When dragging upwards, only move when the cursor is above 50%
      // Dragging downwards
      // if (dragLaneId === hoverLaneId && dragIndex < hoverIndex && hoverClientY < hoverMiddleY) {
      //   return
      // }
      // // Dragging upwards
      // if (dragLaneId === hoverLaneId && dragIndex > hoverIndex && hoverClientY > hoverMiddleY) {
      //   return
      // }
      // Time to actually perform the action
      setDndHoverState({
        cardId: item.id,
        sourceLaneId: dragLaneId,
        targetLaneId: hoverLaneId,
        position: id === 'add' ? hoverIndex : (hoverIndex + ((hoverClientX > hoverMiddleX) ? 1 : 0)),
        hoverIndex,
        isRight: id === 'add' ? false : hoverClientX > hoverMiddleX,
      })
      // Note: we're mutating the monitor item here!
      // Generally it's better to avoid mutations,
      // but it's good here for the sake of performance
      // to avoid expensive index searches.
      item.index = hoverIndex
    },
    drop(item, monitor) {
      if (!dropRef.current) {
        return
      }

      const didDrop = monitor.didDrop();
      if (didDrop) {
        return;
      }

      onDrop && onDrop(item);
    },
  })

  preview(drop(dropRef))
  drag(dragRef)

  let bodyElements = null;
  if (properties && metadata && metadata.data) {
    bodyElements = properties.map(p => {
      if (!metadata.data[p.name]) {
        if (p.type === 'Title') {
          return (
            <CellView
              style={{ fontSize: '14px', fontWeight: 'bold', color: 'gray' }}
              key={p.name}
              property={p}
              value={'unTitled'}
              hasDocAttached={metadata.hasDocAttached}
            />
          )
        }
        return null;
      }

      return <CellView
        style={p.type === 'Title' ? { fontSize: '14px', fontWeight: 'bold', color: '#333' } : { fontSize: '12px', color: '#333' }}
        key={p.name}
        property={p}
        value={metadata.data[p.name]}
        hasDocAttached={metadata.hasDocAttached}
      />
    }).filter(e => e);
  }

  const [hovered, setHovered] = React.useState(false);

  return (
    <div
      ref={dropRef}
      style={{ ...style, opacity }}
    >
      {
        id === 'add'
          ? <div style={{
            cursor: 'pointer',
            width: '-webkit-fill-available',
            border: '1px solid #e0e0e0',
            borderRadius: '3px',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
            minHeight: '40px',
            color: 'gray'
          }}
            onClick={onInsertRow}
          >
            <Plus size={24} style={{ marginRight: 3 }} />
          </div>
          : <div
            ref={dragRef}
            onClick={() => onClick(id)}
            style={{
              backgroundColor: hovered ? '#f5f5f5' : '#fff',
              cursor: 'pointer',
              width: '-webkit-fill-available',
              border: '1px solid #e0e0e0',
              boxShadow: '1px 1px 5px #e0e0e0',
              // boxShadow: '1px 1px 2px rgba(0, 0, 0, 0.1)',
              borderRadius: '3px',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'flex-start',
              justifyContent: 'flex-start',
              height: '100%',
            }}

            onMouseEnter={() => setHovered(true)}
            onMouseLeave={() => setHovered(false)}
          >
            <div
              style={{
                display: 'flex',
                fontSize: '12px',
                height: '160px',
                alignItems: 'flex-start',
                paddingLeft: '8px',
                paddingRight: '8px',
                paddingTop: '8px',
                width: '-webkit-fill-available',
                opacity: 0.8,
                backgroundColor: metadata.docHtml ? '#f7f7f7' : 'transparent',
                // overflowWrap: 'anywhere',
                overflow: 'hidden',
              }}
            >
              {metadata.docHtml && <div dangerouslySetInnerHTML={{ __html: metadata.docHtml }} />}
            </div>

            {
              bodyElements && bodyElements.length > 0 &&
              <div
                style={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'flex-start',
                  rowGap: '8px',
                  padding: '8px',
                  fontSize: '0.8rem',
                  width: 'inherit',
                }}>
                {
                  bodyElements
                }
              </div>
            }

            {
              hovered &&
              <DataMenu
                style={{
                  position: 'absolute',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: '#333',
                  width: '28px',
                  height: '24px',
                  backgroundColor: '#fff',
                  right: '10px',
                  top: '10px',
                  cursor: 'pointer',
                  transition: 'opacity 0.2s ease-in-out',
                  borderRadius: '3px',
                  border: '1px solid #e0e0e0',
                }}

                onDelete={() => onDeleteRow(id)}
                onDuplicate={() => onDuplicateRow(id)}
              >
                <MoreHoriz size={18} />
              </DataMenu>
            }
          </div>
      }
    </div>
  )
}

const CustomList = (props) => {
  const intl = useIntl();
  const [collapses, setCollapses] = React.useState([]);

  const { style, data, viewProperties, titleProperty, groupByProperty, dndHoverState, setDndHoverState, handleDragEnd,
    onDeleteRow, onInsertRow, onDuplicateRow, onUpdateLaneLabel, onItemClick, mode, members, groupByOptions } = props;

  const cellHandlerWidth = mode === 'embed' ? 'max(50% - 400px, 0px)' : '56px';

  const [showHiddenGroups, setShowHiddenGroups] = React.useState(false);

  const laneRender = (lane, options) => {
    const option = (options || []).find(o => o.value === lane.id);
    if (!option) {
      return null;
    }

    return <div
      key={lane.id}
      style={{
        width: '100%',
      }}
    >
      <div
        style={{
          paddingTop: '10px',
          paddingLeft: '28px',
          fontSize: '14px',
          display: 'flex',
          flexDirection: 'row',
          alignItems: 'center',
        }}
      >
        <div
          className='hoverStand'
          style={{
            padding: '4px',
          }}
          onClick={() => {
            setCollapses(prev => {
              if (prev.includes(lane.id)) {
                return prev.filter(id => id !== lane.id);
              } else {
                return [...prev, lane.id];
              }
            })
          }}
        >
          {
            collapses.includes(lane.id) ?
              <TriangleRight size={18} /> :
              <TriangleDown size={18} />
          }
        </div>
        {lane.id === nonTagId && <Inbox size={18} style={{ marginRight: 3 }} />}
        {lane.id === nonTagId && lane.title}
        {lane.id !== nonTagId && ['Select', 'MultiSelect'].includes(groupByProperty.type) &&
          <PopoverInput
            value={lane.title}
            onSubmit={(value) => {
              onUpdateLaneLabel(lane.id, value);
            }}
          >
            <div
              className='hoverStand'
              style={{
                padding: '4px',
              }}
            >
              <OptionChip
                style={{ fontSize: 14 }}
                option={option}
              />
            </div>
          </PopoverInput>
        }
        {
          lane.id !== nonTagId && groupByProperty.type === 'Person' &&
          <CellView property={groupByProperty} value={[option.value]} members={members} />
        }
        <span style={{ color: 'gray', padding: '4px' }}>{lane.cards && (lane.cards.length - 1)}</span>
      </div>
      <Collapse in={!collapses.includes(lane.id)} timeout="auto" unmountOnExit>
        <CustomList
          {...props}
          data={lane}

          style={{
            width: '100%',
          }} />
      </Collapse>
      <Divider
        style={{
          marginTop: '10px',
          marginLeft: cellHandlerWidth,
        }}
      // variant="inset"
      />
    </div>
  }


  if (!data) return null;

  return <div style={{ ...style, width: '100%' }}>
    {
      data.cards &&
      <div
        className='gallery-container'
        style={{
          paddingLeft: cellHandlerWidth,
          // paddingTop: '10px',
          paddingBottom: '10px',
        }}
      >
        {data.cards.map((card, index) => {
          return (<div
            key={index}
            style={{
              display: 'flex',
              flexDirection: 'row',
              alignItems: 'center',
              position: 'relative',
            }}>

            {
              dndHoverState && dndHoverState.targetLaneId === data.id && index === dndHoverState.hoverIndex && !dndHoverState.isRight &&
              <Divider orientation="vertical" style={{ position: 'absolute', left: -9 }} sx={{ borderRight: '4px solid dodgerblue' }} />
            }

            <CustomListItem
              key={card.id}
              item={card}
              index={index}
              laneId={data.id}
              properties={viewProperties}
              titleProperty={titleProperty}
              setDndHoverState={setDndHoverState}
              onDrop={() => {
                handleDragEnd({ ...dndHoverState });
                setDndHoverState(null);
              }}
              onDeleteRow={onDeleteRow}
              onInsertRow={() => onInsertRow(data.id, data.cards.length > 1 ? data.cards[data.cards.length - 2].id : null)}
              onDuplicateRow={onDuplicateRow}
              onClick={(cardId) => {
                onItemClick(cardId)
              }}

              style={{
                width: '100%',
                height: '100%',
              }}
            />
            {
              dndHoverState && dndHoverState.targetLaneId === data.id && index === dndHoverState.hoverIndex && dndHoverState.isRight &&
              <Divider orientation="vertical" style={{ position: 'absolute', right: -9 }} sx={{ borderRight: '4px solid dodgerblue' }} />
            }

          </div>)
        })}
      </div>
    }
    {
      data.lanes && data.lanes.length > 0 &&
      <List>
        {
          data.lanes.map(lane => laneRender(lane, groupByOptions))
        }
      </List>
    }

    {
      data.hiddenLanes && data.hiddenLanes.length > 0 &&
      <div
        className='hoverStand'
        style={{
          display: 'flex',
          flexDirection: 'row',
          alignItems: 'center',
          marginLeft: '50px',
          width: 'max-content',
          borderRadius: '4px',
          marginTop: '4px',
          marginBottom: '4px',
          fontSize: '14px',
          color: '#555',
          cursor: 'pointer',
        }}

        onClick={() => {
          setShowHiddenGroups(!showHiddenGroups);
        }}
      >
        {data.hiddenLanes.length + ' '}
        {intl.formatMessage({ id: 'groups_hidden' })}
        {
          showHiddenGroups &&
          <KeyboardArrowUp size={20} style={{ marginLeft: '5px' }} />
        }
        {
          !showHiddenGroups &&
          <KeyboardArrowDown size={20} style={{ marginLeft: '5px' }} />
        }
      </div>
    }
    {
      showHiddenGroups &&
      <List>
        {
          data.hiddenLanes.map(lane => laneRender(lane, groupByOptions))
        }
      </List>
    }
  </div>
}

const styles = {
  addButton: {
    width: '100%',
    borderRadius: '3px',
    padding: '6px',
    margin: '2px',
    marginRight: '4px',
    color: 'gray',
  }
}



const GalleryView = ({ view, mode }) => {
  const intl = useIntl();
  const dispatch = useDispatch();
  const pageBy = 'orderFactor';

  const hid = view.dataSource;
  const docs = useSelector(state => state.docs);
  const doc = docs.byId[hid];
  const docHtmls = useSelector(state => state.docHtmls);
  const dbdata_list = useSelector(state => getState(state.dbdata_lists, hid));
  const view_dataorder_list = useSelector(state => getState(state.dbview_dataorder_lists, view._id));
  const toHtml = useSelector(state => state.uiState.toHtml);

  const loginUser = useSelector(state => state.loginIn.user);
  const orgs = useSelector(state => getStateByUser(state.org_lists, loginUser));
  const workingSpace = orgs.items.find(org => org._id === loginUser.workingOrgId) || { users: [] };
  const members = workingSpace.users.filter(user => !!user.user).map(user => user.user);

  const sortSettingsState = useSelector(state => state.viewSorts.byId[view._id]) || {};
  const filterSettingsState = useSelector(state => state.viewFilters.byId[view._id]) || {};

  const [filledAdancedDbData, setFilledAdancedDbData] = useState([]);
  const [filteredDbData, setFilteredDbData] = React.useState([]);
  const [filledDbData, setFilledDbData] = React.useState([]);
  const [rawListData, setRawListData] = React.useState([]);
  const [dbdata, setDbdata] = React.useState([]);

  const [viewProperties, setViewProperties] = React.useState([]);
  const serializeEditor = useMemo(() => createPlateEditor({
    plugins: plugins
  }), []);

  useEffect(() => {
    if (doc && doc.meta && doc.meta.properties) {
      let viewProperties = getViewProperties(doc.meta.properties, view).filter(p => !p.hide);

      setViewProperties(viewProperties);
    }
  }, [doc, view]);

  useEffect(() => {
    if (toHtml && toHtml.hid && docs && docs.byId[toHtml.hid]) {
      const html = serializeHtml(serializeEditor, { nodes: docs.byId[toHtml.hid].blocks });
      dispatch({
        type: DOC_HTML_ACTIONS.updated,
        item: {
          hid: toHtml.hid,
          html,
        }
      })
    }
  }, [toHtml]);

  useEffect(() => {
    if (dbdata_list && dbdata_list.items) {
      dbdata_list.items.forEach(item => {
        if (!!item.doc) {
          dispatch(getDoc({ hid: item.doc }, (doc) => {
            if (doc && doc.blocks) {
              const html = serializeHtml(serializeEditor, { nodes: doc.blocks });
              dispatch({
                type: DOC_HTML_ACTIONS.updated,
                item: {
                  hid: doc.hid,
                  html,
                }
              })
            }
          }));
        }
      });
    }
  }, [dbdata_list]);

  useEffect(() => {
    if (!dbdata_list || !dbdata_list.items || !viewProperties || viewProperties.length === 0) {
      return;
    }

    let advancedData = fillAdvancedDbData(dbdata_list.items, viewProperties);
    setFilledAdancedDbData(advancedData);

  }, [dbdata_list, viewProperties]);

  useEffect(() => {
    if (!filledAdancedDbData) {
      return;
    }

    let dbdata = filledAdancedDbData;

    if (filterSettingsState.filterGroup && filterSettingsState.filterGroup.filters && filterSettingsState.filterGroup.filters.length > 0) {
      dbdata = filterDbData(dbdata, filterSettingsState.filterGroup, viewProperties)
    }

    setFilteredDbData(dbdata);
  }, [filledAdancedDbData, filterSettingsState, viewProperties]);

  useEffect(() => {
    setFilledDbData(fillDbData(filteredDbData, viewProperties, members));
  }, [filteredDbData]);

  useEffect(() => {
    if (!doc || !filledDbData) {
      return;
    }

    let dbdata = sortDbData(filteredDbData, filledDbData, sortSettingsState, doc.meta && doc.meta.properties, view_dataorder_list.items);
    setDbdata(dbdata);
  }, [doc && doc.meta, sortSettingsState.sorts, filledDbData, view_dataorder_list]);

  const [listData, setListData] = React.useState(null);

  const [groupByProperty, setGroupByProperty] = React.useState(null);
  const [groupByOptions, setGroupByOptions] = React.useState([]);
  const titleProperty = doc && doc.meta && doc.meta.properties.find(p => p.type === 'Title');

  useEffect(() => {
    if (!doc || !doc.meta || !doc.meta.properties) {
      return;
    }

    let groupByProperty = doc.meta.properties.find(p => p.name === view.groupBy);

    if (groupByProperty && ['Select', 'MultiSelect', 'Person'].includes(groupByProperty.type)) {
      if (!groupByProperty.options) {
        groupByProperty.options = [];
      }

      setGroupByProperty(groupByProperty);
    } else {
      setGroupByProperty(null);
    }
  }, [doc, view.groupBy])

  useEffect(() => {
    if (!groupByProperty) {
      return;
    }

    let options = getGroupByOptions(intl, doc, view, dbdata_list.items);
    setGroupByOptions(options);
  }, [groupByProperty, doc, dbdata_list.items, view])

  useEffect(() => {
    if (!dbdata) {
      return;
    }

    if (!groupByProperty) {
      let cards = dbdata.map(d => {
        const data = d.data || {};
        return {
          id: d._id,
          title: data[titleProperty.name],
          orderFactor: d.orderFactor,

          metadata: {
            data,
            hasDocAttached: !!d.doc,
            doc: d.doc,
          }
        }
      });

      cards.push({
        id: 'add',
        metadata: {}
      })
      setRawListData({
        cards
      })
      return;
    }

    const lanes = groupByOptions.map(o => {
      let cards;
      if (o.value == nonTagId) {
        cards = dbdata.filter(d => {
          return !d.data || !d.data[view.groupBy] || d.data[view.groupBy].length === 0
            || !groupByOptions.map(o => o.value).filter(value => value === d.data[view.groupBy] || d.data[view.groupBy].includes(value)).length;
        })
      } else {
        cards = dbdata.filter(d => {
          if (!d.data || !d.data[view.groupBy]) {
            return false;
          }

          return groupByProperty.type === 'Select' && d.data[view.groupBy] === o.value
            || ['MultiSelect', 'Person'].includes(groupByProperty.type) && d.data[view.groupBy].includes(o.value)
        })
      }

      cards = cards.map(d => {
        return {
          id: d._id,
          title: d.data ? d.data[titleProperty.name] : '',
          orderFactor: d.orderFactor,

          metadata: {
            data: d.data,
            hasDocAttached: !!d.doc,
            doc: d.doc,
          }
        }
      })

      cards.push({
        id: 'add',
        metadata: {}
      });

      return {
        id: o.value,
        hide: o.hide,
        title: o.label,
        // label: '0/0',
        cards
      };
    });

    setRawListData({
      lanes: lanes.filter(o => !o.hide) || [],
      hiddenLanes: lanes.filter(o => o.hide) || [],
    });
  }, [dbdata, groupByProperty, groupByOptions, titleProperty, view]);

  useEffect(() => {
    if (!rawListData) {
      return;
    }

    setListData(fillListDataCardsDocHtml(rawListData, docHtmls));
  }, [rawListData, docHtmls]);


  const fillListDataCardsDocHtml = useCallback((listData, docHtmls) => {

    if (!docHtmls) {
      return listData;
    }

    let newListData = { ...listData };

    if (newListData.cards) {
      newListData.cards = newListData.cards.map(card => {
        return fillCardDocHtml(card, docHtmls);
      })
    }

    if (newListData.lanes) {
      newListData.lanes = newListData.lanes.map(lane => {
        return fillListDataCardsDocHtml(lane, docHtmls);
      })
    }

    if (newListData.hiddenLanes) {
      newListData.hiddenLanes = newListData.hiddenLanes.map(lane => {
        return fillListDataCardsDocHtml(lane, docHtmls);
      })
    }

    return newListData;
  }, []);

  const fillCardDocHtml = useCallback((card, docHtmls) => {
    if (!card.metadata.doc) {
      return card;
    }

    return {
      ...card,
      metadata: {
        ...card.metadata,
        docHtml: docHtmls.byId[card.metadata.doc]?.html || ''
      }
    }
  }, []);

  const onCardClick = useCallback((cardId) => {
    dispatch({
      type: DATA_EDITOR_DIALOG,
      value: {
        dataSourceHid: hid,
        dataId: cardId,
        toHtml: true,
        visible: true,
      }
    })
  }, [hid]);

  const [dndHoverState, setDndHoverState] = React.useState(null);

  const handleDragStart = useCallback((cardId, laneId) => {
    if (sortSettingsState && sortSettingsState.sorts && sortSettingsState.sorts.length > 0) {
      confirmToRemoveSortSettings(dispatch, view._id, intl);
      return;
    }
  }, [sortSettingsState]);

  const handleDragEnd = useCallback(({ cardId, sourceLaneId, targetLaneId, position, isRight }) => {
    const data = dbdata.find(d => d._id === cardId);
    if (!data) {
      return;
    }

    if (sortSettingsState && sortSettingsState.sorts && sortSettingsState.sorts.length > 0) {
      return;
    }

    const targetCards = (!targetLaneId ? listData : (listData.lanes.find(l => l.id === targetLaneId) || listData.hiddenLanes.find(l => l.id === targetLaneId)))?.cards.filter(c => c.id !== 'add');
    const draggedCards = (!sourceLaneId ? listData : (listData.lanes.find(l => l.id === sourceLaneId) || listData.hiddenLanes.find(l => l.id === sourceLaneId))).cards;
    const draggedCardIndex = draggedCards.findIndex(c => c.id === cardId);
    const draggedCard = draggedCards[draggedCardIndex];

    if (sourceLaneId === targetLaneId && (position === draggedCardIndex || isRight && position === draggedCardIndex + 1)) {
      return
    }

    let newOrderFactor = draggedCard.orderFactor;

    if (targetCards && targetCards.length > 0) {
      if (position === 0) {
        if (targetCards[0].orderFactor >= draggedCard.orderFactor) {
          newOrderFactor = targetCards[0].orderFactor + 10;
        }
      } else if (position > 0 && position < targetCards.length) {
        let upCard = targetCards[position - 1];
        let downCard = targetCards[position];

        if (upCard.orderFactor <= draggedCard.orderFactor || downCard.orderFactor >= draggedCard.orderFactor) {
          newOrderFactor = (upCard.orderFactor + downCard.orderFactor) / 2;
        }
      } else {
        if (targetCards[targetCards.length - 1].orderFactor < draggedCard.orderFactor) {
          newOrderFactor = targetCards[targetCards.length - 1].orderFactor - 10;
        }
      }
    } else {
      newOrderFactor = 0;
    }

    if (newOrderFactor !== draggedCard.orderFactor) {
      dispatch(upsertViewDataOrder({
        viewId: view._id,
        dataId: cardId,
        data: {
          orderFactor: newOrderFactor,
        }
      }));
    }

    if (sourceLaneId === targetLaneId || !targetLaneId) {
      return;
    }

    data.data = data.data || {};
    if (groupByProperty.type === 'Select') {
      data.data[view.groupBy] = targetLaneId === nonTagId ? '' : targetLaneId;
    } else {
      if (sourceLaneId === nonTagId) {
        data.data[view.groupBy] = [targetLaneId];
      } else if (targetLaneId === nonTagId) {
        data.data[view.groupBy] = [];
      } else {
        let currentValue = data.data[view.groupBy];

        currentValue = currentValue.filter(v => v !== sourceLaneId);
        currentValue.push(targetLaneId);

        data.data[view.groupBy] = currentValue;
      }
    }

    updateRowData(dispatch, view.dataSource, data);
  }, [dbdata, listData, groupByProperty, view, sortSettingsState]);

  const onUpdateLaneLabel = (laneId, label) => {
    const newOptions = [...groupByProperty.options];
    const option = newOptions.find(o => o.value === laneId);
    const newOption = { ...option, label };
    newOptions[newOptions.indexOf(option)] = newOption;

    updateDBProperty(dispatch, hid, doc.meta, {
      ...groupByProperty,
      options: newOptions,
    });
  }

  const addLane = (laneName) => {
    if (!laneName) {
      return;
    }

    let property = cloneDeep(groupByProperty);
    if (addOptionToProperty(laneName, property)) {
      updateDBProperty(dispatch, hid, doc.meta, property);
    }
  }


  if (!listData) {
    return null;
  }

  return (
    <div style={{
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'flex-start',
      // minWidth: '800px',
      paddingTop: '8px',
    }}>
      <CustomList
        data={listData}
        viewProperties={viewProperties}
        titleProperty={titleProperty}
        groupByProperty={groupByProperty}
        dndHoverState={dndHoverState}
        setDndHoverState={setDndHoverState}
        handleDragEnd={handleDragEnd}
        onDeleteRow={(dataId) => onDeleteRow(hid, dataId, dispatch)}
        onInsertRow={(laneId, dataId) => onInsertRow(hid, view, groupByProperty, dbdata, laneId, dataId, dbdata_list.items.length, sortSettingsState, dispatch, intl)}
        onDuplicateRow={(dataId) => onDuplicateRow(hid, view, dbdata, dataId, sortSettingsState, dispatch, intl)}
        onUpdateLaneLabel={onUpdateLaneLabel}
        onItemClick={onCardClick}
        mode={mode}
        members={members}
        groupByOptions={groupByOptions}
      />

      {
        groupByProperty &&
        groupByProperty.type !== 'Person' &&
        <div
          style={{
            backgroundColor: '#fcfcfc',
            borderRadius: '4px',
            margin: '4px 0px',
          }}
        >
          <PopoverInput
            onSubmit={(value) => {
              addLane(value);
            }}
          >
            <div
              className='hoverStand'
              style={{
                ...styles.addButton,
                marginLeft: '51px',
                width: 160,
                padding: '4px 0px',
                marginBottom: 40
              }}
            >
              <Plus size={20} style={{ marginRight: 3 }} />
              {intl.formatMessage({ id: 'add_option_group' })}
            </div>
          </PopoverInput>
        </div>
      }

    </div>
  )
}

export default GalleryView;
