import { TextareaAutosize, Popover, Divider } from "@mui/material";
import { useEffect, useState } from "react";
import { useIntl } from "react-intl";
import { Trash } from '@styled-icons/bootstrap/Trash';
import { Duplicate } from '@styled-icons/boxicons-regular/Duplicate';

export const DataMenu = ({ style, onDelete, onDuplicate, onClose, children }) => {
    const intl = useIntl();
    const [anchorEl, setAnchorEl] = useState(null);
    const [hoveredIndex, setHoveredIndex] = useState(null);

    const handleClose = () => {
        setAnchorEl(null);
        onClose && onClose();
    }

    const menuItems = [{
        label_intl_id: 'delete',
        icon: <Trash size={18} style={styles.icon} />,
        onClick: onDelete
    }, {
        label_intl_id: 'duplicate',
        icon: <Duplicate size={18} style={styles.icon} />,
        onClick: onDuplicate
    }]

    return <div
        style={style}
        onClick={(event) => {
            event.preventDefault();
            event.stopPropagation();

            if (anchorEl) {
                return false;
            }
            setAnchorEl(event.currentTarget);
        }}
    >
        {
            children
        }

        <Popover
            open={Boolean(anchorEl)}
            onClose={handleClose}

            anchorEl={anchorEl}
            anchorOrigin={{
                vertical: 'top',
                horizontal: 'right',
            }}
            transformOrigin={{
                vertical: 'top',
                horizontal: 'left',
            }}
        >
            <div style={{ width: '200px', padding: '6px 12px' }}>
                {
                    menuItems.map(item => {
                        return <div
                            key={item.label_intl_id}
                            style={{
                                display: 'flex',
                                flexDirection: 'row',
                                alignItems: 'center',
                                backgroundColor: hoveredIndex === item.label_intl_id ? '#eee' : 'white',
                                padding: '2px 6px 2px 6px',
                                cursor: 'pointer',
                            }}
                            onMouseEnter={() => {
                                setHoveredIndex(item.label_intl_id);
                            }}
                            onMouseLeave={() => {
                                setHoveredIndex(null);
                            }}

                            onClick={(e) => {
                                item.onClick(e);
                                handleClose();
                            }}>
                            {
                                item.icon
                            }
                            {
                                intl.formatMessage({ id: item.label_intl_id })
                            }
                        </div>
                    })
                }
            </div>
        </Popover>
    </div>
};

const styles = {
    menuItem: {
        display: 'flex',
        width: '160px',
        margin: '0px',
        flexDirection: 'row',
        justifyContent: 'flex-start',
        alignContent: 'center',
        alignItems: 'center'
    },

    icon: {
        marginRight: 5
    }
}