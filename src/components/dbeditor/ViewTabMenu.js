import { Divider, Popover } from "@mui/material";
import { useEffect, useMemo, useState } from "react";
import { KeyboardReturn } from '@styled-icons/material/KeyboardReturn';
import { useDispatch } from "react-redux";
import { deleteDbView, updateDbView } from "src/actions/ticketAction";
import { useIntl } from "react-intl";
import { Trash } from "@styled-icons/bootstrap/Trash";
import { Link45deg } from '@styled-icons/bootstrap/Link45deg';
import { OPERATION_SUCCESS } from "src/constants/actionTypes";

export const ViewTabMenu = ({ state, onClose, showDeleteBtn }) => {
    const lineInputStyle = {
        border: 'none',
        outline: 'none',
        margin: '0px 0px 4px 0px',
        padding: '10px 6px',
        margin: '0',
        fontSize: '1rem',
    }

    const { anchorEl, view } = state;
    const dispatch = useDispatch();
    const intl = useIntl();

    const [viewName, setViewName] = useState('');

    useEffect(() => {
        view && setViewName(view.name);
    }, [view]);

    const updateViewName = (name) => {
        if (!viewName || !view || viewName === view.name) {
            return;
        }

        dispatch(updateDbView({ viewId: view._id, hid: view.hid, data: { name }, pageBy: 'orderFactor' }, () => { }, 'viewtabmenu'));
    }

    const handleDeleteView = () => {
        dispatch(deleteDbView({ viewId: view._id, hid: view.hid, pageBy: 'orderFactor' }, () => {
        }, 'viewtabmenu'));

        onClose();
    }

    const getViewLink = () => {
        return `${window.location.origin}/#/embed/dbview?id=${view._id}`;
    }


    return <Popover
        open={Boolean(anchorEl)}
        onClose={() => onClose()}

        anchorEl={anchorEl}
        anchorOrigin={{
            vertical: 'bottom',
            horizontal: 'left',
        }}
        transformOrigin={{
            vertical: 'top',
            horizontal: 'left',
        }}

    >
        <div style={{
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'center',
            margin: '10px 16px'
        }}>
            <input
                type="text"
                style={{ border: '1px solid #ccc', outline: 'none', width: '-webkit-fill-available', borderRadius: 4, padding: 5 }}

                value={viewName}
                onChange={(e) => {
                    setViewName(e.target.value);
                }}

                onKeyPress={(e) => {
                    if (e.key === 'Enter') {
                        updateViewName(viewName);
                        onClose();
                    }
                }}

                onBlur={(e) => {
                    updateViewName(viewName);
                }}
            />
            {
                (viewName && view && viewName !== view.name) &&
                <KeyboardReturn size={20} style={{ cursor: 'pointer' }}
                    onClick={(e) => {
                        updateViewName(viewName);
                        onClose();

                    }} />
            }
        </div>
        <Divider />

        {
            showDeleteBtn &&
            <div
                className="hoverStand"
                style={{
                    padding: '10px 16px'
                }}
                onClick={handleDeleteView}
            >
                <div style={styles.menuItem}>
                    <Trash size={18} style={styles.icon} />
                    {
                        intl.formatMessage({ id: 'delete_view' })
                    }
                </div>
            </div>
        }

        <Divider />
        <div
            className="hoverStand"
            style={{
                padding: '10px 16px'
            }}
            onClick={() => {
                navigator.clipboard.writeText(getViewLink()).then(() => {
                    dispatch({
                        type: OPERATION_SUCCESS,
                        message: intl.formatMessage({ id: 'copied' }),
                      });
                }, () => {
                    dispatch({
                        type: OPERATION_SUCCESS,
                        message: intl.formatMessage({ id: 'copy_failed' }),
                      });
                });
            }}
        >
            <div style={styles.menuItem}>
                <Link45deg size={18} style={styles.icon} />
                {
                    intl.formatMessage({ id: 'copy_link' })
                }
            </div>
        </div>
    </Popover>
};

const styles = {
    menuItem: {
        display: 'flex',
        width: '160px',
        margin: '0px',
        flexDirection: 'row',
        justifyContent: 'flex-start',
        alignContent: 'center',
        alignItems: 'center'
    },

    icon: {
        marginRight: 5
    }
}