import * as React from 'react';
import { DATA_EDITOR_DIALOG, REFRESH_EDITOR_CONTENT } from 'src/constants/actionTypes';
import { useDispatch, useSelector } from 'react-redux';
import { DialogContent, Dialog, Button, DialogTitle, DialogActions } from '@mui/material';
import { useIntl } from 'react-intl';
import { useHistory, useLocation } from 'react-router-dom';

import DataEditorCore from './DataEditorCore';
import { OpenInFull } from '@styled-icons/material/OpenInFull';

const DataEditorModal = () => {
    const intl = useIntl();
    const dialogState = useSelector(state => state.uiState.dataEditorDialog) || { visible: false };
    const dispatch = useDispatch();
    const history = useHistory();

    const handleClose = () => {
        dispatch({ type: DATA_EDITOR_DIALOG, value: { visible: false } });
        // setTimeout(() => {
        //     dispatch({ type: REFRESH_EDITOR_CONTENT, value: hid });
        // }, 100);
    }

    return (
        <Dialog
            open={!!dialogState && dialogState.visible}
            onClose={handleClose}
            scroll='paper'
            aria-labelledby="scroll-dialog-title"
            aria-describedby="scroll-dialog-description"
            maxWidth='lg'
            style={{
                zIndex: 100,
            }}
        >
            {/* <div style={{ width: '100%', display: 'flex', flexDirection: 'row', justifyContent: 'space-between' }}>
                <Button variant='text' onClick={handleClose}>{intl.formatMessage({ id: 'close' })}</Button>
            </div> */}
            <DialogContent dividers={true} style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', height: 820, width: 960, padding: 0 }}>
                <DataEditorCore dataSourceHid={dialogState.dataSourceHid} dataId={dialogState.dataId} toHtml={dialogState.toHtml} />
            </DialogContent>

            <div style={{
                position: 'absolute',
                bottom: 10,
                right: 10,
            }}>
                <Button onClick={handleClose}>{intl.formatMessage({ id: 'done' })} </Button>
            </div>

            <div
                style={{
                    position: 'absolute',
                    top: 10,
                    right: 20,
                    marginRight: '3px',
                    paddingLeft: '10px',
                    paddingRight: '10px',
                    borderRadius: '5px',
                    color: 'gray'
                }}
                className="hoverStand"
            >
                <OpenInFull
                    size={20}
                    onClick={() => {
                        history.push(`/data?hid=${dialogState.dataSourceHid}&dataId=${dialogState.dataId}`);
                        handleClose();
                    }}
                />
            </div>
        </Dialog>
    );
}

export default DataEditorModal;
