import React, { useCallback, useEffect, useRef, useState } from 'react'

import { useDispatch, useSelector } from 'react-redux';
import { useIntl } from 'react-intl';
import { getViewProperties, filterDbData, fillDbData, fillAdvancedDbData, nonTagId, getGroupByOptions } from './DBUtil';
import { getState, getStateByUser } from 'src/reducers/listReducer';
import { getWeekNumberISO8601 } from "src/gantt-timeline/helpers/date-helper";

import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  PointElement,
  LineElement,
  LineController,
  BarController,
  ArcElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';
import { Pie, Chart } from 'react-chartjs-2';
import { OPTION_BG_COLORS } from 'src/constants/constants';


ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  BarController,
  Title,
  Tooltip,
  Legend,

  PointElement,
  LineElement,
  LineController,
  ArcElement
);

const ChartView = ({ view, mode }) => {
  const intl = useIntl();
  const dispatch = useDispatch();
  const cellHandlerWidth = mode === 'embed' ? 'max(50% - 400px, 0px)' : '44px';
  const pageBy = 'orderFactor';

  const hid = view.dataSource;
  const doc = useSelector(state => state.docs.byId[hid]);
  const dbdata_list = useSelector(state => getState(state.dbdata_lists, hid));

  const loginUser = useSelector(state => state.loginIn.user);
  const orgs = useSelector(state => getStateByUser(state.org_lists, loginUser));
  const workingSpace = orgs.items.find(org => org._id === loginUser.workingOrgId) || { users: [] };
  const members = workingSpace.users.filter(user => !!user.user).map(user => user.user);

  const filterSettingsState = useSelector(state => state.viewFilters.byId[view._id]) || {};

  const [filledAdancedDbData, setFilledAdancedDbData] = useState([]);
  const [filteredDbData, setFilteredDbData] = useState([]);
  const [filledDbData, setFilledDbData] = useState([]);
  const [groupByProperty, setGroupByProperty] = useState(null);
  const [groupByOptions, setGroupByOptions] = useState([]);
  const [nonTagOption, setNonTagOption] = useState();

  const [viewProperties, setViewProperties] = useState([]);
  const [axisProperty, setAxisProperty] = useState(null);
  const [dataSetsProperties, setDataSetsProperties] = useState([]);
  const [chartData, setChartData] = useState(null);

  useEffect(() => {
    if (doc && doc.meta && doc.meta.properties) {
      let viewProperties = getViewProperties(doc.meta.properties, view).filter(p => !p.hide);
      setViewProperties(viewProperties);

      view.axis && setAxisProperty(doc.meta.properties.find(p => p.name === view.axis.property));
      view.dataSets && setDataSetsProperties(view.dataSets.map(ds => {
        return {
          ...ds,
          ...(ds.property === 'item' ? { name: 'item', label: intl.formatMessage({ id: 'row_item_label' }), type: 'Item' } : doc.meta.properties.find(p => p.name === ds.property))
        }
      }));

      let groupByProperty = doc.meta.properties.find(p => p.name === view.groupBy);
      if (groupByProperty && ['Select', 'MultiSelect', 'Person'].includes(groupByProperty.type)) {
        if (!groupByProperty.options) {
          groupByProperty.options = [];
        }

        setGroupByProperty(groupByProperty);
      } else {
        setGroupByProperty(null);
      }
    }
  }, [doc, view]);

  useEffect(() => {
    if (!groupByProperty) {
      return;
    }

    let options = getGroupByOptions(intl, doc, view, dbdata_list.items);
    if (groupByProperty.type === 'Person') {
      options = options.map(option => {
        const member = members.find(m => m._id === option.value);
        return {
          ...option,
          label: member ? member.nickname : option.label,
        }
      });
    }

    setNonTagOption(options.find(option => option.value === nonTagId));
    setGroupByOptions(options);
  }, [groupByProperty, doc, dbdata_list.items, view])

  useEffect(() => {
    if (!dbdata_list || !dbdata_list.items || !viewProperties || viewProperties.length === 0) {
      return;
    }

    let advancedData = fillAdvancedDbData(dbdata_list.items, viewProperties);
    setFilledAdancedDbData(advancedData);

  }, [dbdata_list, viewProperties]);

  useEffect(() => {
    if (!filledAdancedDbData) {
      return;
    }

    let dbdata = filledAdancedDbData;

    if (filterSettingsState.filterGroup && filterSettingsState.filterGroup.filters && filterSettingsState.filterGroup.filters.length > 0) {
      dbdata = filterDbData(dbdata, filterSettingsState.filterGroup, viewProperties)
    }

    setFilteredDbData(dbdata);
  }, [filledAdancedDbData, filterSettingsState, viewProperties]);

  useEffect(() => {
    setFilledDbData(fillDbData(filteredDbData, viewProperties, members, view, intl));
  }, [filteredDbData, view]);

  const compareFunc = useCallback((a, b) => {
    if (a < b) {
      return -1;
    }
    if (a > b) {
      return 1;
    }

    return 0;
  }, []);

  const compareFuncForLabels = useCallback((a, b) => {
    if (a.value < b.value) {
      return -1;
    }
    if (a.value > b.value) {
      return 1;
    }

    return 0;
  }, []);

  const unTagged = 'Untagged';

  const getLabel = useCallback((data, property) => {
    if (!data[property.name]) {
      return { label: unTagged, value: '' };
    }

    let axisData = data[property.name];
    let label = axisData;

    if (property.type === 'Date') {
      axisData = new Date(axisData);
      let formator = {
        year: 'numeric',
        month: 'numeric',
      }

      if (view.axis.aggregateTo === 'week') {
        formator = { year: 'numeric' };
      } else if (view.axis.aggregateTo === 'month') {
        formator = { year: 'numeric', month: 'numeric' };
      } else if (view.axis.aggregateTo === 'day') {
        formator = { year: 'numeric', month: 'numeric', day: 'numeric' };
      } else if (view.axis.aggregateTo === 'quarter') {
        formator = { year: 'numeric' };
      } else if (view.axis.aggregateTo === 'year') {
        formator = { year: 'numeric' };
      }
      const weekNum = getWeekNumberISO8601(axisData);
      const quarterNum = Math.floor(axisData.getMonth() / 3) + 1;

      label = intl.formatDate(axisData, formator);

      if (view.axis.aggregateTo === 'week') {
        label = 'W' + weekNum + ', ' + label;
      } else if (view.axis.aggregateTo === 'quarter') {
        label = 'Q' + quarterNum + ', ' + label;
      }
    } else if (property.type === 'MultiSelect' || property.type === 'Person') {
      if (view.axis.aggregateTo != 'individual') {
        label.sort(compareFunc);
        label = label.join(', ');
      }
    }

    return { text: label, value: axisData };
  }, [view]);

  const aggregator = useCallback((value, dataSetProperty, axisProperty) => {
    if (dataSetProperty.type === 'Number' && axisProperty.name != dataSetProperty.name) {
      value = Number(value);
      if (isNaN(value)) {
        return 0;
      }

      return value;
    } else {
      return 1;
    }
  }, []);

  const shouldGroup = useCallback((chartType) => groupByProperty && groupByOptions && chartType === 'bar', [groupByProperty, groupByOptions]);

  useEffect(() => {
    if (!dataSetsProperties || !axisProperty || !filledDbData || !filledDbData.length) {
      return;
    }

    let data = {
      datasets: [],
      chartType: view.chartType
    };
    let labels = [];

    filledDbData.forEach(row => {
      let label = getLabel(row.data, axisProperty);

      if (Array.isArray(label.text)) {
        label.text.forEach(l => {
          if (!labels.find(lbl => lbl.text == l)) {
            labels.push({ text: l, value: l });
          }
        });
      } else {
        if (!labels.find(lbl => lbl.text == label.text)) {
          labels.push(label);
        }
      }
    });

    labels.sort(compareFuncForLabels);
    data.labels = labels.map(l => l.text);

    dataSetsProperties.forEach(ds => {
      const viewds = view.dataSets.find(vds => vds.property === ds.name);
      if (!viewds) {
        return;
      }

      const chartType = viewds.type || view.chartType;  // dataset chart type
      const shouldHandleGroup = shouldGroup(chartType);

      let dataSet = {
        label: ds.label,
        type: ['bar', 'line'].includes(view.chartType) ? chartType : undefined,
        data: labels.map(label => 0),
        backgroundColor: ['bar', 'line'].includes(view.chartType) ?
          (ds.color || 'azure') :
          labels.map((label, i) => OPTION_BG_COLORS[(i + 1) * 7 % OPTION_BG_COLORS.length].value),
        borderColor: ['line'].includes(view.chartType) ? (ds.color || 'azure') : undefined,
        borderWidth: 1,
      };

      let groupedData = {};
      if (shouldHandleGroup) {
        if (!nonTagOption) {
          return;
        }
        groupByOptions.forEach(option => {
          groupedData[option.label] = {
            label: option.label,
            data: labels.map(label => 0),
          };
        });
      }

      filledDbData.forEach(row => {
        let label = getLabel(row.data, axisProperty);
        let value = row.data[ds.name];

        if (Array.isArray(label.text)) {
          label.text.forEach(l => {
            if (!l) {
              l = unTagged;
            }

            let index = data.labels.indexOf(l);
            if (!shouldHandleGroup) {
              dataSet.data[index] += aggregator(value, ds, axisProperty);
            } else {
              let group = row.data[groupByProperty.name];

              if (!group || group.length === 0) {
                group = nonTagOption.label;
                groupedData[group].data[index] += aggregator(value, ds, axisProperty);
              } else {
                if (!Array.isArray(group)) {
                  if (!groupByOptions.map(o => o.label).includes(group)) {
                    group = nonTagOption.label;
                  }

                  groupedData[group].data[index] += aggregator(value, ds, axisProperty);
                } else {
                  group.forEach(g => {
                    if (!groupByOptions.map(o => o.label).includes(g)) {
                      g = nonTagOption.label;
                    }

                    groupedData[g].data[index] += aggregator(value, ds, axisProperty);
                  });
                }
              }
            }
          });
        } else {
          let index = data.labels.indexOf(label.text || unTagged);
          if (!shouldHandleGroup) {
            dataSet.data[index] += aggregator(value, ds, axisProperty);
          } else {
            let group = row.data[groupByProperty.name];

            if (!group || group.length === 0) {
              group = nonTagOption.label;
              groupedData[group].data[index] += aggregator(value, ds, axisProperty);
            } else {
              if (!Array.isArray(group)) {
                if (!groupByOptions.map(o => o.label).includes(group)) {
                  group = nonTagOption.label;
                }

                groupedData[group].data[index] += aggregator(value, ds, axisProperty);
              } else {
                group.forEach(g => {
                  if (!groupByOptions.map(o => o.label).includes(g)) {
                    g = nonTagOption.label;
                  }

                  groupedData[g].data[index] += aggregator(value, ds, axisProperty);
                });
              }
            }
          }
        }
      });

      if (!shouldHandleGroup) {
        data.datasets.push(dataSet);
      } else {
        data.datasets = data.datasets.concat(groupByOptions.map((option, index) => {
          return {
            ...dataSet,
            data: groupedData[option.label].data,
            backgroundColor: option.bgColor,
            label: ds.property === 'item' ? option.label : (ds.label + '/' + option.label),
            stack: ['bar', 'line'].includes(view.chartType) ? ds.label : undefined,
          };
        }));
      }
    });

    setChartData(data);

  }, [filledDbData, axisProperty, dataSetsProperties, groupByProperty, groupByOptions, view]);

  if (!chartData) {
    return null;
  }

  let options = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top',
      },
      // title: {
      //   display: true,
      //   text: 'Chart.js Bar Chart',
      // },
    },
  };

  return (
    <div style={{
      display: 'flex',
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      minWidth: '800px',
      paddingLeft: cellHandlerWidth,
      paddingRight: cellHandlerWidth,
    }}>

      {
        ['bar', 'line'].includes(chartData.chartType) &&
        <Chart type={chartData.chartType} options={options} data={chartData} />
      }
      {
        ['pie', 'doughnut'].includes(chartData.chartType) &&
        <div style={{ width: '75vh' }}>
          <Pie options={options} data={chartData} />
        </div>
      }
    </div>
  )
}

export default ChartView;
