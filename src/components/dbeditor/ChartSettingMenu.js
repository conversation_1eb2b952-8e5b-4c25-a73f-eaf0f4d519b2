import { Plus } from '@styled-icons/boxicons-regular/Plus';
import { useEffect, useState, useCallback, useMemo } from 'react';
import { useIntl } from 'react-intl';
import { Divider, Menu } from '@mui/material';
import { Check } from '@styled-icons/material-outlined';
import { useDispatch } from 'react-redux';
import { createDbView, updateDbView } from 'src/actions/ticketAction';
import { ModeEdit } from '@styled-icons/material-outlined/ModeEdit';
import { Selector } from '../common/Selector';
import { DB_PROPERTY_TYPES, OPTION_BG_COLORS } from 'src/constants/constants';
import { BGColorSelector } from './BGColorSelector';

export const ChartSettingMenu = ({ doc, view }) => {
    const intl = useIntl();
    const dispatch = useDispatch();
    const [anchorEl, setAnchorEl] = useState(null);

    const [axisProperty, setAxisProperty] = useState(null);

    useEffect(() => {
        if (!doc || !doc.meta || !doc.meta.properties) {
            return;
        }

        let axisProperty = doc.meta.properties.find(p => p.name === view.axis?.property);
        setAxisProperty(axisProperty);
    }, [doc, view]);

    const handleClose = () => {
        setAnchorEl(null);
    }

    const handleTypeChange = (type) => {
        dispatch(updateDbView({
            viewId: view._id,
            hid: view.hid,
            pageBy: 'orderFactor',
            data: {
                chartType: type,
            }
        }));
    }
    const handleAxisChange = (axis) => {
        dispatch(updateDbView({
            viewId: view._id,
            hid: view.hid,
            pageBy: 'orderFactor',
            data: {
                axis: {
                    ...view.axis,
                    ...axis
                }
            }
        }));
    }

    const handleDataSetsChange = (checked, dataset) => {
        let dataSets = [...view.dataSets];
        if (checked) {
            let index = dataSets.findIndex(p => p.property === dataset.property);
            if (index === -1) {
                dataset.color = OPTION_BG_COLORS[(dataSets.length + 1) * 3 % OPTION_BG_COLORS.length].value;
                dataSets.push(dataset);
            } else {
                dataSets[index] = dataset;
            }

        } else {
            dataSets = dataSets.filter(p => p.property !== dataset.property);
        }


        dispatch(updateDbView({
            viewId: view._id,
            hid: view.hid,
            pageBy: 'orderFactor',
            data: {
                dataSets
            }
        }));
    }

    const dateAggregateTo = useMemo(() => [{
        value: 'month',
        label: intl.formatMessage({ id: 'month' })
    }, {
        value: 'week',
        label: intl.formatMessage({ id: 'week' })
    }, {
        value: 'day',
        label: intl.formatMessage({ id: 'day' })
    }, {
        value: 'quarter',
        label: intl.formatMessage({ id: 'quarter' })
    }, {
        value: 'year',
        label: intl.formatMessage({ id: 'year' })
    }], [intl]);

    const chartTypes = useMemo(() => [{
        value: 'line',
        label: intl.formatMessage({ id: 'line' })
    }, {
        value: 'bar',
        label: intl.formatMessage({ id: 'bar' })
    }, {
        value: 'pie',
        label: intl.formatMessage({ id: 'pie' })
    }], [intl]);

    if (!doc) {
        return null;
    }

    return (<>
        <div className='hoverStand'
            style={styles.button}
            onClick={(e) => {
                setAnchorEl(e.currentTarget);
            }}
        >
            <ModeEdit size={16} style={styles.icon} />
            {
                intl.formatMessage({ id: 'view_config' })
            }
        </div>
        <Menu
            id="basic-menu"
            anchorEl={anchorEl}
            open={Boolean(anchorEl)}
            onClose={handleClose}
            MenuListProps={{
                'aria-labelledby': 'basic-button',
            }}
        >

            <div style={{
                margin: '0px 14px 6px 14px',
                fontWeight: 'bold',
                fontSize: 14,
                minWidth: '180px',
                color: '#333',
                width: 280,
            }}>
                {intl.formatMessage({ id: 'view_settings' })}
            </div>

            <Divider />

            <div style={{
                margin: '6px 14px 6px 14px',
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'flex-start',
                rowGap: '6px'
            }}>
                <div style={styles.menuItemTitle}>
                    {
                        intl.formatMessage({ id: 'type' })
                    }
                </div>

                <Selector
                    value={view.chartType}
                    options={
                        chartTypes
                    }
                    onChange={
                        (value) => handleTypeChange(value)
                    }

                    inputStyle={{
                        width: '-webkit-fill-available',
                    }}
                />


            </div>

            <div style={{
                margin: '6px 14px 6px 14px',
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'flex-start',
                rowGap: '6px'
            }}>
                <div style={styles.menuItemTitle}>
                    {
                        intl.formatMessage({ id: 'chart_axis' })
                    }
                </div>

                <Selector
                    value={view.axis && view.axis.property}
                    options={
                        doc?.meta.properties.map(p => {
                            return {
                                value: p.name,
                                label: p.label,
                                icon: DB_PROPERTY_TYPES.find(t => t.value === p.type).icon
                            }
                        })
                    }
                    onChange={
                        (value) => handleAxisChange({ property: value })
                    }

                    inputStyle={{
                        width: '-webkit-fill-available',
                    }}
                />

                {
                    axisProperty && ['Date', 'Person', 'MultiSelect'].includes(axisProperty.type) &&
                    <div>
                        <div style={styles.menuItemTitle}>
                            {
                                intl.formatMessage({ id: 'aggregate_to' })
                            }
                        </div>
                        {
                            axisProperty.type === 'Date' &&
                            <Selector
                                value={view.axis && view.axis.aggregateTo}
                                options={
                                    dateAggregateTo
                                }
                                onChange={
                                    (value) => handleAxisChange({ aggregateTo: value })
                                }

                                inputStyle={{
                                    width: '-webkit-fill-available',
                                }}
                            />
                        }
                        {
                            ['Person', 'MultiSelect'].includes(axisProperty.type) &&
                            <div>
                                <input type='checkbox'
                                    id='checkbox'
                                    checked={view.axis && view.axis.aggregateTo === 'individual'}
                                    onChange={(e) => {
                                        handleAxisChange({ aggregateTo: e.target.checked && 'individual' || 'group' });
                                    }}
                                />
                                <label htmlFor="checkbox">{intl.formatMessage({ id: 'aggregate_to_individual' })}</label>
                            </div>
                        }
                    </div>
                }
            </div>

            <div style={{
                margin: '6px 14px 6px 14px',
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'flex-start',
                rowGap: '6px'
            }}>
                <div style={styles.menuItemTitle}>
                    {
                        intl.formatMessage({ id: 'chart_datasets' })
                    }
                </div>
                {
                    doc.meta.properties
                        .filter(p => p.type === 'Number')
                        .concat([{ name: 'item', label: intl.formatMessage({ id: 'row_item_label' }), type: 'Item' }])
                        .map((p, index) => {
                            const dataset = view.dataSets && view.dataSets.find(ds => ds.property === p.name);

                            return <div key={p.name}
                                style={{
                                    alignItems: 'center',
                                    display: 'flex',
                                    flexDirection: 'row',
                                    justifyContent: 'space-between',
                                }}
                            >
                                <div
                                    style={{
                                        display: 'flex',
                                        flexDirection: 'row',
                                        alignItems: 'center',
                                    }}
                                >
                                    <input type='checkbox'
                                        id={p.name}
                                        style={{
                                            alignSelf: 'center',
                                            marginRight: '6px'
                                        }}
                                        checked={!!dataset}
                                        onChange={(e) => {
                                            handleDataSetsChange(e.target.checked, { property: p.name });
                                        }}
                                    />
                                    <label htmlFor={p.name}
                                        style={{
                                            alignSelf: 'center',
                                            display: 'flex',
                                            flexDirection: 'row',
                                            alignItems: 'center',
                                        }}
                                    >
                                        {
                                            DB_PROPERTY_TYPES.find(t => t.value === p.type)?.icon
                                        }
                                        {
                                            p.label
                                        }
                                    </label>
                                </div>
                                {
                                    dataset &&
                                    <div style={{
                                        display: 'flex',
                                        flexDirection: 'row',
                                        alignItems: 'center',
                                        columnGap: '6px',
                                    }}>
                                        {
                                            (view.chartType === 'bar' || view.chartType === 'line') &&
                                            <Selector
                                                value={dataset.type || view.chartType}
                                                options={
                                                    chartTypes.filter(t => t.value !== 'pie')
                                                }
                                                onChange={
                                                    (value) => handleDataSetsChange(true, { ...dataset, type: value })
                                                }

                                                inputStyle={{
                                                    width: '-webkit-fill-available',
                                                }}
                                            />
                                        }
                                        <BGColorSelector value={dataset.color} onChange={(color) => {
                                            handleDataSetsChange(true, { ...dataset, color });
                                        }} />
                                    </div>
                                }
                            </div>
                        })
                }
            </div>
        </Menu>
    </>
    )
}


const styles = {
    menuItem: {
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'flex-start',
        alignItems: 'center',
    },
    menuContent: {
        display: 'flex',
        width: '100%',
        margin: '0px',
        flexDirection: 'row',
        justifyContent: 'flex-start',
        alignContent: 'center',
        alignItems: 'center',
        minWidth: 170
    },

    menuItemTitle: {
        fontSize: 13,
        color: 'gray',
        flexDirection: 'row',
        justifyContent: 'flex-start',
        alignItems: 'center'
    },

    button: {
        display: 'flex',
        width: 'max-content',
        margin: '0px',
        flexDirection: 'row',
        justifyContent: 'flex-start',
        alignContent: 'center',
        alignItems: 'center',
        cursor: 'pointer',
        color: 'gray',
        fontSize: 14,
    },

    icon: {
        marginRight: 4
    }
}