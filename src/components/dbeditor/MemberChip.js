import { Close } from "@styled-icons/material/Close";

export const MemberChip = ({ member, onDelete }) => {
    return <div
        style={{
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
            height: '100%',
            width: 'fit-content',
            backgroundColor: member.bgColor || '#eee',
            borderRadius: '4px',
            cursor: 'pointer',
            padding: '2px 6px 2px 6px',
        }}
    >
        {member.nickname || member.username}

        {
            onDelete &&
            <Close onClick={onDelete} style={{ marginLeft: '2px' }} size={15} />
        }
    </div>
}
