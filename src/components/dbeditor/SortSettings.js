import { TextareaAutosize, Popover } from "@mui/material";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import { SORT_SETTINGS_DIALOG, VIEW_SORT_ACTIONS } from "src/constants/actionTypes";
import { ArrowUpward } from '@styled-icons/material-outlined/ArrowUpward'
import { ArrowDownward } from '@styled-icons/material-outlined/ArrowDownward'
import { ArrowSort } from '@styled-icons/fluentui-system-filled/';
import { KeyboardArrowDown } from '@styled-icons/material';
import { getViewProperties, removeSortSettings } from "./DBUtil";
import { DragIndicator } from '@styled-icons/material/DragIndicator';
import { Close } from '@styled-icons/material/Close';
import { Trash } from '@styled-icons/bootstrap/Trash';
import { Plus } from '@styled-icons/boxicons-regular/Plus';
import { SortableCard } from "../dndlist/SortableCard";
import { useIntl } from "react-intl";
import { DB_PROPERTY_TYPES } from "src/constants/constants";
import { Divider, FormControl, ListItemText, Menu, MenuItem, Select } from '@mui/material';

export const SortSettings = ({ view }) => {
    const lineInputStyle = {
        border: 'none',
        outline: 'none',
        margin: '0px 0px 4px 0px',
        padding: '10px 6px',
        margin: '0',
        fontSize: '1rem',
    }

    const btnRef = useRef(null);
    const intl = useIntl();
    const dialogState = useSelector(state => state.viewSorts.byId[view._id]) || {};
    const { visible } = dialogState;
    const doc = useSelector(state => state.docs.byId[view.dataSource]);

    const [sorts, setSorts] = useState(dialogState.sorts || []);
    const dispatch = useDispatch();
    const [properties, setProperties] = useState(null);

    useEffect(() => {
        if (!doc || !doc.meta || !doc.meta.properties) return;

        setProperties(getViewProperties(doc.meta.properties, view));
    }, [doc, view]);

    const handleClose = () => {
        dispatch({
            type: VIEW_SORT_ACTIONS.updated,
            item: {
                viewId: view._id,
                sorts: [...sorts],
                visible: false,
            }
        })
    }

    const submitChanges = (sorts) => {
        dispatch({
            type: VIEW_SORT_ACTIONS.updated,
            item: {
                viewId: view._id,
                sorts: [...sorts],
            }
        })
        setSorts(sorts);

    }

    const handleDelete = (name) => {
        submitChanges(sorts.filter(sort => sort.name !== name));
    }

    const handleAdd = () => {
        const newSorts = [...sorts];
        const propery = properties.find(p => !p.hide && !sorts.find(s => s.name === p.name));
        if (!propery) {
            return;
        }

        newSorts.push({
            name: propery.name,
            order: 'asc',
        });
        submitChanges(newSorts);
    }

    const moveCard = useCallback((dragIndex, hoverIndex) => {
        const newSorts = [...sorts];
        const dragCard = newSorts[dragIndex];
        newSorts.splice(dragIndex, 1);
        newSorts.splice(hoverIndex, 0, dragCard);
        submitChanges(newSorts);
    }, [sorts]);

    const handleChangeSortOrder = (name, value) => {
        submitChanges(sorts.map(sort => {
            if (sort.name === name) {
                return {
                    ...sort,
                    order: value,
                }
            }
            return sort;
        }));
    }

    const handleChangeSortName = (name, value) => {
        submitChanges(sorts.map(sort => {
            if (sort.name === name) {
                return {
                    ...sort,
                    name: value,
                }
            }

            if (sort.name === value) {
                return {
                    ...sort,
                    name: name,
                }
            }
            return sort;
        }));
    }

    return <div>
        <div
            ref={btnRef}
            className='round-corner-btn'
            onClick={() => {
                dispatch({
                    type: VIEW_SORT_ACTIONS.updated,
                    item: {
                        ...dialogState,
                        visible: !visible,
                    }
                })
            }}
        >
            {
                sorts.length === 1 && (sorts[0].order === 'asc'
                    ? <ArrowUpward size={14} style={{ paddingRight: 2, marginTop: 1 }} />
                    : <ArrowDownward size={14} style={{ paddingRight: 2, marginTop: 1 }} />)
            }
            {sorts.length > 1 && <ArrowSort size={14} style={{ paddingRight: 2 }} />}
            <div>
                {sorts.length === 1 &&
                    (properties && properties.find(vp => vp.name == sorts[0].name) ? properties.find(vp => vp.name == sorts[0].name).label : '1 ' + intl.formatMessage({ id: 'sort' }))}
                {sorts.length > 1 && `${sorts.length} ${intl.formatMessage({ id: 'sorts' })}`}
            </div>

            {<KeyboardArrowDown size={17} style={{ paddingLeft: 2, marginTop: 1 }} />}
        </div>
        <Popover
            open={visible || false}
            onClose={handleClose}

            anchorEl={btnRef.current}
            anchorOrigin={{
                vertical: 'bottom',
                horizontal: 'left',
            }}
            transformOrigin={{
                vertical: 'top',
                horizontal: 'left',
            }}

        >
            <div style={{ minWidth: 240, width: 'fit-content', padding: 0 }}>
                {sorts.map((sort, index) => {
                    return <SortableCard
                        key={sort.name}
                        index={index}
                        id={sort.name}
                        moveCard={moveCard}
                    >
                        <div
                            className='hoverStand'
                            style={{
                                display: 'flex',
                                justifyContent: 'space-between',
                                alignItems: 'center',
                                width: '-webkit-fill-available',
                                padding: '0px',
                                cursor: 'pointer',
                            }}
                        >
                            <div
                                style={{
                                    display: 'flex',
                                    alignItems: 'center',
                                    flexDirection: 'row'
                                }}
                            >
                                <DragIndicator
                                    size={18}
                                    style={{
                                        color: 'rgba(55, 53, 47, 0.3)',
                                        backgroundColor: 'transparent',
                                    }}
                                />
                                {/* <div> {item.label} </div> */}
                                <FormControl
                                    variant="standard"
                                    size="small"
                                    sx={{ m: 1, minWidth: 60, marginLeft: 2 }}
                                >
                                    <Select
                                        labelId="demo-customized-select-label"
                                        id="demo-customized-select"
                                        value={sort.name}
                                        onChange={(event) => handleChangeSortName(sort.name, event.target.value)}
                                    >
                                        {

                                            properties && properties.map(p => {
                                                if (p.hide) {
                                                    return null;
                                                }

                                                let ptype = DB_PROPERTY_TYPES.find(pt => pt.value === p.type);

                                                return <MenuItem
                                                    key={p.name}
                                                    value={p.name}
                                                >
                                                    {ptype.icon} {p.label}
                                                </MenuItem>
                                            })
                                        }
                                    </Select>
                                </FormControl>

                                <FormControl
                                    variant="standard"
                                    size="small"
                                    sx={{ m: 1, minWidth: 60, marginLeft: 2 }}
                                >
                                    <Select
                                        labelId="demo-customized-select-label"
                                        value={sort.order}
                                        onChange={(event) => handleChangeSortOrder(sort.name, event.target.value)}
                                    >
                                        <MenuItem
                                            key={'asc'}
                                            value={'asc'}
                                        >
                                            {intl.formatMessage({ id: 'asc' })}
                                        </MenuItem>
                                        <MenuItem
                                            key={'desc'}
                                            value={'desc'}
                                        >
                                            {intl.formatMessage({ id: 'desc' })}
                                        </MenuItem>
                                    </Select>
                                </FormControl>
                            </div>
                            <div
                                className='hoverStand'
                                style={{ color: 'gray', padding: '5px' }}
                                onClick={() => {
                                    handleDelete(sort.name);
                                }}

                            >
                                <Close size={18} style={{ color: 'rgba(55, 53, 47, 0.3)' }} />
                            </div>
                        </div>
                    </SortableCard >
                })
                }
            </div>

            <Divider />

            <div
                className="hoverStand"
                style={styles.menuItem}
                onClick={handleAdd}
            >
                <Plus size={18} style={styles.icon} />
                {
                    intl.formatMessage({ id: 'add_sort' })
                }
            </div>

            <div
                className="hoverStand"
                style={styles.menuItem}
                onClick={() => { removeSortSettings(dispatch, view._id); }}
            >
                <Trash size={18} style={styles.icon} />
                {
                    intl.formatMessage({ id: 'delete_sort' })
                }
            </div>
        </Popover >
    </div >
};

const styles = {
    menuItem: {
        display: 'flex',
        margin: '0px',
        flexDirection: 'row',
        justifyContent: 'flex-start',
        alignContent: 'center',
        alignItems: 'center'
    },

    icon: {
        marginRight: 5
    }
}
