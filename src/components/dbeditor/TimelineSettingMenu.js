import { Plus } from '@styled-icons/boxicons-regular/Plus';
import { useEffect, useState, useCallback } from 'react';
import { useIntl } from 'react-intl';
import { Divider, FormControl, ListItemText, Menu, MenuItem, Select, Switch, TextField, IconButton, Button, Popover, InputLabel, Chip, Checkbox, FormControlLabel } from '@mui/material';
import { Check } from '@styled-icons/material-outlined';
import { useDispatch } from 'react-redux';
import { createDbView, updateDbView } from 'src/actions/ticketAction';
import { ModeEdit } from '@styled-icons/material-outlined/ModeEdit';
import { Selector } from '../common/Selector';
import { insertColumn } from './DBUtil';
import { DateRange, MoreHoriz } from '@styled-icons/material';
import PropertyMenu from './PropertyMenu';
import { PopoverInput } from '../common/PopoverInput';
import { OPTION_BG_COLORS } from 'src/constants/constants';

export const TimelineSettingMenu = ({ doc, view }) => {
    const intl = useIntl();
    const dispatch = useDispatch();
    const [anchorEl, setAnchorEl] = useState(null);

    const handleClose = () => {
        setAnchorEl(null);
    }

    const [selectableProperties, setSelectableProperties] = useState([]);
    const [selectableTimeProperties, setSelectableTimeProperties] = useState([]);

    useEffect(() => {
        if (!doc || !doc.meta || !doc.meta.properties) return;

        setSelectableProperties(doc.meta.properties.filter(p => p.type === 'Date' && p.hasEndDate) || []);
        setSelectableTimeProperties(doc.meta.properties.filter(p => p.type === 'Date' && !p.hasEndDate) || []);

    }, [doc, view]);

    const handleTimelineChange = (timeline) => {
        dispatch(updateDbView({
            viewId: view._id,
            hid: view.hid,
            pageBy: 'orderFactor',
            data: {
                timeline: {
                    ...view.timeline,
                    ...timeline
                }
            }
        }));
    }

    const addDatePropertyMenu = (hasEndDate) => <PopoverInput
        onSubmit={(value) => {
            // addLane(value);
            console.log(value);
            insertColumn(dispatch, intl, doc.hid, doc.meta, doc.meta.properties.length, {
                type: 'Date',
                hasEndDate,
                label: value,
            });
        }}
    >
        <div
            className='hoverStand'
            style={{
                ...styles.menuContent,
                padding: '6px 14px 6px 14px',
                marginBottom: '8px',
                color: '#777'
            }}
        >
            <Plus size={20} style={{ marginRight: 3 }} />
            {intl.formatMessage({ id: 'add_property' })}
        </div>
    </PopoverInput>;

    return (<>
        <div className='hoverStand'
            style={styles.button}
            onClick={(e) => {
                setAnchorEl(e.currentTarget);
            }}
        >
            <ModeEdit size={16} style={styles.icon} />
            {
                intl.formatMessage({ id: 'view_config' })
            }
        </div>
        <Menu
            id="basic-menu"
            anchorEl={anchorEl}
            open={Boolean(anchorEl)}
            onClose={handleClose}
            MenuListProps={{
                'aria-labelledby': 'basic-button',
            }}
        >

            <div style={{
                margin: '0px 14px 6px 14px',
                fontWeight: 'bold',
                fontSize: 14,
                minWidth: '180px',
                color: '#333'
            }}>
                {intl.formatMessage({ id: 'view_settings' })}
            </div>

            <Divider />

            {
                view.timeline && !view.timeline.useTimePair &&
                <div style={{
                    margin: '6px 14px 6px 14px',
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'flex-start',
                    rowGap: '6px'
                }}>
                    <div style={styles.menuItemTitle}>
                        {
                            intl.formatMessage({ id: 'timelineby' })
                        }
                    </div>

                    <Selector
                        value={view.timeline && view.timeline.range}
                        options={
                            selectableProperties.map(p => {
                                return {
                                    value: p.name,
                                    label: p.label,
                                    icon: <DateRange size={18} style={{ paddingRight: '2px' }} />,
                                    // rightElement:  perpertyEditIcon(p)
                                }
                            })
                        }
                        onChange={
                            (value) => handleTimelineChange({ range: value })
                        }

                        inputStyle={{
                            width: '-webkit-fill-available',
                        }}

                        extraMenuItems={addDatePropertyMenu(true)}
                    />
                </div>
            }

            {
                view.timeline && view.timeline.useTimePair &&
                <div style={{
                    margin: '6px 14px 6px 14px',
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'flex-start',
                    rowGap: '6px'
                }}>
                    <div style={styles.menuItemTitle}>
                        {
                            intl.formatMessage({ id: 'timeline_start' })
                        }
                    </div>

                    <Selector
                        value={view.timeline && view.timeline.start}
                        options={
                            selectableTimeProperties
                                .map(p => {
                                    return {
                                        value: p.name,
                                        label: p.label,
                                        icon: <DateRange size={18} style={{ paddingRight: '2px' }} />
                                    }
                                })
                        }
                        onChange={
                            (value) => handleTimelineChange({ start: value })
                        }

                        inputStyle={{
                            width: '-webkit-fill-available',
                        }}

                        extraMenuItems={addDatePropertyMenu(false)}
                    />
                </div>
            }

            {
                view.timeline && view.timeline.useTimePair &&
                <div style={{
                    margin: '6px 14px 6px 14px',
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'flex-start',
                    rowGap: '6px'
                }}>
                    <div style={styles.menuItemTitle}>
                        {
                            intl.formatMessage({ id: 'timeline_end' })
                        }
                    </div>

                    <Selector
                        value={view.timeline && view.timeline.end}
                        options={
                            selectableTimeProperties
                                .filter(p => p.name !== (view.timeline && view.timeline.start))
                                .map(p => {
                                    return {
                                        value: p.name,
                                        label: p.label,
                                        icon: <DateRange size={18} style={{ paddingRight: '2px' }} />
                                    }
                                })
                        }
                        onChange={
                            (value) => handleTimelineChange({ end: value })
                        }

                        inputStyle={{
                            width: '-webkit-fill-available',
                        }}

                        extraMenuItems={addDatePropertyMenu(false)}
                    />
                </div>
            }

            <div style={{
                margin: '14px 12px 14px 12px',
                display: 'flex',
                alignItems: 'center',
                flexDirection: 'row',
                columnGap: '6px',
                fontSize: '14px',
            }}>
                <input type={'checkbox'}
                    checked={view.timeline && view.timeline.useTimePair || false}
                    onChange={(e) => {
                        handleTimelineChange({ useTimePair: e.target.checked })
                    }} />
                {
                    intl.formatMessage({ id: 'use_time_pair' })
                }
            </div>

            <Divider style={{
                marginTop: 12,
                marginBottom: 12,
            }}/>

            <div style={{
                    margin: '6px 14px 6px 14px',
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'flex-start',
                    rowGap: '6px'
                }}>
                    <div style={styles.menuItemTitle}>
                        {
                            intl.formatMessage({ id: 'timeline_color' })
                        }
                    </div>

                    <Selector
                        value={view.timeline && view.timeline.color}
                        options={
                            OPTION_BG_COLORS.map(c => {
                                return {
                                    value: c.value,
                                    label: c.label,
                                    rightElement:  <div style={{
                                        width: '60px',
                                        height: '24px',
                                        backgroundColor: c.value,
                                        borderRadius: '2px',
                                    }} />
                                }
                            })
                        }
                        onChange={
                            (value) => handleTimelineChange({ color: value })
                        }

                        inputStyle={{
                            width: '-webkit-fill-available',
                            backgroundColor: view.timeline && view.timeline.color || 'transparent',
                        }}

                        extraMenuItems={addDatePropertyMenu(true)}
                    />
                </div>
        </Menu>
    </>
    )
}


const styles = {
    menuItem: {
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'flex-start',
        alignItems: 'center',
    },
    menuContent: {
        display: 'flex',
        width: '100%',
        margin: '0px',
        flexDirection: 'row',
        justifyContent: 'flex-start',
        alignContent: 'center',
        alignItems: 'center',
        minWidth: 170
    },

    menuItemTitle: {
        fontSize: 13,
        color: 'gray',
        flexDirection: 'row',
        justifyContent: 'flex-start',
        alignItems: 'center'
    },

    button: {
        display: 'flex',
        width: 'max-content',
        margin: '0px',
        flexDirection: 'row',
        justifyContent: 'flex-start',
        alignContent: 'center',
        alignItems: 'center',
        cursor: 'pointer',
        color: 'gray',
        fontSize: 14,
    },

    icon: {
        marginRight: 4
    }
}