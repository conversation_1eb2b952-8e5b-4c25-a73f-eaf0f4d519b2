
import { useState } from 'react';
import DBList from './DBList';

const ListView = ({ view, mode }) => {
  const [listData, setListData] = useState([]);
  const [collapses, setCollapses] = useState([]);

  return (
    <div style={{
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      minWidth: '600px',
      paddingRight: 20
      // paddingLeft: cellHandlerWidth,
      // paddingRight: cellHandlerWidth,
    }}>
      <DBList
        view={view}
        listData={listData}
        setListData={setListData}
        collapses={collapses}
        setCollapses={setCollapses}
        mode={mode}
        />
    </div>
  )
}

export default ListView;
