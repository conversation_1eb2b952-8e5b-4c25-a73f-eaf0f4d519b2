import { MoreHoriz } from '@styled-icons/material';
import { useState, useCallback, useEffect } from 'react';
import { SortableCard } from '../dndlist/SortableCard';
import { OptionChip } from './OptionChip';
import { OptionEditor } from './OptionEditor';
import update from 'immutability-helper';
import { DragIndicator } from '@styled-icons/material/DragIndicator';

const OptionItem = ({ index, option, setOptionEditState, onClick, optionEditState, moveCard, itemRightIcon, itemRightIconClicked }) => {
    const [itemTargeted, setItemTargeted] = useState(false);

    return <SortableCard
        index={index}
        id={option.value}
        moveCard={moveCard}
    >
        <div
            style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                width: '-webkit-fill-available',
                backgroundColor: itemTargeted || optionEditState && optionEditState.option && optionEditState.option.value === option.value ? '#f5f5f5' : '#fff',
                padding: '0px',
                cursor: 'pointer',
            }}

            onMouseEnter={() => {
                setItemTargeted(true);
            }}
            onMouseLeave={() => {
                setItemTargeted(false);
            }}

            onClick={(event) => {
                if (onClick) {
                    onClick(event);
                } else {
                    setOptionEditState({
                        option,
                        anchorEl: event.currentTarget,
                    });
                }
            }}>
            <div
                style={{
                    display: 'flex',
                    alignItems: 'center',
                    flexDirection: 'row'
                }}
            >
                <DragIndicator
                    size={18}
                    style={{
                        color: 'rgba(55, 53, 47, 0.3)',
                        backgroundColor: 'transparent',
                    }}
                />
                <OptionChip
                    option={option}
                />
            </div>
            <div
                className='hoverStand'
                style={{ color: 'gray', padding: '5px' }}
                onClick={(event) => {
                    itemRightIconClicked ?
                        itemRightIconClicked(option) :
                        setOptionEditState({
                            option,
                            anchorEl: event.currentTarget,
                        });

                        event.preventDefault();
                        event.stopPropagation();
                }}

            >
                {
                    !!itemRightIcon && itemRightIcon
                }
                {
                    !itemRightIcon &&
                    <MoreHoriz size={16} style={{ color: 'rgba(55, 53, 47, 0.3)' }} />
                }
            </div>
        </div>
    </SortableCard >
}


export const OptionList = ({ options, updateOptions, itemRightIcon, itemRightIconClicked }) => {
    const [optionEditState, setOptionEditState] = useState({ option: {} });

    const [items, setItems] = useState(options);

    useEffect(() => {
        setItems(options);
    }, [options]);

    const moveCard = useCallback((dragIndex, hoverIndex) => {
        setItems((prevCards) =>
            update(prevCards, {
                $splice: [
                    [dragIndex, 1],
                    [hoverIndex, 0, prevCards[dragIndex]],
                ],
            }),
        )
    }, [])


    return <div style={{
        display: 'flex',
        flexDirection: 'column',
        width: '-webkit-fill-available',
        // padding: '0px 0px 0px 4px',
    }}
        onDrop={(event) => {
            updateOptions(items, 'moveCard');
        }}
    >
        {items.map((option, index) => {
            return <OptionItem
                key={option.value}
                index={index}
                option={option}
                setOptionEditState={setOptionEditState}
                optionEditState={optionEditState}
                moveCard={moveCard}
                itemRightIcon={itemRightIcon}
                itemRightIconClicked={itemRightIconClicked}
            />
        })}

        <OptionEditor
            state={optionEditState}
            saveValue={(option) => {
                // let options = property.options || [];
                items.forEach((o, i) => {
                    if (o.value === option.value) {
                        items[i] = option;
                    }
                });

                updateOptions(items, 'edit');
            }}

            onDelete={() => {
                updateOptions(items.filter(o => o.value !== optionEditState.option.value), 'delete');

                setOptionEditState({
                    option: {},
                    anchorEl: null,
                });
            }}

            onClose={() => {
                setOptionEditState({ option: {} });
            }}
        />
    </div>
}