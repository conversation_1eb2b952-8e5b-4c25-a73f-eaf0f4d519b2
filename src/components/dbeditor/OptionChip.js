import { Close } from "@styled-icons/material/Close";

export const OptionChip = ({ style, option, onDelete }) => {
    return <div
        style={{
            ...style,
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
            height: '100%',
            width: 'fit-content',
            backgroundColor: option && option.bgColor || 'white',
            borderRadius: '4px',
            cursor: 'pointer',
            padding: '1px 4px 1px 4px',
        }}
    >
        {option && option.label}

        {
            onDelete &&
            <Close onClick={onDelete} style={{ marginLeft: '2px' }} size={15} />
        }
    </div>
}
