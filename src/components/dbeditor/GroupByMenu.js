import { Plus } from '@styled-icons/boxicons-regular/Plus';
import { useEffect, useState, useCallback } from 'react';
import { useIntl } from 'react-intl';
import { Divider, FormControl, ListItemText, Menu, MenuItem, Select, Switch, TextField, IconButton, Button, Popover, InputLabel, Chip } from '@mui/material';
import { useDispatch } from 'react-redux';
import { updateDbView } from 'src/actions/ticketAction';
import { OptionList } from './OptionList';
import { Visibility } from '@styled-icons/material-outlined/Visibility';
import { VisibilityOff } from '@styled-icons/material-outlined/VisibilityOff';
import { updateDBProperty, nonTagId, getGroupByOptions, addOptionToProperty } from './DBUtil';
import { cloneDeep } from 'lodash';
import { KeyboardReturn } from '@styled-icons/material/KeyboardReturn';
import { Close } from '@styled-icons/material/Close';
import { LayoutThreeColumns } from '@styled-icons/bootstrap/LayoutThreeColumns';
import { useSelector } from 'react-redux';
import { getState, getStateByUser } from 'src/reducers/listReducer';

export const GroupByMenu = ({ view }) => {
    const intl = useIntl();
    const dispatch = useDispatch();
    const [anchorEl, setAnchorEl] = useState(null);
    const [showOptionInput, setShowOptionInput] = useState(false);
    const [optionInput, setOptionInput] = useState('');
    const doc = useSelector(state => state.docs.byId[view.dataSource]);
    const dbdata_list = useSelector(state => getState(state.dbdata_lists, view.dataSource));

    const loginUser = useSelector(state => state.loginIn.user);
    const orgs = useSelector(state => getStateByUser(state.org_lists, loginUser));
    const workingSpace = orgs.items.find(org => org._id === loginUser.workingOrgId) || { users: [] };
    const members = workingSpace.users.filter(user => !!user.user).map(user => user.user);

    const handleClose = () => {
        setAnchorEl(null);
    }

    const [selectableProperties, setSelectableProperties] = useState([]);
    const [groupByOptions, setGroupByOptions] = useState('');

    useEffect(() => {
        if (!doc || !doc.meta || !doc.meta.properties) return;

        setSelectableProperties(doc.meta.properties.filter(p => ['Select', 'MultiSelect', 'Person'].includes(p.type)) || []);

        const groupByProperty = doc.meta.properties.find(p => p.name === view.groupBy);
        if (!groupByProperty) {
            return;
        }

        let options = getGroupByOptions(intl, doc, view, dbdata_list.items);

        if (groupByProperty.type === 'Person') {
            options = options.map(o => {
                const member = members.find(m => m._id === o.value);
                return {
                    ...o,
                    label: member ? member.nickname : o.value,
                }
            });
        }

        setGroupByOptions(options);
    }, [doc, view, dbdata_list.items]);

    const handleGroupByChange = (e) => {
        dispatch(updateDbView({
            viewId: view._id,
            hid: view.hid,
            pageBy: 'orderFactor',
            data: { groupBy: e.target.value }
        }));
    }

    const deleteViewGroupBy = (e) => {
        e.preventDefault();
        e.stopPropagation();
        dispatch(updateDbView({
            viewId: view._id,
            hid: view.hid,
            pageBy: 'orderFactor',
            data: { groupBy: '' }
        }));
    }

    const handleHideOption = (option, hide) => {
        dispatch(updateDbView({
            viewId: view._id,
            hid: view.hid,
            pageBy: 'orderFactor',
            data: {
                groupByOptions: groupByOptions.map(o2 => {
                    if (o2.value == option.value) {
                        return { ...o2, hide };
                    }
                    return o2;
                })
            }
        }));
    }

    const updateGroupByOptions = (options, action) => {
        dispatch(updateDbView({
            viewId: view._id,
            hid: view.hid,
            pageBy: 'orderFactor',
            data: { groupByOptions: options }
        }));

        if (action === 'moveCard') {
            return;
        }

        let property = doc.meta.properties.find(p => p.name == view.groupBy);
        property = cloneDeep(property);
        property.options = options.filter(o => o.value !== nonTagId).map(o => {
            let newOption = { ...o };
            delete newOption.hide;
            return newOption;
        });

        updateDBProperty(dispatch, doc.hid, doc.meta, property);
    }

    const newOption = useCallback((label) => {
        let property = doc.meta.properties.find(p => p.name == view.groupBy);
        property = cloneDeep(property);

        if (addOptionToProperty(label, property)) {
            updateDBProperty(dispatch, doc.hid, doc.meta, property);
        }

        setOptionInput('');
    }, [doc, view]);

    const showOptions = groupByOptions && groupByOptions.filter(o => !o.hide);
    const hideOptions = groupByOptions && groupByOptions.filter(o => o.hide);

    return (<>
        <div className='hoverStand'
            style={styles.button}
            onClick={(e) => {
                setAnchorEl(e.currentTarget);
            }}
        >
            <LayoutThreeColumns size={13} style={styles.icon} />
            {
                intl.formatMessage({ id: 'groupby_setting' })
            }
        </div>
        <Menu
            id="basic-menu"
            anchorEl={anchorEl}
            open={Boolean(anchorEl)}
            onClose={handleClose}
            MenuListProps={{
                'aria-labelledby': 'basic-button',
            }}
        >

            <div style={{
                margin: '0px 14px 6px 14px',
                fontWeight: 'bold',
                fontSize: 14,
                minWidth: '180px',
                color: '#333'
            }}>
                {intl.formatMessage({ id: 'groupby_setting_title' })}
            </div>

            <Divider />

            <MenuItem
                key={'groupby'}
                style={{ ...styles.menuItem, marginTop: '6px' }}
            >
                <div style={styles.menuContent}>
                    {/* <ArrowRight size={18} style={styles.icon} /> */}
                    {
                        intl.formatMessage({ id: 'groupby' })
                    }
                </div>

                <FormControl
                    variant="standard"
                >
                    <Select
                        labelId="demo-customized-select-label"
                        id="demo-customized-select"
                        size='small'
                        value={view.groupBy}
                        onChange={handleGroupByChange}
                    >
                        {
                            selectableProperties.map(t => {
                                return <MenuItem
                                    key={t.name}
                                    value={t.name
                                    }>
                                    {t.icon} {t.label}
                                </MenuItem>
                            })
                        }
                        {
                            view.layout !== 'board' && <MenuItem
                                key={'none'}
                                onClick={deleteViewGroupBy}
                            >
                                {intl.formatMessage({ id: 'delete_groupby' })}
                            </MenuItem>

                        }
                    </Select>
                </FormControl>
            </MenuItem>

            {
                view.groupBy &&

                <Divider />
            }
            {
                view.groupBy &&
                showOptions &&
                <div style={{
                    margin: '0px 14px 6px 14px',

                }}>
                    <div style={{
                        fontSize: 13,
                        color: 'gray'
                    }}
                    >
                        {intl.formatMessage({ id: 'groups_shown' })}

                    </div>
                    <OptionList
                        options={showOptions}
                        itemRightIcon={<Visibility size={18} style={styles.icon} />}
                        itemRightIconClicked={(o) => handleHideOption(o, true)}
                        updateOptions={(options, action) => {
                            updateGroupByOptions(options.concat(hideOptions), action);
                        }}
                    />
                </div>
            }

            {
                view.groupBy &&
                hideOptions &&
                <div style={{
                    margin: '0px 14px 6px 14px',
                    fontSize: 13,
                    color: 'gray'
                }}>
                    <div style={{
                        fontSize: 13,
                        color: 'gray'
                    }}
                    >
                        {intl.formatMessage({ id: 'groups_hidden' })}
                    </div>
                    <OptionList
                        options={hideOptions}
                        itemRightIcon={<VisibilityOff size={18} style={styles.icon} />}
                        itemRightIconClicked={(o) => handleHideOption(o, false)}
                        updateOptions={(options, action) => {
                            updateGroupByOptions(showOptions.concat(options.map(o => {
                                return { ...o, hide: true };
                            })), action);
                        }}
                    />
                </div>
            }

            {
                view.groupBy &&
                <Divider />
            }

            {
                view.groupBy &&

                !showOptionInput &&
                <MenuItem
                    style={{
                        marginTop: '6px',
                    }}
                    onClick={() => {
                        setShowOptionInput(true);
                    }}
                >
                    <div style={styles.menuItem}>
                        <Plus size={18} style={styles.icon} />
                        {
                            intl.formatMessage({ id: 'add_option' })
                        }
                    </div>
                </MenuItem>
            }

            {
                view.groupBy &&
                showOptionInput &&
                <div
                    style={{
                        display: 'flex',
                        flexDirection: 'row',
                        alignItems: 'center',
                        margin: '10px 16px 2px 16px',
                    }}
                >
                    <input
                        type="text"
                        style={{
                            display: 'flex',
                            flexDirection: 'row',
                            alignItems: 'center',
                            justifyContent: 'center',
                            border: '1px solid #ccc',
                            outline: 'none',
                            // width: '-webkit-fill-available',
                            borderRadius: 4,
                            padding: 5,
                            marginRight: 4,
                        }}
                        autoFocus={true}
                        placeholder={intl.formatMessage({ id: 'option_input_placeholder' })}
                        value={optionInput}
                        onChange={(e) => {
                            setOptionInput(e.target.value);
                        }}
                        onKeyPress={(e) => {
                            if (e.key === 'Enter' && optionInput) {
                                newOption(optionInput);
                            }
                        }}
                    />
                    {
                        optionInput &&
                        <KeyboardReturn size={20} style={{ cursor: 'pointer' }} onClick={(e) => {
                            newOption(optionInput);
                        }} />

                    }
                    {
                        !optionInput &&
                        <Close size={20} style={{ cursor: 'pointer' }} onClick={() => setShowOptionInput(false)} />
                    }

                </div>
            }

        </Menu>
    </>
    )
}


const styles = {
    menuItem: {
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    menuContent: {
        display: 'flex',
        width: '130px',
        margin: '0px',
        flexDirection: 'row',
        justifyContent: 'flex-start',
        alignContent: 'center',
        alignItems: 'center'
    },

    button: {
        display: 'flex',
        width: 'max-content',
        margin: '0px',
        flexDirection: 'row',
        justifyContent: 'flex-start',
        alignContent: 'center',
        alignItems: 'center',
        cursor: 'pointer',
        color: 'gray',
        fontSize: 14,
    },

    icon: {
        marginRight: 4
    }
}