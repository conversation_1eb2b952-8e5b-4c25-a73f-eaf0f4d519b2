import React, { useCallback, useEffect, useRef, useState } from 'react'
import ReactDOM from 'react-dom'

import { Plus } from '@styled-icons/boxicons-regular/Plus';
import { MoreHoriz } from '@styled-icons/material/MoreHoriz';

import { useDispatch, useSelector } from 'react-redux';
import { newDbData, updateDbData, updateDbView, addDefaultStatusPropertyToDB, upsertViewDataOrder } from 'src/actions/ticketAction';
import { useIntl } from 'react-intl';
import { addDBRow, addOptionToProperty, getViewProperties, deleteDBRow, insertColumn, updateDBProperty, updateDocProperties, nonTagId, updateRowData, getGroupByOptions, filterDbData, fillDbData, sortDbData, confirmToRemoveSortSettings, fillAdvancedDbData, onDuplicateRow, onDeleteRow } from './DBUtil';
import { getState, getStateByUser } from 'src/reducers/listReducer';
import Board from 'react-trello';
import { DATA_EDITOR_DIALOG } from 'src/constants/actionTypes';
import { TextareaAutosize, Tooltip } from '@mui/material';
import { cloneDeep } from 'lodash';
import { OptionChip } from './OptionChip';
import { Inbox } from '@styled-icons/bootstrap/Inbox';
import { CellView } from './CellView';
import { DataMenu } from './DataMenu';
import { PopoverInput } from '../common/PopoverInput';

const CustomCard = ({
  onClick,
  className,
  title,
  id,
  cardStyle,
  onDelete,
  onDuplicate,
  properties,
  metadata,
  members
}) => {
  const clickDelete = e => {
    onDelete(id);
    e.stopPropagation();
  }

  let bodyElements = null;
  if (properties && metadata && metadata.data) {
    bodyElements = properties.map(p => {
      if (p.type === 'Title' || !metadata.data[p.name]) {
        return null;
      }

      return <CellView
        key={p.name}
        property={p}
        value={metadata.data[p.name]}
        members={members}
        hasDocAttached={metadata.hasDocAttached}
        style={{
          whiteSpace: 'inherit',
        }}
      />
    }).filter(e => e);
  }

  const [hovered, setHovered] = useState(false);

  return (
    <div
      onClick={onClick}
      style={{
        ...cardStyle,
        backgroundColor: hovered ? '#f5f5f5' : '#fff',
        cursor: 'pointer',
        borderRadius: '3px',
        boxShadow: '1px 1px 2px rgba(0, 0, 0, 0.1)',
        border: '1px solid #e0e0e0',
        margin: '2px',
        padding: '6px',
        minHeight: '40px',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
      }}
      className={className}

      onMouseEnter={() => setHovered(true)}
      onMouseLeave={() => setHovered(false)}
    >
      <header style={{
        display: 'flex',
        position: 'relative',
        flexDirection: 'row',
        paddingTop: '5px',
        paddingBottom: '5px',
        fontSize: 14,
        fontWeight: 500,
        color: title ? '#333' : 'lightgray',
        alignItems: 'center',
      }}>
        {title || 'Untitled'}
        {
          hovered &&
          <DataMenu
            style={{
              position: 'absolute',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: '#333',
              width: '28px',
              height: '24px',
              backgroundColor: '#fff',
              right: '4px',
              top: '4px',
              cursor: 'pointer',
              transition: 'opacity 0.2s ease-in-out',
              borderRadius: '3px',
              border: '1px solid #e0e0e0',
            }}

            onDelete={clickDelete}
            onDuplicate={() => onDuplicate(id)}
          >
            <MoreHoriz size={18} />
          </DataMenu>
        }
      </header>

      {
        bodyElements && bodyElements.length > 0 &&
        <div
          style={{
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            paddingTop: '6px',
            rowGap: '6px',
            fontSize: '0.7rem',
          }}>
          {
            bodyElements
          }
        </div>
      }
    </div>
  )
}

const NewCardForm = ({ onAdd }) => {
  const [title, setTitle] = useState('')
  const handleAdd = () => onAdd({ title })

  return <div
    style={{
      backgroundColor: '#fff',
      cursor: 'pointer',
      borderRadius: '3px',
      boxShadow: '1px 1px 2px rgba(0, 0, 0, 0.1)',
      border: '1px solid #e0e0e0',
      margin: '2px',
      padding: '6px',
      minHeight: '40px',
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'center',
    }}
  >
    <TextareaAutosize
      style={{
        border: 'none',
        outline: 'none',
        margin: '0px 0px 4px 0px',
        margin: '0',
        fontSize: '1rem',
        fontFamily: 'auto',
      }}
      value={title}
      onChange={(e) => setTitle(e.target.value.replace(/\n/g, ''))}
      placeholder={`Type a name`}
      autoFocus
      onBlur={handleAdd}
      onKeyPress={(e) => {
        if (e.key === 'Enter') {
          handleAdd();
        }
      }}

    />
  </div>
}

const styles = {
  addButton: {
    width: '100%',
    borderRadius: '3px',
    padding: '6px',
    margin: '2px',
    marginRight: '4px',
    color: 'gray',
  }
}

const AddCardButton = ({ onClick }) => {
  const intl = useIntl();

  return (
    <div
      onClick={onClick}
      className={'hoverStand'}
      style={
        styles.addButton
      }
    >
      <Plus size={20} style={{ marginRight: 3 }} />
      {intl.formatMessage({ id: 'new_row' })}
    </div>
  )
}

const LaneHeader = ({ id, title, groupByProperty, members, options, onUpdateLaneLabel, onCardAdd }) => {
  const intl = useIntl();
  const option = (options || []).find(o => o.value === id);
  if (!option) {
    return null;
  }

  return (
    <div
      style={{
        cursor: 'pointer',
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}
    >
      {
        id === nonTagId &&
        <div style={{
          display: 'flex',
          flexDirection: 'row',
          fontSize: 14,
          fontWeight: 'bold',
          alignItems: 'center',
          height: '24px'
        }}>
          <Inbox size={20} style={{ marginRight: '4px' }} /> {title}
        </div>
      }
      {
        id !== nonTagId && groupByProperty.type !== 'Person' &&
        <PopoverInput
          value={option ? option.label : title}
          onSubmit={(value) => {
            onUpdateLaneLabel(id, value);
          }}
        >
          <OptionChip
            style={{ fontSize: 14 }}
            option={option}
          />
        </PopoverInput>
      }
      {
        id !== nonTagId && groupByProperty.type === 'Person' &&
        <CellView property={groupByProperty} value={[option.value]} members={members} />
      }
      <Tooltip title={intl.formatMessage({ id: 'new_card' })} arrow placement="top">
        <div
          className='hoverStand'
          style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', padding: '2px', color: 'gray' }}
          onClick={() => {

          }}
        >
          <PopoverInput
            transformOrigin={{
              vertical: 'top',
              horizontal: 'right',
            }}
            onSubmit={(value) => {
              onCardAdd({ title: value }, id, 0);
            }}
          >
            <Plus size={20} />
          </PopoverInput>
        </div>
      </Tooltip>
    </div>
  )
}

const BoardView = ({ view, mode }) => {
  const intl = useIntl();
  const dispatch = useDispatch();
  const cellHandlerWidth = mode === 'embed' ? 'max(50% - 400px, 0px)' : '44px';
  const pageBy = 'orderFactor';

  const hid = view.dataSource;
  const doc = useSelector(state => state.docs.byId[hid]);
  const dbdata_list = useSelector(state => getState(state.dbdata_lists, hid));
  const view_dataorder_list = useSelector(state => getState(state.dbview_dataorder_lists, view._id));

  const loginUser = useSelector(state => state.loginIn.user);
  const orgs = useSelector(state => getStateByUser(state.org_lists, loginUser));
  const workingSpace = orgs.items.find(org => org._id === loginUser.workingOrgId) || { users: [] };
  const members = workingSpace.users.filter(user => !!user.user).map(user => user.user);

  const sortSettingsState = useSelector(state => state.viewSorts.byId[view._id]) || {};
  const filterSettingsState = useSelector(state => state.viewFilters.byId[view._id]) || {};

  const [filledAdancedDbData, setFilledAdancedDbData] = useState([]);
  const [filteredDbData, setFilteredDbData] = useState([]);
  const [filledDbData, setFilledDbData] = useState([]);
  const [dbdata, setDbdata] = useState([]);

  const [groupByProperty, setGroupByProperty] = useState(null);
  const [groupByOptions, setGroupByOptions] = useState([]);

  const [viewProperties, setViewProperties] = useState([]);
  useEffect(() => {
    if (doc && doc.meta && doc.meta.properties) {
      let viewProperties = getViewProperties(doc.meta.properties, view).filter(p => !p.hide);

      setViewProperties(viewProperties);
    }
  }, [doc, view]);


  useEffect(() => {
    if (!dbdata_list || !dbdata_list.items || !viewProperties || viewProperties.length === 0) {
      return;
    }

    let advancedData = fillAdvancedDbData(dbdata_list.items, viewProperties);
    setFilledAdancedDbData(advancedData);

  }, [dbdata_list, viewProperties]);

  useEffect(() => {
    if (!filledAdancedDbData) {
      return;
    }

    let dbdata = filledAdancedDbData;

    if (filterSettingsState.filterGroup && filterSettingsState.filterGroup.filters && filterSettingsState.filterGroup.filters.length > 0) {
      dbdata = filterDbData(dbdata, filterSettingsState.filterGroup, viewProperties)
    }

    setFilteredDbData(dbdata);
  }, [filledAdancedDbData, filterSettingsState, viewProperties]);

  useEffect(() => {
    setFilledDbData(fillDbData(filteredDbData, viewProperties, members));
  }, [filteredDbData]);

  useEffect(() => {
    if (!doc || !filledDbData) {
      return;
    }

    let dbdata = sortDbData(filteredDbData, filledDbData, sortSettingsState, doc.meta && doc.meta.properties, view_dataorder_list.items);

    setDbdata(dbdata);
  }, [doc && doc.meta, sortSettingsState.sorts, filledDbData, view_dataorder_list]);

  const [boardData, setBoardData] = useState({
    lanes: []
  });

  const titleProperty = doc && doc.meta && doc.meta.properties.find(p => p.type === 'Title');

  useEffect(() => {
    if (!doc || !doc.meta || !doc.meta.properties) {
      return;
    }

    let groupByProperty = doc.meta.properties.find(p => p.name === view.groupBy);

    if (groupByProperty && ['Select', 'MultiSelect', 'Person'].includes(groupByProperty.type)) {
      if (!groupByProperty.options) {
        groupByProperty.options = [];
      }

      setGroupByProperty(groupByProperty);
    } else {
      const selectableProperty = doc.meta.properties.find(p => ['Select', 'MultiSelect', 'Person'].includes(p.type));

      if (!selectableProperty) {
        dispatch(addDefaultStatusPropertyToDB({
          hid,
        }));
      } else {
        dispatch(updateDbView({
          viewId: view._id,
          hid: view.hid,
          pageBy: 'orderFactor',
          data: {
            groupBy: selectableProperty.name
          }
        }));
      }
    }
  }, [doc, view.groupBy])

  useEffect(() => {
    if (!groupByProperty) {
      return;
    }

    setGroupByOptions(getGroupByOptions(intl, doc, view, dbdata_list.items));
  }, [groupByProperty, doc, dbdata_list.items, view])

  useEffect(() => {
    if (!dbdata) {
      return;
    }

    if (!groupByProperty) {
      return;
    }

    const lanes = groupByOptions.map(o => {
      let cards;
      if (o.value == nonTagId) {
        cards = dbdata.filter(d => {
          return !d.data || !d.data[view.groupBy] || d.data[view.groupBy].length === 0
            || !groupByOptions.map(o => o.value).filter(value => value === d.data[view.groupBy]
              || d.data[view.groupBy].includes(value)).length;
        })
      } else {
        cards = dbdata.filter(d => {
          if (!d.data || !d.data[view.groupBy]) {
            return false;
          }

          return groupByProperty.type === 'Select' && d.data[view.groupBy] === o.value
            || ['MultiSelect', 'Person'].includes(groupByProperty.type) && d.data[view.groupBy].includes(o.value)
        })
      }

      cards = cards.map(d => {
        return {
          id: d._id,
          title: d.data ? d.data[titleProperty.name] : '',
          orderFactor: d.orderFactor,

          metadata: {
            data: d.data,
            hasDocAttached: !!d.doc
          }
        }
      })

      return {
        id: o.value,
        hide: o.hide,
        title: o.label,
        // label: '0/0',
        cards
      };
    });

    setBoardData({
      lanes: lanes.filter(o => !o.hide) || [],
      hiddenLanes: lanes.filter(o => o.hide) || [],
    });
  }, [dbdata, groupByProperty, groupByOptions, titleProperty, view, intl]);

  const onCardClick = useCallback((cardId) => {
    dispatch({
      type: DATA_EDITOR_DIALOG,
      value: {
        dataSourceHid: hid,
        dataId: cardId,
        visible: true,
      }
    })
  }, [hid]);


  const handleDragStart = useCallback((cardId, laneId) => {
    if (sortSettingsState && sortSettingsState.sorts && sortSettingsState.sorts.length > 0) {
      confirmToRemoveSortSettings(dispatch, view._id, intl);
      return;
    }
  }, [sortSettingsState, intl]);

  const handleDragEnd = useCallback((cardId, sourceLaneId, targetLaneId, position) => {
    const data = dbdata.find(d => d._id === cardId);
    if (!data) {
      return;
    }

    if (sortSettingsState && sortSettingsState.sorts && sortSettingsState.sorts.length > 0) {
      return;
    }

    const targetCards = boardData.lanes.find(l => l.id === targetLaneId).cards;
    const draggedCard = boardData.lanes.find(l => l.id === sourceLaneId).cards.find(c => c.id === cardId);

    if (sourceLaneId === targetLaneId) {
      let sourcePosition = boardData.lanes.find(l => l.id === sourceLaneId).cards.findIndex(c => c.id === cardId);
      targetCards.splice(sourcePosition, 1);
    }
    targetCards.splice(position, 0, draggedCard);

    let newOrderFactor = draggedCard.orderFactor;

    if (targetCards && targetCards.length > 1) {
      if (position === 0) {
        if (targetCards[1].orderFactor > draggedCard.orderFactor) {
          newOrderFactor = targetCards[1].orderFactor + 10;
        }
      } else if (position > 0 && position < targetCards.length - 1) {
        let upCard = targetCards[position - 1];
        let downCard = targetCards[position + 1];

        if (upCard.orderFactor <= draggedCard.orderFactor || downCard.orderFactor >= draggedCard.orderFactor) {
          newOrderFactor = (upCard.orderFactor + downCard.orderFactor) / 2;
        }
      } else {
        if (targetCards[targetCards.length - 2].orderFactor < draggedCard.orderFactor) {
          newOrderFactor = targetCards[targetCards.length - 2].orderFactor - 10;
        }
      }
    }

    if (newOrderFactor !== draggedCard.orderFactor) {
      dispatch(upsertViewDataOrder({
        viewId: view._id,
        dataId: cardId,
        data: {
          orderFactor: newOrderFactor,
        }
      }));
    }

    if (sourceLaneId === targetLaneId) {
      return;
    }

    data.data = data.data || {};
    if (groupByProperty.type === 'Select') {
      data.data[view.groupBy] = targetLaneId === nonTagId ? '' : targetLaneId;
    } else {
      if (sourceLaneId === nonTagId) {
        data.data[view.groupBy] = [targetLaneId];
      } else if (targetLaneId === nonTagId) {
        data.data[view.groupBy] = [];
      } else {
        let currentValue = data.data[view.groupBy];

        currentValue = currentValue.filter(v => v !== sourceLaneId);
        currentValue.push(targetLaneId);

        data.data[view.groupBy] = currentValue;
      }
    }

    updateRowData(dispatch, view.dataSource, data);
  }, [dbdata, boardData, groupByProperty, view, sortSettingsState]);

  const handleLaneDragEnd = useCallback((removedIndex, addedIndex) => {
    if (removedIndex === addedIndex) {
      return;
    }

    let lanes = boardData.lanes.map(lane => lane.id);
    if (!lanes || lanes.length === 0) {
      return;
    }

    const removedLaneId = lanes[removedIndex];
    lanes.splice(removedIndex, 1);
    lanes.splice(addedIndex, 0, removedLaneId);

    const newOptions = lanes.map(l => {
      return groupByOptions.find(o => o.value === l);
    });

    dispatch(updateDbView({
      viewId: view._id,
      hid: view.hid,
      pageBy: 'orderFactor',
      data: {
        groupByOptions: newOptions
      }
    }));
  }, [boardData, groupByProperty, hid, doc]);

  const onCardAdd = useCallback((card, laneId, index) => {
    addDBRow(dispatch, view.dataSource, dbdata, {
      [titleProperty.name]: card.title,
      [view.groupBy]: laneId === nonTagId ? '' : (groupByProperty.type === 'Select' ? laneId : [laneId]),
    }, index)

  }, [dbdata, groupByProperty, titleProperty, view]);

  const addLane = (laneName) => {
    if (!laneName) {
      return;
    }

    let property = cloneDeep(groupByProperty);
    if (addOptionToProperty(laneName, property)) {
      updateDBProperty(dispatch, hid, doc.meta, property);
    }
  }

  // const onCardDelete = useCallback((cardId) => {
  //   const data = dbdata.find(d => d._id === cardId);
  //   if (!data) {
  //     return;
  //   }

  //   deleteDBRow(dispatch, view.dataSource, data._id);
  // }, [dbdata]);

  const onUpdateLaneLabel = (laneId, label) => {
    const newOptions = [...groupByProperty.options];
    const option = newOptions.find(o => o.value === laneId);
    const newOption = { ...option, label };
    newOptions[newOptions.indexOf(option)] = newOption;

    updateDBProperty(dispatch, hid, doc.meta, {
      ...groupByProperty,
      options: newOptions,
    });
  }

  const hiddenOptons = view.groupByOptions && view.groupByOptions.filter(o => o.hide);

  return (
    <div style={{
      display: 'flex',
      flexDirection: 'row',
      alignItems: 'flex-start',
      minWidth: '800px',
      paddingLeft: cellHandlerWidth,
      paddingRight: cellHandlerWidth,
    }}>
      <div>
        <Board
          style={{
            background: '#fff',
            height: '-webkit-fill-available',
            padding: 0
          }}
          laneStyle={{
            background: '#fcfcfc',
            width: '260px',
            margin: '0px',
          }}

          draggable={true}
          editable={true}
          hideCardDeleteIcon={true}
          data={boardData}
          components={{
            Card: (props) => <CustomCard
              {...props}
              members={members}
              properties={viewProperties}
              onDelete={(dataId) => onDeleteRow(hid, dataId, dispatch)}
              onDuplicate={(dataId) => onDuplicateRow(hid, view, dbdata, dataId, sortSettingsState, dispatch, intl)}
            />,
            AddCardLink: (props) => <AddCardButton {...props} viewId={view._id} onClick={
              (e) => {
                console.log('add card', sortSettingsState);
                if (sortSettingsState && sortSettingsState.sorts && sortSettingsState.sorts.length > 0) {
                  confirmToRemoveSortSettings(dispatch, view._id, intl);
                  return;
                }
                props.onClick(e);
              }
            } />,
            NewCardForm: NewCardForm,
            LaneHeader: ({ id, title }) => <LaneHeader
              options={groupByOptions}
              members={members}
              groupByProperty={groupByProperty}
              id={id}
              title={title}
              onUpdateLaneLabel={onUpdateLaneLabel}
              onCardAdd={onCardAdd}
            />
          }}

          handleLaneDragEnd={handleLaneDragEnd}
          handleDragEnd={handleDragEnd}
          handleDragStart={handleDragStart}
          onCardClick={onCardClick}
          onCardAdd={onCardAdd}
        />
      </div>
      {
        hiddenOptons && hiddenOptons.length > 0 && boardData && boardData.hiddenLanes &&

        <div
          style={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'flex-start',
            margin: '0px 10px',
            minWidth: 180,
            rowGap: '10px',
            paddingLeft: '8px',
            paddingBottom: '10px',
            paddingTop: '10px',
            backgroundColor: '#fcfcfc',
            borderRadius: '4px',
          }}
        >
          <div
            style={{
              marginTop: '4px',
              marginBottom: '4px',
              fontSize: '14px',
              color: '#555',
            }}
          >
            {intl.formatMessage({ id: 'groups_hidden' })}
          </div>

          {hiddenOptons.map(o => {
            let optionLabel;
            if (o.value !== nonTagId) {
              optionLabel = <OptionChip
                key={o.value}
                option={o}
              />
            } else {
              optionLabel = (
                <div
                  key={o.value}
                  style={{
                    fontSize: '14px',
                  }}
                >
                  {o.label}
                </div>
              )
            }

            const hiddenLane = boardData.hiddenLanes.find(l => l.id === o.value)
            return <div
              key={o.value}
              style={{
                display: 'flex',
                flexDirection: 'row',
                justifyContent: 'flex-start',
                alignItems: 'center',
              }}
            >
              {optionLabel}
              <span style={{ marginLeft: '12px', color: '#555' }}>
                {hiddenLane && hiddenLane.cards ? hiddenLane.cards.length : 0}
              </span>
            </div>
          })}

        </div>
      }

      {
        groupByProperty && groupByProperty.type !== 'Person' &&
        <div
          style={{
            backgroundColor: '#fcfcfc',
            borderRadius: '4px',
            margin: '0px 4px',
            padding: '5px 0px'
          }}
        >
          <PopoverInput
            onSubmit={addLane}
            value={''}
          >
            <div
              className='hoverStand'
              style={{
                ...styles.addButton,
                // margin: '10px',
                marginLeft: '3px',
                width: 160,
                padding: '4px 4px',
              }}
            >
              <Plus size={20} style={{ marginRight: 3 }} />
              {
                intl.formatMessage({ id: 'add_option_group' })
              }
            </div>
          </PopoverInput>
        </div>
      }

    </div>
  )
}

export default BoardView;
