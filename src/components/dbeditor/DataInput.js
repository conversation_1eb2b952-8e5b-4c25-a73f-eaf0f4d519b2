import { TextareaAutosize, Popover, Divider } from "@mui/material";
import { cloneDeep } from "lodash";
import { useEffect, useMemo, useState } from "react";
import { MemberSelector } from "./MemberSelector";
import { OptionSelector } from "./OptionSelector";
import 'react-date-range/dist/styles.css'; // main style file
import 'react-date-range/dist/theme/default.css'; // theme css file
import { useIntl } from "react-intl";
import { DateSelector } from "./DateSelector";

export const DataInput = ({ state, closeAndSaveValue, navigateCell, updateProperty, style }) => {
    const intl = useIntl();
    const lineInputStyle = {
        border: 'none',
        outline: 'none',
        margin: '0px 0px 4px 0px',
        padding: '10px 6px',
        margin: '0',
        fontSize: '1rem',
        fontFamily: 'auto',
        ...style,
        width: '-webkit-fill-available',
    }

    const { anchorEl } = state;
    const [property, setProperty] = useState({});

    const [value, setValue] = useState('');

    useEffect(() => {
        setValue(state.value);
        setProperty(cloneDeep(state.property));
    }, [state]);

    if (!property) {
        return <div></div>
    }

    let inputEl;
    if (property.type === 'Title' || property.type === 'Text') {
        inputEl = <TextareaAutosize
            style={lineInputStyle}
            value={value}
            onChange={(e) => {
                setValue(e.target.value);
            }}

            autoFocus
            onFocus={(e) => {
                e.target.setSelectionRange(e.target.value.length, e.target.value.length);
            }}
        />
    } else if (property.type === 'Select' || property.type === 'MultiSelect') {
        inputEl = <OptionSelector
            value={value}
            enableMulti={property.type === 'MultiSelect'}
            options={property.options || []}
            selectDone={(value) => {
                closeAndSaveValue(value)
            }}

            updateValue={(value) => {
                setValue(value);
            }}
            updateOptions={(options) => {
                property.options = options;
                updateProperty(property)
            }}
        />
    } else if (property.type === 'Person') {
        inputEl = <MemberSelector
            value={value}

            updateValue={(value) => {
                setValue(value);
            }}
        />
    } else if (property.type === 'Date') {
        inputEl = <DateSelector
            value={value}
            setValue={setValue}
            property={property}
            closeAndSaveValue={closeAndSaveValue}
        />

    } else {
        inputEl = <input
            style={lineInputStyle}
            type={property.type === 'Number' && 'number'
                // || property.type === 'Date' && 'date'
                || property.type === 'Email' && 'email'
                || property.type === 'Phone' && 'phone'
                || 'text'}
            value={value}
            onChange={(e) => setValue(e.target.value)}

            autoFocus
        />
    }

    return <Popover
        open={Boolean(anchorEl)}
        onClose={() => closeAndSaveValue(value)}
        onKeyPress={(e) => {
            if (e.key === 'Enter') {
                closeAndSaveValue(value);
                navigateCell && navigateCell("down");
            }
        }}
        anchorEl={anchorEl}
        anchorOrigin={{
            vertical: property.type === 'MultiSelect' ? 'bottom' : 'top',
            horizontal: 'left',
        }}
        transformOrigin={{
            vertical: 'top',
            horizontal: 'left',
        }}

    >
        <div style={{
            width: anchorEl && anchorEl.offsetWidth,
            minWidth: '260px',
            padding: 0
        }}>
            {inputEl}
        </div>
    </Popover>
};