import { Popover, Divider } from "@mui/material";
import { useEffect, useState } from "react";
import { useIntl } from "react-intl";
import { OPTION_BG_COLORS } from "src/constants/constants";
import { Check, KeyboardArrowDown } from '@styled-icons/material';

export const BGColorSelector = ({ value, onChange }) => {
    const intl = useIntl();

    const [anchorEl, setAnchorEl] = useState(null);

    const [color, setColor] = useState(value);

    const handleClose = () => {
        setAnchorEl(null);
    }

    const [hoveredIndex, setHoveredIndex] = useState(null);

    return <div>
        <div
            style={{
                width: '48px',
                height: '16px',
                backgroundColor: color,
                border: '1px solid #ddd',
                marginRight: '8px',
            }}

            onClick={(e) => {
                setAnchorEl(e.currentTarget);
            }}
        />
        <Popover
            open={Boolean(anchorEl)}
            onClose={handleClose}

            anchorEl={anchorEl}
            anchorOrigin={{
                vertical: 'top',
                horizontal: 'right',
            }}
            transformOrigin={{
                vertical: 'top',
                horizontal: 'left',
            }}
        >
            <div style={{ width: '200px', padding: '6px 12px' }}>
                {
                    OPTION_BG_COLORS.map((c, index) => (
                        <div
                            key={index}
                            style={{
                                display: 'flex',
                                flexDirection: 'row',
                                alignItems: 'center',
                                justifyContent: 'space-between',
                                backgroundColor: hoveredIndex === index ? '#eee' : 'white',
                                padding: '2px 6px 2px 6px',
                            }}
                            onMouseEnter={() => {
                                setHoveredIndex(index);
                            }}
                            onMouseLeave={() => {
                                setHoveredIndex(null);
                            }}
                            onClick={() => {
                                // setOption({ ...option, bgColor: c.value });
                                // setColor(c.value);
                                setColor(c.value);
                                onChange && onChange(c.value);
                            }}
                        >
                            <div
                                style={{
                                    display: 'flex',
                                    flexDirection: 'row',
                                    alignItems: 'center',
                                }}
                            >
                                <div
                                    style={{
                                        width: '48px',
                                        height: '24px',
                                        backgroundColor: c.value,
                                        borderRadius: '4px',
                                        marginRight: '8px',
                                    }}
                                />
                                {
                                    c.label
                                }
                            </div>
                            {
                                c.value === color &&
                                <div>
                                    <Check size={15} style={{ marginRight: '0px' }} />
                                </div>
                            }
                        </div>
                    ))
                }
            </div>
        </Popover>
    </div>
};

const styles = {
    menuItem: {
        display: 'flex',
        width: '160px',
        margin: '0px',
        flexDirection: 'row',
        justifyContent: 'flex-start',
        alignContent: 'center',
        alignItems: 'center'
    },

    icon: {
        marginRight: 5
    }
}