import { DateRange, Calendar } from 'react-date-range';
import { useEffect, useState } from "react";
import { Divider } from '@mui/material';
import { useIntl } from 'react-intl';
import moment from 'moment';

const DateInput = ({ date, dateDisplayFormat, onChange }) => {
    const dateStr = moment(date).format(dateDisplayFormat);
    return <div
        style={{
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'flex-start',
            margin: '10px 22px 0px 18px',
            padding: '4px 6px 4px 6px',
            fontSize: '15px',
            border: '1px solid #ccc',
            columnGap: '6px',
            width: 'fit-content',
        }}
    >
        {dateStr}
        <Divider orientation="vertical" flexItem />
        <input
            type={'time'}
            value={moment(date).format('HH:mm')}
            style={{
                outline: 'none',
                border: 'none',
                fontSize: '15px',
                fontFamily: 'Roboto, sans-serif',
            }}
            onChange={(e) => {
                onChange(moment(`${dateStr} ${e.target.value}`).toDate());
            }}
        />
    </div>
}

export const DateSelector = ({ value, setValue, property, closeAndSaveValue }) => {
    const [dates, setDates] = useState([new Date(), new Date()]);
    const dateDisplayFormat = property.dateFormat || 'YYYY-MM-DD';
    const intl = useIntl();

    useEffect(() => {
        if (!value) {
            return;
        }

        if (Array.isArray(value)) {
            setDates(value.map(v => new Date(v)));
        } else {
            setDates([new Date(value)]);
        }
    }, [value]);


    const combineDayToDate = (date, value) => {
        let time = moment(date).format('HH:mm');
        return moment(`${moment(value).format(dateDisplayFormat)} ${time}`).toDate();
    }

    let calendarEl = null;
    if (property.hasEndDate) {
        calendarEl = <DateRange
            editableDateInputs={true}
            onChange={item => {
                setValue([combineDayToDate(dates[0], item.selection.startDate), combineDayToDate(dates[1], item.selection.endDate)]);
            }}
            dateDisplayFormat={dateDisplayFormat.replace('YYYY', 'yyyy').replace('DD', 'dd')}
            moveRangeOnFirstSelection={false}
            showDateDisplay={!property.hasTime}
            retainEndDateOnFirstSelection={false}
            ranges={[{
                startDate: dates[0],
                endDate: dates[1],
                key: 'selection',
            }]}
        />
    } else {
        calendarEl = <Calendar
            showDateDisplay={true}
            date={dates[0]}
            onChange={(date) => {
                setValue(combineDayToDate(dates[0], date));
            }}
        />
    }

    let dateInput;
    if (property.hasTime) {
        dateInput = (
            <>
                <DateInput
                    date={dates[0]}
                    dateDisplayFormat={dateDisplayFormat}
                    onChange={(date) => {
                        if (property.hasEndDate) {
                            setValue([date, dates[1]]);
                        } else {
                            setValue(date);
                        }
                    }}
                />
                {
                    property.hasEndDate &&
                    <DateInput
                        date={dates[1]}
                        dateDisplayFormat={dateDisplayFormat}
                        onChange={(date) => {
                            setValue([dates[0], date]);
                        }}
                    />
                }
            </>
        );
    }

    return <div style={{
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
    }}>
        {
            dateInput
        }
        {
            calendarEl
        }
        <Divider style={{ marginTop: 12, marginBottom: 0 }} />
        <div
            className="hoverStand"
            style={{
                padding: '10px 22px 10px 18px',
                fontSize: '15px',
            }}
            onClick={() => {
                closeAndSaveValue(null);
            }}
        >
            {intl.formatMessage({ id: 'clear_cell_data' })}
        </div>
    </div>
}