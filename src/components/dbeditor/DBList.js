import React, { useCallback, useEffect, useRef, useState } from 'react'
import ReactDOM from 'react-dom'

import { Plus } from '@styled-icons/boxicons-regular/Plus';
import { MoreHoriz } from '@styled-icons/material';
import { TriangleRight } from '@styled-icons/entypo/TriangleRight';
import { TriangleDown } from '@styled-icons/entypo/TriangleDown';

import { useDispatch, useSelector } from 'react-redux';
import { updateDbData, upsertViewDataOrder } from 'src/actions/ticketAction';
import { useIntl } from 'react-intl';
import { addDBRow, addOptionToProperty, getViewProperties, updateDBProperty, nonTagId, updateRowData, getGroupByOptions, filterDbData, fillDbData, sortDbData, confirmToRemoveSortSettings, fillAdvancedDbData, onDuplicateRow, onDeleteRow, onInsertRow } from './DBUtil';
import { getState, getStateByUser } from 'src/reducers/listReducer';
import { DATA_EDITOR_DIALOG } from 'src/constants/actionTypes';
import { Collapse, Divider, List } from '@mui/material';
import { cloneDeep } from 'lodash';
import { Inbox } from '@styled-icons/bootstrap/Inbox';
import { CellView } from './CellView';
import { useDrag, useDrop } from 'react-dnd'
import { DataMenu } from './DataMenu';
import { DragIndicator } from '@styled-icons/material/DragIndicator';
import { KeyboardArrowDown } from '@styled-icons/material/KeyboardArrowDown';
import { KeyboardArrowUp } from '@styled-icons/material/KeyboardArrowUp';
import { OpenInFull } from '@styled-icons/material/OpenInFull';
import { PopoverInput } from '../common/PopoverInput';
import { OptionChip } from './OptionChip';
import { DataInput } from './DataInput';

const rowHeight = 32;

const CustomListItem = ({
  onClick,
  setDndHoverState,
  properties,
  titleProperty,
  item,
  index,
  onDrop,
  onDeleteRow,
  onInsertRow,
  onDuplicateRow,
  laneId,
  style,
  cellHandlerWidth,
  dropIndicator,
  showItemDivider,
  onlyTitle,
  titleEditable,
  inputState,
  setInputState,
  currentCellRef,
  setSelectedCell,
  selectedCell,
  navigateCell
}) => {
  const { id, title, metadata } = item;
  const intl = useIntl();

  const dropRef = React.useRef(null)
  const dragRef = React.useRef(null)

  const [{ opacity }, drag, preview] = useDrag(() => ({
    type: 'ROW',
    collect: (monitor) => ({
      opacity: monitor.isDragging() ? 0.4 : 1,
    }),
    item: () => {
      return { index, laneId, id }
    },
  }))

  const [, drop] = useDrop({
    accept: 'ROW',
    hover(item, monitor) {
      if (!dropRef.current) {
        return
      }
      const dragIndex = item.index
      const dragLaneId = item.laneId
      const hoverIndex = index
      const hoverLaneId = laneId
      // Don't replace items with themselves
      // if (dragIndex === hoverIndex && dragLaneId === hoverLaneId) {
      //   return
      // }
      // Determine rectangle on screen
      const hoverBoundingRect = dropRef.current.getBoundingClientRect()
      // Get vertical middle
      const hoverMiddleY =
        (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2
      // Determine mouse position
      const clientOffset = monitor.getClientOffset()
      // Get pixels to the top
      const hoverClientY = clientOffset.y - hoverBoundingRect.top
      // Only perform the move when the mouse has crossed half of the items height
      // When dragging downwards, only move when the cursor is below 50%
      // When dragging upwards, only move when the cursor is above 50%
      // Dragging downwards
      // if (dragLaneId === hoverLaneId && dragIndex < hoverIndex && hoverClientY < hoverMiddleY) {
      //   return
      // }
      // // Dragging upwards
      // if (dragLaneId === hoverLaneId && dragIndex > hoverIndex && hoverClientY > hoverMiddleY) {
      //   return
      // }
      // Time to actually perform the action
      setDndHoverState({
        cardId: item.id, sourceLaneId: dragLaneId, targetLaneId: hoverLaneId, hoverIndex,
        position: id === 'add' ? hoverIndex : (hoverIndex + ((hoverClientY > hoverMiddleY) ? 1 : 0)),
        hoverIndex,
        isBottom: id === 'add' ? false : hoverClientY > hoverMiddleY,
      })
      // Note: we're mutating the monitor item here!
      // Generally it's better to avoid mutations,
      // but it's good here for the sake of performance
      // to avoid expensive index searches.
      item.index = hoverIndex
    },
    drop(item, monitor) {
      if (!dropRef.current) {
        return
      }

      const didDrop = monitor.didDrop();
      if (didDrop) {
        return;
      }

      onDrop && onDrop(item);
    },
  })

  preview(drop(dropRef))
  drag(dragRef)

  const [hovered, setHovered] = React.useState(false);

  let titleContent = <CellView
    property={titleProperty}
    value={title || intl.formatMessage({ id: 'newpage' })}
    hasDocAttached={metadata.hasDocAttached}
    alwaysShowDocIcon={true}
    style={{
      maxWidth: 500,
      minWidth: 180,
    }}
  />;

  const isSelected = selectedCell && laneId === selectedCell.laneId && selectedCell.rowIndex === index;

  let titleElement = !titleEditable ? titleContent
    : <div
      tabIndex="0"
      className='focusable_cell'
      style={{
        display: 'flex',
        height: rowHeight,
        fontSize: '14px',
        alignItems: 'center',
        width: '100%',
      }}
      ref={isSelected ? currentCellRef : null}
      onKeyDown={(e) => {
        if (e.key === 'ArrowDown' || e.key === 'ArrowUp' || e.key === 'ArrowLeft' || e.key === 'ArrowRight') {
          e.preventDefault();
          e.stopPropagation();
          navigateCell(laneId, selectedCell.rowIndex, e.key === 'ArrowDown' && 'down' || e.key === 'ArrowUp' && 'up' || e.key === 'ArrowLeft' && 'left' || e.key === 'ArrowRight' && 'right');
          return;
        }

        if (e.key === 'Enter') {
          e.preventDefault();
          e.stopPropagation();
        }

        setInputState({
          anchorEl: e.currentTarget,
          property: titleProperty,
          value: title,
          cell: {
            rowId: index,
            columnId: titleProperty.name
          }
        });
      }}
      onClick={(e) => {
        if (!inputState || !inputState.anchorEl) {
          setInputState({
            anchorEl: e.currentTarget,
            property: titleProperty,
            value: title || '',
            cell: {
              rowId: index,
              columnId: titleProperty.name
            }
          });
        }

        if (!isSelected) {
          setSelectedCell({
            rowIndex: index,
            laneId: laneId,
          });
        }
      }}

      onBlur={(e) => {
        if (inputState && inputState.anchorEl) {
          return;
        }

        if (isSelected) {
          setSelectedCell(null);
          return
        }
      }}
    >
      {titleContent}

    </div>

  let bodyElements = null;
  if (!onlyTitle && properties && metadata && metadata.data) {
    bodyElements = properties.map(p => {
      if (p.type === 'Title' || !metadata.data[p.name]) {
        return null;
      }

      return <CellView
        key={p.name}
        property={p}
        value={metadata.data[p.name]}
        hasDocAttached={metadata.hasDocAttached}
        style={{
          whiteSpace: 'nowrap',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          maxWidth: 500,
        }}
      />
    }).filter(e => e);
  }

  return (
    <div
      ref={dropRef}
      style={{
        ...style,
        opacity,
      }}
    >
      {
        id === 'add' ?
          <div
            style={{
              marginLeft: cellHandlerWidth,
              width: '-webkit-fill-available',

              borderBottom: dropIndicator === 'bottom' ? '1px solid dodgerblue' : 'none',
              borderTop: dropIndicator === 'top' ? '1px solid dodgerblue' : 'none',
              height: rowHeight
            }}
          >
            <div
              className='hoverStand'
              style={{
                ...styles.addButton,
                padding: '6px 4px',
                marginLeft: 9,
              }}

              onClick={() => {
                onInsertRow();
              }}
            >
              <Plus size={20} style={{ marginRight: 3 }} />
              {intl.formatMessage({ id: 'new_row' })}
            </div>

          </div>

          : <div
            ref={dragRef}
            onClick={titleEditable ? null : () => onClick(id)}
            style={{
              backgroundColor: hovered ? '#f5f5f5' : '#fff',
              cursor: 'pointer',
              width: '-webkit-fill-available',
              borderRadius: '3px',
              paddingRight: '4px',
              minHeight: '32px',
              display: 'flex',
              flexDirection: 'row',
              alignItems: 'center',
            }}

            onMouseEnter={() => setHovered(true)}
            onMouseLeave={() => setHovered(false)}
          >

            <div
              style={{
                width: cellHandlerWidth,
                minWidth: cellHandlerWidth,
                position: 'relative',
                marginRight: '8px',
                paddingLeft: '8px',
                display: 'flex',
                alignItems: 'center',
                flexDirection: 'row',
              }}
            >
              {
                hovered &&
                <div
                  className='add_db_data_handler'
                  style={{
                    color: 'rgba(55, 53, 47, 0.3)',
                    backgroundColor: 'transparent',
                  }}
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    onInsertRow(laneId, id);
                  }}
                >
                  <Plus size={22} />
                </div>
              }
              {
                hovered &&
                <div
                  className='drag_db_data_handler'
                >
                  <DataMenu
                    onClose={() => setHovered(null)}
                    onDelete={() => {
                      onDeleteRow(id);
                    }}
                    onDuplicate={() => {
                      onDuplicateRow(id);
                    }}
                  >
                    <DragIndicator
                      size={22}
                      style={{
                        color: 'rgba(55, 53, 47, 0.3)',
                        backgroundColor: 'transparent',
                      }}
                    />
                  </DataMenu>
                </div>
              }
            </div>

            <div
              style={{
                display: 'flex',
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'space-between',
                height: rowHeight,
                flex: 1,
                borderBottom: dropIndicator === 'bottom' ? '1px solid dodgerblue' : (showItemDivider ? '1px solid #eee' : 'none'),
                borderTop: dropIndicator === 'top' ? '1px solid dodgerblue' : 'none',
                position: 'relative',
              }}
            >
              {titleElement}
              {
                titleEditable && hovered &&
                <div className='open_db_data_button'
                  onClick={(event) => {
                    event.stopPropagation();
                    event.preventDefault();
                    onClick(id);
                  }}

                  style={{
                    left: 180,
                  }}
                >
                  <OpenInFull
                    style={{
                      width: '11px',
                      height: '11px',
                    }}
                  />

                  {intl.formatMessage({ id: 'open' })}
                </div>
              }
              {
                bodyElements && bodyElements.length > 0 &&
                <div
                  style={{
                    display: 'flex',
                    flexDirection: 'row',
                    alignItems: 'center',
                    columnGap: '12px',
                    fontSize: '0.7rem',
                  }}>
                  {
                    bodyElements
                  }
                </div>
              }
            </div>
          </div>
      }
    </div>
  )
}

const CustomList = (props) => {
  const intl = useIntl();
  // const [collapes, setCollapses] = React.useState([]);

  const { style, data, viewProperties, titleProperty, groupByProperty, dndHoverState, setDndHoverState, handleDragEnd,
    onDeleteRow, onInsertRow, onDuplicateRow, onUpdateLaneLabel, onItemClick, showItemDivider, noExtraPaddingForGroupDivider,
    onlyTitle, titleEditable, inputState, setInputState, collapses, setCollapses,
    currentCellRef, setSelectedCell, selectedCell, navigateCell, mode, members, groupByOptions } = props;

  const [showHiddenGroups, setShowHiddenGroups] = React.useState(false);

  const cellHandlerWidth = mode === 'embed' ? 'max(50% - 400px, 0px)' : '44px';

  const laneRender = (lane, options) => {
    const option = (options || []).find(o => o.value === lane.id);
    if (!option) {
      return null;
    }

    return <div
      key={lane.id}
      style={{
        width: '-webkit-fill-available',
        paddingTop: noExtraPaddingForGroupDivider ? 0 : 10,
      }}
    >
      <div
        style={{
          paddingLeft: cellHandlerWidth,
          fontSize: '14px',
          display: 'flex',
          flexDirection: 'row',
          alignItems: 'center',
          height: rowHeight,
          paddingBottom: noExtraPaddingForGroupDivider ? (collapses.includes(lane.id) ? 0 : 1) : 10,
        }}
      >
        <div
          className='hoverStand'
          style={{
            padding: '4px',
          }}
          onClick={() => {
            setCollapses(prev => {
              if (prev.includes(lane.id)) {
                return prev.filter(id => id !== lane.id);
              } else {
                return [...prev, lane.id];
              }
            })
          }}
        >
          {
            collapses.includes(lane.id) ?
              <TriangleRight size={18} /> :
              <TriangleDown size={18} />
          }
        </div>
        {lane.id === nonTagId && <Inbox size={18} style={{ marginRight: 3 }} />}
        {lane.id === nonTagId && lane.title}
        {
          lane.id !== nonTagId && ['Select', 'MultiSelect'].includes(groupByProperty.type) &&
          <PopoverInput
            value={lane.title}
            onSubmit={(value) => {
              onUpdateLaneLabel(lane.id, value);
            }}
          >
            <div
              className='hoverStand'
              style={{
                padding: '4px',
              }}
            >
              <OptionChip
                style={{ fontSize: 14 }}
                option={option}
              />
            </div>
          </PopoverInput>
        }
        {
          lane.id !== nonTagId && groupByProperty.type === 'Person' &&
          <CellView property={groupByProperty} value={[option.value]} members={members} />
        }
        <span style={{ color: 'gray', padding: '4px' }}>{lane.cards && (lane.cards.length - 1)}</span>
      </div>
      <Collapse in={!collapses.includes(lane.id)} timeout="auto" unmountOnExit>
        <CustomList
          {...props}
          data={lane}

          style={{
            width: '100%',
          }} />
      </Collapse>
      <Divider style={{
        marginLeft: '56px',
        paddingTop: 0,
        paddingBottom: 0,
      }} />
    </div>
  }

  if (!data) return <div />;

  return <div style={{ ...style, width: '100%' }}>
    {
      data.cards && data.cards.length > 0 &&
      <List style={{
        width: '-webkit-fill-available',
        paddingTop: 0,
        paddingBottom: 0,
      }}>
        {data.cards.map((card, index) => {
          return (
            <CustomListItem
              key={card.id}
              item={card}
              index={index}
              laneId={data.id}
              properties={viewProperties}
              titleProperty={titleProperty}
              setDndHoverState={setDndHoverState}
              onDrop={() => {
                handleDragEnd({ ...dndHoverState });
                setDndHoverState(null);
              }}
              onDeleteRow={onDeleteRow}
              onInsertRow={onInsertRow}
              onDuplicateRow={onDuplicateRow}
              onClick={onItemClick}
              dropIndicator={dndHoverState && dndHoverState.targetLaneId === data.id && index === dndHoverState.hoverIndex && (dndHoverState.isBottom ? 'bottom' : 'top')}
              showItemDivider={showItemDivider}
              onlyTitle={onlyTitle}
              titleEditable={titleEditable}
              inputState={inputState}
              setInputState={setInputState}
              currentCellRef={currentCellRef}
              setSelectedCell={setSelectedCell}
              selectedCell={selectedCell}
              navigateCell={navigateCell}
              cellHandlerWidth={cellHandlerWidth}
            />
          )
        })}
      </List>
    }
    {
      data.lanes && data.lanes.length > 0 &&
      data.lanes.map(lane => laneRender(lane, groupByOptions))
    }

    {
      data.hiddenLanes && data.hiddenLanes.length > 0 &&
      <div
        className='hoverStand'
        style={{
          display: 'flex',
          flexDirection: 'row',
          alignItems: 'center',
          marginLeft: '54px',
          width: 'max-content',
          borderRadius: '4px',
          marginTop: '4px',
          marginBottom: '4px',
          fontSize: '14px',
          color: '#555',
          cursor: 'pointer',
        }}

        onClick={() => {
          setShowHiddenGroups(!showHiddenGroups);
        }}
      >
        {data.hiddenLanes.length + ' '}
        {intl.formatMessage({ id: 'groups_hidden' })}
        {
          showHiddenGroups &&
          <KeyboardArrowUp size={20} style={{ marginLeft: '5px' }} />
        }
        {
          !showHiddenGroups &&
          <KeyboardArrowDown size={20} style={{ marginLeft: '5px' }} />
        }
      </div>
    }
    {
      showHiddenGroups &&
      data.hiddenLanes.map(lane => laneRender(lane, groupByProperty.options))
    }
  </div>
}



const styles = {
  addButton: {
    width: '100%',
    borderRadius: '3px',
    // margin: '2px',
    marginRight: '4px',
    marginLeft: '2px',
    color: 'gray',
  }
}



const DBList = ({ view, listData, setListData, showItemDivider, listWidth, noExtraPaddingForGroupDivider,
  onlyTitle, titleEditable,
  collapses, setCollapses, setTitlePropertyId, setProperties, mode }) => {
  const intl = useIntl();
  const dispatch = useDispatch();
  const pageBy = 'orderFactor';

  const hid = view.dataSource;
  const doc = useSelector(state => state.docs.byId[hid]);
  const dbdata_list = useSelector(state => getState(state.dbdata_lists, hid));
  const view_dataorder_list = useSelector(state => getState(state.dbview_dataorder_lists, view._id));

  const loginUser = useSelector(state => state.loginIn.user);
  const orgs = useSelector(state => getStateByUser(state.org_lists, loginUser));
  const workingSpace = orgs.items.find(org => org._id === loginUser.workingOrgId) || { users: [] };
  const members = workingSpace.users.filter(user => !!user.user).map(user => user.user);

  const sortSettingsState = useSelector(state => state.viewSorts.byId[view._id]) || {};
  const filterSettingsState = useSelector(state => state.viewFilters.byId[view._id]) || {};

  const [filledAdancedDbData, setFilledAdancedDbData] = useState([]);
  const [filteredDbData, setFilteredDbData] = React.useState([]);
  const [filledDbData, setFilledDbData] = React.useState([]);
  const [dbdata, setDbdata] = React.useState([]);

  const [viewProperties, setViewProperties] = React.useState([]);
  useEffect(() => {
    if (doc && doc.meta && doc.meta.properties) {
      let viewProperties = getViewProperties(doc.meta.properties, view).filter(p => !p.hide);

      setViewProperties(viewProperties);
      setProperties && setProperties(viewProperties);
    }
  }, [doc, view]);

  useEffect(() => {
    if (!dbdata_list || !dbdata_list.items || !viewProperties || viewProperties.length === 0) {
      return;
    }

    let advancedData = fillAdvancedDbData(dbdata_list.items, viewProperties);
    setFilledAdancedDbData(advancedData);

  }, [dbdata_list, viewProperties]);

  useEffect(() => {
    if (!filledAdancedDbData) {
      return;
    }

    let dbdata = filledAdancedDbData;

    if (filterSettingsState.filterGroup && filterSettingsState.filterGroup.filters && filterSettingsState.filterGroup.filters.length > 0) {
      dbdata = filterDbData(dbdata, filterSettingsState.filterGroup, viewProperties)
    }

    setFilteredDbData(dbdata);
  }, [filledAdancedDbData, filterSettingsState, viewProperties]);

  useEffect(() => {
    setFilledDbData(fillDbData(filteredDbData, viewProperties, members));
  }, [filteredDbData]);

  useEffect(() => {
    if (!doc || !filledDbData) {
      return;
    }

    let dbdata = sortDbData(filteredDbData, filledDbData, sortSettingsState, doc.meta && doc.meta.properties, view_dataorder_list.items);
    setDbdata(dbdata);
  }, [doc && doc.meta, sortSettingsState.sorts, filledDbData, view_dataorder_list]);

  const [groupByProperty, setGroupByProperty] = React.useState(null);
  const [groupByOptions, setGroupByOptions] = useState([]);
  const titleProperty = doc && doc.meta && doc.meta.properties.find(p => p.type === 'Title');

  useEffect(() => {
    if (titleProperty && setTitlePropertyId) {
      setTitlePropertyId(titleProperty.name);
    }
  }, [titleProperty]);

  useEffect(() => {
    if (!doc || !doc.meta || !doc.meta.properties) {
      return;
    }

    let groupByProperty = doc.meta.properties.find(p => p.name === view.groupBy);

    if (groupByProperty && ['Select', 'MultiSelect', 'Person'].includes(groupByProperty.type)) {
      if (!groupByProperty.options) {
        groupByProperty.options = [];
      }

      setGroupByProperty(groupByProperty);
    } else {
      setGroupByProperty(null);
    }
  }, [doc, view.groupBy])

  useEffect(() => {
    if (!groupByProperty) {
      return;
    }

    setGroupByOptions(getGroupByOptions(intl, doc, view, dbdata_list.items));
  }, [groupByProperty, doc, dbdata_list.items, view])

  useEffect(() => {
    if (!dbdata) {
      return;
    }

    if (!groupByProperty) {
      let cards = dbdata.map(d => {
        const data = d.data || {};
        return {
          id: d._id,
          title: data[titleProperty.name],
          orderFactor: d.orderFactor,

          metadata: {
            data,
            hasDocAttached: !!d.doc
          }
        }
      });
      cards.push({
        id: 'add',
        metadata: {}
      });

      setListData({
        cards,
      })
      return;
    }

    const lanes = groupByOptions.map(o => {
      let cards;
      if (o.value == nonTagId) {
        cards = dbdata.filter(d => {
          return !d.data || !d.data[view.groupBy] || d.data[view.groupBy].length === 0
            || !groupByOptions.map(o => o.value).filter(value => value === d.data[view.groupBy] || d.data[view.groupBy].includes(value)).length;
        })
      } else {
        cards = dbdata.filter(d => {
          if (!d.data || !d.data[view.groupBy]) {
            return false;
          }

          return groupByProperty.type === 'Select' && d.data[view.groupBy] === o.value
            || ['MultiSelect', 'Person'].includes(groupByProperty.type) && d.data[view.groupBy].includes(o.value)
        })
      }

      cards = cards.map(d => {
        return {
          id: d._id,
          title: d.data ? d.data[titleProperty.name] : '',
          orderFactor: d.orderFactor,

          metadata: {
            data: d.data,
            hasDocAttached: !!d.doc
          }
        }
      });

      cards.push({
        id: 'add',
        metadata: {}
      });

      return {
        id: o.value,
        hide: o.hide,
        title: o.label,
        // label: '0/0',
        cards
      };
    });

    setListData({
      lanes: lanes.filter(o => !o.hide) || [],
      hiddenLanes: lanes.filter(o => o.hide) || [],
    });
  }, [dbdata, groupByProperty, groupByOptions, titleProperty, view, intl]);

  const onCardClick = useCallback((cardId) => {
    dispatch({
      type: DATA_EDITOR_DIALOG,
      value: {
        dataSourceHid: hid,
        dataId: cardId,
        visible: true,
      }
    })
  }, [hid]);


  const [dndHoverState, setDndHoverState] = React.useState(null);

  const handleDragStart = useCallback((cardId, laneId) => {
    if (sortSettingsState && sortSettingsState.sorts && sortSettingsState.sorts.length > 0) {
      confirmToRemoveSortSettings(dispatch, view._id, intl);
      return;
    }
  }, [sortSettingsState]);

  const handleDragEnd = useCallback(({ cardId, sourceLaneId, targetLaneId, position, isUp }) => {
    const data = dbdata.find(d => d._id === cardId);
    if (!data) {
      return;
    }

    if (sortSettingsState && sortSettingsState.sorts && sortSettingsState.sorts.length > 0) {
      return;
    }

    const targetCards = (!targetLaneId ? listData : (listData.lanes.find(l => l.id === targetLaneId) || listData.hiddenLanes.find(l => l.id === targetLaneId)))?.cards.filter(c => c.id !== 'add');
    const draggedCards = (!sourceLaneId ? listData : (listData.lanes.find(l => l.id === sourceLaneId) || listData.hiddenLanes.find(l => l.id === sourceLaneId))).cards;
    const draggedCardIndex = draggedCards.findIndex(c => c.id === cardId);
    const draggedCard = draggedCards[draggedCardIndex];

    if (sourceLaneId === targetLaneId && (position === draggedCardIndex || !isUp && position === draggedCardIndex + 1)) {
      return
    }

    let newOrderFactor = draggedCard.orderFactor;

    if (targetCards && targetCards.length > 0) {
      if (position === 0) {
        if (targetCards[0].orderFactor >= draggedCard.orderFactor) {
          newOrderFactor = targetCards[0].orderFactor + 10;
        }
      } else if (position > 0 && position < targetCards.length) {
        let upCard = targetCards[position - 1];
        let downCard = targetCards[position];

        if (upCard.orderFactor <= draggedCard.orderFactor || downCard.orderFactor >= draggedCard.orderFactor) {
          newOrderFactor = (upCard.orderFactor + downCard.orderFactor) / 2;
        }
      } else {
        if (targetCards[targetCards.length - 1].orderFactor < draggedCard.orderFactor) {
          newOrderFactor = targetCards[targetCards.length - 1].orderFactor - 10;
        }
      }
    } else {
      newOrderFactor = 0;
    }

    if (newOrderFactor !== draggedCard.orderFactor) {
      dispatch(upsertViewDataOrder({
        viewId: view._id,
        dataId: cardId,
        data: {
          orderFactor: newOrderFactor,
        }
      }));
    }

    if (sourceLaneId === targetLaneId || !targetLaneId) {
      return;
    }

    data.data = data.data || {};
    if (groupByProperty.type === 'Select') {
      data.data[view.groupBy] = targetLaneId === nonTagId ? '' : targetLaneId;
    } else {
      if (sourceLaneId === nonTagId) {
        data.data[view.groupBy] = [targetLaneId];
      } else if (targetLaneId === nonTagId) {
        data.data[view.groupBy] = [];
      } else {
        let currentValue = data.data[view.groupBy];

        currentValue = currentValue.filter(v => v !== sourceLaneId);
        currentValue.push(targetLaneId);

        data.data[view.groupBy] = currentValue;
      }
    }

    updateRowData(dispatch, view.dataSource, data);
  }, [dbdata, listData, groupByProperty, view, sortSettingsState]);

  // const onInsertRow = (laneId, dataId) => {
  //   if (sortSettingsState && sortSettingsState.sorts && sortSettingsState.sorts.length > 0) {
  //     confirmToRemoveSortSettings(dispatch, view._id, intl);
  //     return;
  //   }

  //   const rowIndex = dataId ? dbdata.findIndex(d => d._id === dataId) : (dbdata_list.items.length - 1);
  //   addDBRow(dispatch, hid, dbdata,
  //     view.groupBy ? {
  //       [view.groupBy]: laneId === nonTagId ? '' : (groupByProperty.type === 'Select' ? laneId : [laneId]),
  //     } : null,
  //     rowIndex + 1);
  // }

  const onUpdateLaneLabel = (laneId, label) => {
    const newOptions = [...groupByProperty.options];
    const option = newOptions.find(o => o.value === laneId);
    const newOption = { ...option, label };
    newOptions[newOptions.indexOf(option)] = newOption;

    updateDBProperty(dispatch, hid, doc.meta, {
      ...groupByProperty,
      options: newOptions,
    });
  }

  const addLane = (laneName) => {
    if (!laneName) {
      return;
    }

    let property = cloneDeep(groupByProperty);
    if (addOptionToProperty(laneName, property)) {
      updateDBProperty(dispatch, hid, doc.meta, property);
    }
  }

  const [inputState, setInputState] = useState({
    anchorEl: null,
    property: null,
    value: null,
  });
  const [selectedCell, setSelectedCell] = React.useState(null);
  const currentCellRef = useRef(null);

  useEffect(() => {
    if (currentCellRef && currentCellRef.current) {
      currentCellRef.current.focus();
    }
  }, [currentCellRef && currentCellRef.current, selectedCell]);


  const updateCellData = (rowIndex, columnId, value) => {
    if ((!value && (!dbdata[rowIndex].data || !dbdata[rowIndex].data[columnId])) || dbdata[rowIndex].data && (value == dbdata[rowIndex].data[columnId])) {
      return;
    }

    dispatch(updateDbData({
      hid,
      pageBy,
      data: {
        _id: dbdata[rowIndex]._id,
        data: {
          ...dbdata[rowIndex].data,
          [columnId]: value
        }
      }
    }, () => { }, 'update'));
  }

  const navigateCell = (laneId, rowIndex, direction) => {
    const totalRows = laneId && listData.lanes ? (listData.lanes.find(l => l.id === laneId).cards.length - 1) : dbdata.length;

    if (direction === 'left' || direction === 'down') {
      rowIndex = (rowIndex + 1) % totalRows;
    } else if (direction === 'right' || direction === 'up') {
      rowIndex = (rowIndex - 1 + totalRows) % totalRows;
    }

    setSelectedCell({ rowIndex, laneId });
  }


  if (!listData) {
    return <></>;
  }

  return (
    <div style={{
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      width: listWidth || '-webkit-fill-available',
    }}>
      <CustomList
        data={listData}
        viewProperties={viewProperties}
        titleProperty={titleProperty}
        groupByProperty={groupByProperty}
        dndHoverState={dndHoverState}
        members={members}
        groupByOptions={groupByOptions}
        setDndHoverState={setDndHoverState}
        handleDragEnd={handleDragEnd}
        onDeleteRow={(dataId) => onDeleteRow(hid, dataId, dispatch)}
        onInsertRow={(laneId, dataId) => onInsertRow(hid, view, groupByProperty, dbdata, laneId, dataId, dbdata_list.items.length, sortSettingsState, dispatch, intl)}
        onDuplicateRow={(dataId) => onDuplicateRow(hid, view, dbdata, dataId, sortSettingsState, dispatch, intl)}
        onUpdateLaneLabel={onUpdateLaneLabel}
        onItemClick={onCardClick}
        showItemDivider={showItemDivider}
        noExtraPaddingForGroupDivider={noExtraPaddingForGroupDivider}
        onlyTitle={onlyTitle}
        titleEditable={titleEditable}
        inputState={inputState}
        setInputState={setInputState}
        collapses={collapses}
        setCollapses={setCollapses}
        currentCellRef={currentCellRef}
        setSelectedCell={setSelectedCell}
        selectedCell={selectedCell}
        navigateCell={navigateCell}
        mode={mode}
      />
      {
        groupByProperty &&
        groupByProperty.type !== 'Person' &&
        <div
          style={{
            backgroundColor: '#fcfcfc',
            borderRadius: '4px',
            margin: '4px 0px',
            alignSelf: 'flex-start',
          }}
        >
          <PopoverInput
            onSubmit={(value) => {
              addLane(value);
            }}
          >
            <div
              className='hoverStand'
              style={{
                ...styles.addButton,
                marginLeft: '55px',
                width: 160,
                padding: '4px 0px',
                marginBottom: 40
              }}
            >
              <Plus size={20} style={{ marginRight: 3 }} />
              {intl.formatMessage({ id: 'add_option_group' })}
            </div>
          </PopoverInput>
        </div>
      }
      {
        titleEditable &&
        <DataInput
          style={{
            width: listWidth - 54,
          }}
          state={inputState}
          // updateProperty={(property) => {
          //   updateDBProperty(dispatch, hid, doc.meta, property);
          // }}

          closeAndSaveValue={(value) => {
            updateCellData(inputState.cell.rowId, inputState.cell.columnId, value);
            setInputState({ anchorEl: null })
            currentCellRef && currentCellRef.current && currentCellRef.current.focus();
          }}

          navigateCell={(direction) => {
            navigateCell(selectedCell.laneId, selectedCell.rowIndex, direction);
          }}

        />
      }

    </div>
  )
}

export default DBList;
