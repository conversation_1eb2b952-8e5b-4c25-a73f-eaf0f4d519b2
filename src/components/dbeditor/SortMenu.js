import { Plus } from '@styled-icons/boxicons-regular/Plus';
import { useEffect, useState, useCallback } from 'react';
import { useIntl } from 'react-intl';
import { Menu } from '@mui/material';
import { useDispatch, useSelector } from 'react-redux';

import { getViewProperties } from './DBUtil';
import { ArrowSort } from '@styled-icons/fluentui-system-filled/';

import { VIEW_SORT_ACTIONS } from 'src/constants/actionTypes';
import { DB_PROPERTY_TYPES } from 'src/constants/constants';
import { ViewPropertiesMenu } from './ViewPropertiesMenu';

export const SortMenu = ({ view }) => {
    const intl = useIntl();
    const dispatch = useDispatch();
    const dialogState = useSelector(state => state.viewSorts.byId[view._id]) || {};
    const [anchorEl, setAnchorEl] = useState(null);
    const [properties, setProperties] = useState([]);

    const doc = useSelector(state => state.docs.byId[view.dataSource]);

    const { sorts = [] } = dialogState;

    const handleClose = () => {
        setAnchorEl(null);
    }

    useEffect(() => {
        if (!doc || !doc.meta || !doc.meta.properties) return;

        setProperties(getViewProperties(doc.meta.properties, view).filter(p => !p.hide));
    }, [doc, view]);

    const handleItemClick = (property) => {
        setAnchorEl(null);
        dispatch({
            type: VIEW_SORT_ACTIONS.updated,
            item: {
                viewId: view._id,
                sorts: [{
                    name: property.name,
                    order: 'asc'
                }]
            }
        });
    }

    let style = {
        ...styles.button,
    }

    if (sorts.length > 0) {
        style.color = 'dodgerblue';
    }

    return (<>
        <div className='hoverStand'
            style={style}
            onClick={(e) => {
                if (sorts.length > 0) {
                    dispatch({
                        type: VIEW_SORT_ACTIONS.updated,
                        item: {
                            ...dialogState,
                            visible: !dialogState.visible,
                        }
                    })
                } else {
                    setAnchorEl(e.currentTarget);
                }
            }}
        >
            <ArrowSort size={15} style={styles.icon} />
            {
                intl.formatMessage({ id: 'sort' })
            }
        </div>
        <ViewPropertiesMenu
            anchorEl={anchorEl}
            onClose={handleClose}
            onChange={handleItemClick}
            properties={properties}
        />
    </>
    )
}


const styles = {
    menuItem: {
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    menuContent: {
        display: 'flex',
        width: '130px',
        margin: '0px',
        flexDirection: 'row',
        justifyContent: 'flex-start',
        alignContent: 'center',
        alignItems: 'center'
    },

    button: {
        display: 'flex',
        width: 'max-content',
        margin: '0px',
        flexDirection: 'row',
        justifyContent: 'flex-start',
        alignContent: 'center',
        alignItems: 'center',
        cursor: 'pointer',
        color: 'gray',
        fontSize: '14px',
    },

    icon: {
        marginRight: 4
    }
}