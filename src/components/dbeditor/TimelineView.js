import React, { useCallback, useEffect, useState, useRef } from 'react'

import { useDispatch, useSelector } from 'react-redux';
import { useIntl } from 'react-intl';
import { updateRowData, addDBRow } from './DBUtil';
import { getState, getStateByUser } from 'src/reducers/listReducer';

import { Gantt } from "../../gantt-timeline"

import moment from 'moment'
import { updateDbView, addDefaultDatePropertyToDB } from 'src/actions/ticketAction';

const headerHeight = 68;
const listLeftPadding = 56;

const TimelineView = ({ view, settingsContainerHeight, mode }) => {
  const intl = useIntl();
  const dispatch = useDispatch();

  const hid = view.dataSource;
  const doc = useSelector(state => state.docs.byId[hid]);
  const dbdata_list = useSelector(state => getState(state.dbdata_lists, hid));

  const dbdata = dbdata_list.items;

  const containerRef = useRef(null);
  const [lineBy, setLineBy] = React.useState(null);
  const [groupByProperty, setGroupByProperty] = React.useState(null);
  const [containerHeight, setContainerHeight] = React.useState(0);

  useEffect(() => {
    if (!doc || !doc.meta || !doc.meta.properties) {
      return;
    }

    let groupByProperty = doc.meta.properties.find(p => p.name === view.groupBy);

    if (groupByProperty && (groupByProperty.type === 'Select' || groupByProperty.type === 'MultiSelect')) {
      if (!groupByProperty.options) {
        groupByProperty.options = [];
      }

      setGroupByProperty(groupByProperty);
    } else {
      setGroupByProperty(null);
    }

    let useTimePair = view.timeline && view.timeline.useTimePair;
    let range = !useTimePair && doc.meta.properties.find(p => p.name === (view.timeline && view.timeline.range));
    let start = useTimePair && doc.meta.properties.find(p => p.name === (view.timeline && view.timeline.start));
    let end = useTimePair && doc.meta.properties.find(p => p.name === (view.timeline && view.timeline.end));

    if (!useTimePair && !range) {
      const selectableDateProperties = doc.meta.properties.filter(p => p.type === 'Date');
      const selectableProperties = doc.meta.properties.filter(p => p.type === 'Date' && p.hasEndDate);

      if (!selectableDateProperties || selectableDateProperties.length == 0) {
        dispatch(addDefaultDatePropertyToDB({
          hid,
        }));
      } else if (selectableProperties && selectableProperties.length > 0) {
        dispatch(updateDbView({
          viewId: view._id,
          hid: view.hid,
          pageBy: 'orderFactor',
          data: {
            timeline: {
              range: selectableProperties[0].name,
            }
          }
        }));
      }

      return;
    }

    setLineBy({
      useTimePair,
      range,
      start,
      end,
    });
  }, [doc, view.timeline, view.groupBy]);


  const setTimeProperty = (start, end) => {
    if (!lineBy || (!lineBy.useTimePair && !lineBy.range) || (lineBy.useTimePair && (!lineBy.start || !lineBy.end))) {
      dispatch({
        type: 'CONFIRM_DIALOG', value: {
          visible: true,
          handleConfirm: () => {

          },
          content: intl.formatMessage({ id: 'info_set_timeline_content' }),
          title: intl.formatMessage({ id: 'info_set_timeline_title' }),
        }
      })
      return;
    }

    let data = {};

    if (lineBy.useTimePair) {
      data[lineBy.start.name] = start;
      data[lineBy.end.name] = end;
    } else {
      data[lineBy.range.name] = [start, end];
    }

    return data;
  }

  const handleCreateTask = useCallback((task) => {
    let data = setTimeProperty(task.start, task.end);
    if (!data) {
      return;
    }

    if (groupByProperty && task.group) {
      if (groupByProperty.type === 'MultiSelect') {
        data[groupByProperty.name] = [task.group];
      } else {
        data[groupByProperty.name] = task.group;
      }
    }

    addDBRow(dispatch, hid, dbdata, data);
  }, [dispatch, hid, dbdata,  groupByProperty, lineBy]);

  const handleTaskChange = useCallback((task) => {
    if (!task || !task.id) {
      return;
    }

    let { start, end, id } = task;

    const data = dbdata.find(d => d._id === id);

    if (!data) {
      return;
    }

    let dateData = setTimeProperty(start, end);
    if (!dateData) {
      return;
    }

    let newData = { ...data };
    newData.data = {
      ...newData.data,
      ...dateData,
    };

    updateRowData(dispatch, view.dataSource, newData);

  }, [dbdata, lineBy, view.dataSource]);


  useEffect(() => {
    if (containerRef.current) {
      setContainerHeight(containerRef.current.clientHeight - (settingsContainerHeight > 20 ? (settingsContainerHeight - 10) : 0));
    }
  }, [containerRef, settingsContainerHeight]);

  return (
    <div
      ref={containerRef}
      style={{
        minWidth: '800px',
        width: '-webkit-fill-available',
        height: '100%',
      }}
    >
      <Gantt
        view={view}
        onDateChange={handleTaskChange}
        onCreateTask={handleCreateTask}
        marginLeft={38}
        ganttHeight={containerHeight - headerHeight - (mode === 'embed' ? 18 : 0)}
        listLeftPadding={listLeftPadding}
        headerHeight={headerHeight}
      />
    </div>
  )
}

export default TimelineView;
