import { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { TabPanel, a11yProps } from '../tab/TabPanel';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import { Alert } from "@mui/material";
import { useIntl } from 'react-intl';
import { fetchDbData, fetchDbViews, getDoc, setViewDataSource } from 'src/actions/ticketAction';

import TableView from './TableView';
import BoardView from './BoardView';
import ListView from './ListView';
import GalleryView from './GalleryView';
import { AddViewMenu } from './AddViewMenu';
import { ViewTabMenu } from './ViewTabMenu';
import { GroupByMenu } from './GroupByMenu';
import { PropertiesMenu } from './PropertiesMenu';
import { DOCS_FOLDER, VIEW_LAYOUTS } from 'src/constants/constants';
import { SortMenu } from './SortMenu';
import { SortSettings } from './SortSettings';
import { FilterMenu } from './FilterMenu';
import { FilterSettings } from './FilterSettings';
import { Divider } from '@mui/material';
import TimelineView from './TimelineView';
import { TimelineSettingMenu } from './TimelineSettingMenu';
import { PageChooser } from '../PageChooser';
import { TitleEditor } from '../common/TitleEditor';
import { useHistory } from 'react-router-dom';
import RestoreDocButton from '../RestoreDocButton';
import ChartView from './ChartView';
import { ChartSettingMenu } from './ChartSettingMenu';
import { getState } from 'src/reducers/listReducer';

const BlankView = ({ hid, view }) => {
    const dispatch = useDispatch();
    const [showPageChooser, setShowPageChooser] = useState(false);

    return (<div style={{
        width: '100%',
        height: 400,
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        position: 'relative'
    }}>
        <div
            className='hoverStand'
            onClick={() => {
                setShowPageChooser(true);
            }}
        >
            Choose data source
        </div>

        {
            showPageChooser &&
            <div style={{
                position: 'absolute',
                right: 0,
                top: 0,
                width: 300,
                height: '-webkit-fill-available',
                backgroundColor: '#fff',
                border: '1px solid #ccc',
            }}>
                <PageChooser
                    accepts={['db']}
                    onSelect={(item) => {
                        setShowPageChooser(false);

                        dispatch(setViewDataSource({
                            hid,
                            viewId: view._id,
                            dataSource: item.hid,
                        }))
                    }}
                    onClose={() => {
                        setShowPageChooser(false);
                    }}
                />
            </div>
        }
    </div>);
}

const DBEditorFrame = ({ hid, mode }) => {
    const dispatch = useDispatch();
    const intl = useIntl();
    const docs = useSelector(state => state.docs);
    const views = useSelector(state => getState(state.dbview_lists, hid)).items;
    const history = useHistory();

    const [tab, setTab] = useState(0);
    const [view, setView] = useState(null);
    const [doc, setDoc] = useState(null);
    const [dsDoc, setDsDoc] = useState(null);
    const pageBy = 'orderFactor';

    const handleChange = (event, newValue) => {
        setTab(newValue);
    };

    useEffect(() => {
        setTab(0);
    }, [hid]);

    useEffect(() => {
        if (hid) {
            dispatch(getDoc({ hid: hid }, (doc) => {
                if (!doc) {
                    return;
                }

                dispatch(fetchDbViews({ hid, pageBy }));
            }, null, 'editor'));
        }
    }, [dispatch, hid]);

    useEffect(() => {
        if (hid) {
            setDoc(docs.byId[hid]);
        }
    }, [hid, docs.byId[hid]]);

    const onlyUnique = useCallback((value, index, self) => {
        return self.indexOf(value) === index;
    }, []);

    useEffect(() => {
        if (views && views.length > 0) {
            views.map(view => view.dataSource)
                .filter(onlyUnique)
                .forEach(
                    dataSource => {
                        if (!dataSource) return;
                        dataSource != hid && dispatch(getDoc({ hid: dataSource }, null, null, 'dbeditorframe'));

                        dispatch(fetchDbData({ hid: dataSource, pageBy }));
                    }
                );
        }
    }, [views?.length > 0 && views.map(v => v.dataSource).join(',')]);

    useEffect(() => {
        if (views) {
            setView(views[tab]);
        }
    }, [tab, JSON.stringify(views)]);

    const [tabMenuState, setTabMenuState] = useState({
        anchorEl: null,
        view: null
    });

    const sortSettingsState = useSelector(state => state.viewSorts.byId[view && view._id]) || {};
    const filterSettingsState = useSelector(state => state.viewFilters.byId[view && view._id]) || {};

    useEffect(() => {
        if (tabMenuState.view) {
            setTabMenuState({ ...tabMenuState, view: views.find(view => view._id === tabMenuState.view._id) });
        }
    }, [JSON.stringify(views)]);

    const containerRef = useRef(null);
    const settingsContainerRef = useRef(null);
    const [settingsContainerHeight, setSettingsContainerHeight] = useState(0);

    useEffect(() => {
        if (settingsContainerRef.current) {
            if (sortSettingsState.sorts && sortSettingsState.sorts.length > 0 || filterSettingsState.filters && sortSettingsState.filters.length > 0) {
                setSettingsContainerHeight(settingsContainerRef.current.clientHeight);
            } else {
                setSettingsContainerHeight(0);
            }
        }
    }, [JSON.stringify(sortSettingsState), JSON.stringify(filterSettingsState)]);

    useEffect(() => {
        if (!containerRef.current || !view) {
            return;
        }

        if (view.layout === 'timeline') {
            containerRef.current.style.overflow = 'hidden';
        } else {
            containerRef.current.style.overflow = 'auto';
        }
    }, [view?.layout]);

    const [containerSize, setContainerSize] = useState({});
    useEffect(() => {
        if (containerRef.current) {
            setContainerSize({
                width: containerRef.current.clientWidth,
                height: containerRef.current.clientHeight
            });
        }
    }, [containerRef.current && containerRef.current.clientWidth]);

    const [viewDSTitle, setViewDSTitle] = useState('');
    useEffect(() => {
        if (view && view.dataSource && docs) {
            let doc = docs.byId[view.dataSource];
            if (doc) {
                setViewDSTitle(doc.title);
            }

            setDsDoc(doc);
        }
    }, [JSON.stringify(docs), view?.dataSource]);

    const left = 'max(50% - 400px, 0px)';
    const containerPaddingLeft = useMemo(() => Math.max(containerSize.width / 2 - 400, 0), [containerSize.width]);

    // const tableView = useMemo(() => {
    //     return <TableView view={view} mode={mode} containerPaddingLeft={containerPaddingLeft} />
    // }, [JSON.stringify(view), mode])

    if (!dsDoc) {
        return <div></div>;
    }

    // console.log('db editor frame........................')

    return (
        <div
            ref={containerRef}
            style={{
                minWidth: '600px',
                width: '-webkit-fill-available',
                position: 'relative',
                display: 'flex',
                flexDirection: 'column',
                zIndex: 1,
                flexGrow: 1,
                overflow: 'auto'
            }}

            id="dbEditorContainer"
        >
            {
                hid && !doc &&
                <div style={{
                    position: 'sticky',
                    width: 'fit-content',
                    left: mode === 'embed' ? left : '0px'
                }}>
                    <Alert variant="filled" severity="warning">
                        {intl.formatMessage({ id: 'doc_not_found' })}
                    </Alert>
                </div>
            }
            {
                docs.byId[hid] && docs.byId[hid].folder === DOCS_FOLDER.trashbin &&
                <div style={{
                    position: 'sticky',
                    width: 'fit-content',
                    left: mode === 'embed' ? left : '0px'
                }}>
                    <Alert variant="filled" severity="warning"
                        action={
                            <RestoreDocButton item={{ hid, parent: docs.byId[hid].parent }} />
                        }
                    >
                        {intl.formatMessage({ id: 'doc_in_trashbin' })}
                    </Alert>
                </div>
            }
            {
                doc &&
                <div
                    className={mode === 'embed' ? 'slate-dbviews-embed' : null}
                    style={{
                        display: 'flex',
                        flexDirection: 'column',
                        alignSelf: 'center',
                        flexShrink: 0,
                        zIndex: 99,
                        position: 'sticky',
                        width: '-webkit-fill-available',
                        left: mode === 'embed' ? left : '0px'
                    }}>

                    {
                        mode !== 'embed' &&
                        <TitleEditor
                            title={doc?.title}
                            doc={{ hid, title: doc?.title }}
                            containerStyle={{
                                maxWidth: '-webkit-fill-available',
                            }}
                        />
                    }

                    <div style={{
                        display: 'flex',
                        flexDirection: 'row',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        borderBottom: 1,
                        borderColor: 'divider',
                        paddingLeft: mode === 'embed' ? 0 : '54px',
                        paddingRight: 20
                    }}>
                        <div style={{
                            display: 'flex',
                            flexDirection: 'row',
                            alignItems: 'center',
                            maxWidth: '600px'
                        }}
                        >
                            <Tabs
                                style={{
                                    minHeight: '30px',
                                    height: '36px',
                                    width: '-webkit-fill-available',
                                    display: 'flex-inline',
                                }}
                                value={tab}
                                onChange={handleChange}
                                variant="scrollable"
                                scrollButtons="auto"
                            >
                                {views.map((view, index) => {
                                    if (index > 4) {
                                        return null;
                                    }
                                    return (
                                        <Tab
                                            style={{
                                                minHeight: '30px',
                                                height: '36px',
                                                fontSize: '14px',
                                                padding: '0px',
                                            }}
                                            key={view._id}
                                            size="small"
                                            label={
                                                <div
                                                    style={{
                                                        display: 'flex',
                                                        flexDirection: 'row',
                                                        alignItems: 'center',
                                                        alignSelf: 'flex-start',
                                                    }}
                                                >
                                                    {
                                                        VIEW_LAYOUTS.find(layout => layout.name === view.layout).icon
                                                    }
                                                    {view.name}
                                                </div>
                                            }
                                            {...a11yProps(index)}

                                            onClick={(e) => {
                                                if (tab === index) {
                                                    console.log('click same tab');
                                                    setTabMenuState({
                                                        anchorEl: e.currentTarget,
                                                        view
                                                    })
                                                }
                                            }}
                                        />
                                    )
                                })}
                            </Tabs>
                            {/* {
                            views.length > 5 && (
                                

                        } */}
                            <AddViewMenu hid={hid} dataSource={doc?.isDataSource ? hid : null} views={views} viewAdded={() => {
                                setTimeout(() => {
                                    setTab(views.length);
                                }, 100);
                            }} />
                        </div>
                        {
                            tab != null && view && view.dataSource &&

                            <div style={{
                                display: 'flex',
                                flexDirection: 'row',
                                justifyContent: 'center',
                            }}>
                                {
                                    view.layout === 'timeline' &&
                                    <TimelineSettingMenu doc={dsDoc} view={view} />
                                }
                                {
                                    view.layout === 'chart' &&
                                    <ChartSettingMenu doc={dsDoc} view={view} />
                                }
                                {
                                    ['board', 'list', 'gallery', 'timeline', 'chart'].includes(view.layout) &&
                                    <GroupByMenu
                                        doc={dsDoc}
                                        view={view}
                                    />
                                }
                                {
                                    view.layout !== 'chart' &&
                                    <PropertiesMenu
                                        doc={dsDoc}
                                        view={view}
                                    />
                                }
                                {
                                    view.layout !== 'chart' &&
                                    <SortMenu
                                        doc={dsDoc}
                                        view={view}
                                    />
                                }
                                <FilterMenu
                                    doc={dsDoc}
                                    view={view}
                                />
                            </div>
                        }
                    </div>

                    <div
                        ref={settingsContainerRef}
                        style={{
                            display: 'flex',
                            flexDirection: 'column',
                            marginLeft: mode === 'embed' ? 0 : '54px',
                        }}
                    >
                        {
                            mode === 'embed' && view && hid === view.dataSource &&
                            <TitleEditor
                                title={viewDSTitle}
                                doc={dsDoc}
                                containerStyle={{
                                    marginLeft: 0,
                                    paddingTop: '8px',
                                    paddingBottom: '0px',
                                    paddingLeft: '0px',
                                }}

                                inputStyle={{
                                    color: '#555',
                                    fontSize: '1em',
                                    fontWeight: 'bold',
                                }}
                            />
                        }
                        {
                            view && view.dataSource !== doc.hid &&
                            <div className='hoverStand'
                                style={{
                                    marginTop: 4,
                                    padding: 2,
                                    paddingLeft: 4,
                                    paddingRight: 4,
                                    borderRadius: '4px',
                                    fontSize: '1em',
                                    fontWeight: 'bold',
                                    color: '#555',
                                    width: 'fit-content',
                                }}

                                onClick={() => {
                                    history.push(`/db?hid=${view.dataSource}`);
                                }}
                            >
                                {viewDSTitle}
                            </div>
                        }
                        <div
                            style={{
                                paddingTop: 8,
                            }}
                        >
                            {
                                (sortSettingsState.sorts && sortSettingsState.sorts.length > 0 ||
                                    filterSettingsState.filterGroup && filterSettingsState.filterGroup.type) &&

                                <div
                                    style={{
                                        display: 'flex',
                                        flexDirection: 'row',
                                        alignItems: 'center',
                                        marginBottom: 10
                                    }}
                                >
                                    {
                                        sortSettingsState.sorts && sortSettingsState.sorts.length > 0 &&
                                        <div style={{
                                            display: 'flex',
                                            flexDirection: 'row',
                                            justifyContent: 'flex-start',
                                        }}>

                                            <SortSettings doc={dsDoc} view={view} />
                                        </div>
                                    }

                                    {
                                        sortSettingsState.sorts && sortSettingsState.sorts.length > 0 &&
                                        filterSettingsState.filterGroup && filterSettingsState.filterGroup.type &&
                                        <div style={{
                                            marginLeft: '12px',
                                            marginRight: '12px',
                                            height: '20px',
                                            width: '2px',
                                            backgroundColor: '#eee',
                                        }} />
                                    }
                                    {
                                        filterSettingsState.filterGroup && filterSettingsState.filterGroup.type &&
                                        <div style={{
                                            display: 'flex',
                                            flexDirection: 'row',
                                            justifyContent: 'flex-start',
                                        }}>

                                            <FilterSettings doc={dsDoc} view={view} />
                                        </div>
                                    }
                                </div>
                            }
                        </div>
                    </div>
                </div>
            }

            {
                views.map((view, index) => {
                    if (!doc) {
                        return null;
                    }

                    let panel = null;

                    if (!view.dataSource) {
                        panel = <BlankView view={view} hid={hid} />;
                    } else {
                        if (view.layout === 'table') {
                            panel = <TableView view={view} mode={mode} containerPaddingLeft={containerPaddingLeft} />
                            // panel = tableView
                        } else if (view.layout === 'board') {
                            panel = <BoardView view={view} mode={mode} />
                        } else if (view.layout === 'list') {
                            panel = <ListView view={view} mode={mode} />
                        } else if (view.layout === 'gallery') {
                            panel = <GalleryView view={view} mode={mode} />
                        } else if (view.layout === 'timeline') {
                            panel = <TimelineView view={view} mode={mode}
                                settingsContainerHeight={settingsContainerHeight}
                            />
                        } else if (view.layout === 'chart') {
                            panel = <ChartView view={view} mode={mode} />
                        }
                    }

                    return (
                        <TabPanel
                            style={{
                                height: '-webkit-fill-available',
                            }}
                            value={tab} index={index} key={view._id}>
                            {panel}
                        </TabPanel>
                    )
                })
            }
            <ViewTabMenu
                onClose={() => {
                    setTabMenuState({
                        anchorEl: null,
                        view: null
                    });
                }}
                state={tabMenuState}
                showDeleteBtn={views.length > 1}
            />
        </div>
    )
}

export default DBEditorFrame;