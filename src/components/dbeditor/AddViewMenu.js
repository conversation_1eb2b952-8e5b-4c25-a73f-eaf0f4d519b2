import { Plus } from '@styled-icons/boxicons-regular/Plus';
import { useState } from 'react';
import { useIntl } from 'react-intl';
import { Divider, FormControl, ListItemText, Menu, MenuItem, Select, Switch, TextField, IconButton, Button, Popover, InputLabel, Chip } from '@mui/material';
import { Check } from '@styled-icons/material-outlined';
import { useDispatch } from 'react-redux';
import { createDbView } from 'src/actions/ticketAction';
import { Kanban } from '@styled-icons/bootstrap/Kanban';
import { Table } from '@styled-icons/bootstrap/Table';
import { VIEW_LAYOUTS } from 'src/constants/constants';

export const AddViewMenu = ({ hid, dataSource, views, viewAdded }) => {
    const intl = useIntl();
    const dispatch = useDispatch();
    const [anchorEl, setAnchorEl] = useState(null);
    const [name, setName] = useState('');
    const [layout, setLayout] = useState('board');

    const handleClose = () => {
        setAnchorEl(null);
    }

    const createNewView = () => {
        let smallestOrderFactor = (!views || views.length === 0) ? 0 : views[views.length - 1].orderFactor;
        dispatch(createDbView({ hid, pageBy: 'orderFactor', data: { name, layout, dataSource, orderFactor: smallestOrderFactor - 10 } }, () => {
            handleClose();
            viewAdded && viewAdded();
        }, 'createView'));
    }

    return (<>
        <div className='hoverStand'
            style={styles.button}
            onClick={(e) => {
                setAnchorEl(e.currentTarget);
            }}
        >
            <Plus size={18} style={styles.icon} />
            {
                intl.formatMessage({ id: 'add_view' })
            }
        </div>
        <Menu
            id="basic-menu"
            anchorEl={anchorEl}
            open={Boolean(anchorEl)}
            onClose={handleClose}
            MenuListProps={{
                'aria-labelledby': 'basic-button',
            }}
        >
            <div style={{ margin: '10px 16px' }}>
                <input
                    type="text"
                    style={{ border: '1px solid #ccc', outline: 'none', width: '-webkit-fill-available', borderRadius: 4, padding: 5 }}
                    placeholder={intl.formatMessage({ id: 'view_name_placeholder' })}
                    value={name}
                    autoFocus={true}
                    onChange={(e) => {
                        setName(e.target.value);
                    }}
                />
            </div>

            {
                VIEW_LAYOUTS.map(l => {
                    return <MenuItem
                        key={l.name}
                        style={styles.menuItem}
                        onClick={() => {
                            setLayout(l.name);
                        }}>
                        <div style={styles.menuContent}>
                            {l.icon}
                            {
                                intl.formatMessage({ id: l.name })
                            }
                        </div>

                        {layout === l.name && <Check size={18} style={styles.icon} />}
                    </MenuItem>
                })
            }

            <div style={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
            }}>
                <Button
                    variant='contained'
                    size='small'
                    onClick={() => {
                        createNewView();
                    }}
                    style={{
                        width: 160,
                        marginTop: 10,
                        marginBottom: 10,
                    }}
                >
                    {intl.formatMessage({ id: 'create_view' })}
                </Button>
            </div>

        </Menu>
    </>
    )
}


const styles = {
    menuItem: {
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'space-between',
    },
    menuContent: {
        display: 'flex',
        width: '160px',
        margin: '0px',
        flexDirection: 'row',
        justifyContent: 'flex-start',
        alignContent: 'center',
        alignItems: 'center',
        color: '#555',
    },

    button: {
        display: 'flex',
        minWidth: '86px',
        margin: '0px',
        flexDirection: 'row',
        justifyContent: 'flex-start',
        alignContent: 'center',
        alignItems: 'center',
        cursor: 'pointer',
        color: 'gray',
        fontSize: 14,
        whiteSpace: 'nowrap'
    },

    icon: {
        marginRight: 0
    }
}