import { useCallback, useMemo, useEffect, useState } from 'react';
import { useIntl } from 'react-intl';
import { Divider, FormControl, ListItemText, Menu, MenuItem, Select, Switch, TextField, IconButton, Button, Popover, InputLabel, Chip } from '@mui/material';
import { useDispatch, useSelector } from 'react-redux';
import { ArrowLeft } from '@styled-icons/fluentui-system-regular/ArrowLeft';
import { updateDocProperties, insertColumn, deleteColumn, moveColumn, updateDBProperty, addOptionToProperty } from './DBUtil';
import { ArrowRight } from '@styled-icons/fluentui-system-regular/ArrowRight';
import { KeyboardReturn } from '@styled-icons/material/KeyboardReturn';
import { Close } from '@styled-icons/material/Close';
import { Trash } from '@styled-icons/bootstrap/Trash';
import { Plus } from '@styled-icons/boxicons-regular/Plus';
import { DB_PROPERTY_TYPES, getDefaultRuleOp, OPTION_BG_COLORS } from 'src/constants/constants';
import { OptionList } from './OptionList';
import cloneDeep from 'lodash/cloneDeep';
import { Visibility } from '@styled-icons/material-outlined/Visibility';
import { ArrowDown } from '@styled-icons/bootstrap/ArrowDown';
import { ArrowUp } from '@styled-icons/bootstrap/ArrowUp';
import { VIEW_FILTER_ACTIONS, VIEW_SORT_ACTIONS } from 'src/constants/actionTypes';
import { Filter } from '@styled-icons/fluentui-system-filled/Filter';
import { Selector } from '../common/Selector';

export default function PropertyMenu({ columnId, hid, viewId, showPlace, onHideProperty, style, trigger }) {
    const dispatch = useDispatch();
    const intl = useIntl();
    const [anchorEl, setAnchorEl] = useState(null);
    const [showOptionInput, setShowOptionInput] = useState(false);
    const [optionInput, setOptionInput] = useState('');

    const doc = useSelector(state => state.docs.byId[hid]);
    const [properties, setProperties] = useState([]);
    const [property, setProperty] = useState(null);
    const [columnIndex, setColumnIndex] = useState(0);

    const viewFilterState = useSelector(state => state.viewFilters.byId[viewId]) || {};

    useEffect(() => {
        if (!doc || !doc.meta || !doc.meta.properties) return;

        setProperty(cloneDeep(doc.meta.properties.find(p => p.name === columnId)));
        setProperties(doc.meta.properties);
        setColumnIndex(doc.meta.properties.findIndex(p => p.name === columnId));
    }, [doc, columnId]);

    const handleClick = (event) => {
        setAnchorEl(event.currentTarget);
        event.preventDefault();
        event.stopPropagation();
    };

    const handleClose = () => {
        updateDBProperty(dispatch, doc.hid, doc.meta, property);

        setAnchorEl(null);
    };

    const open = Boolean(anchorEl);

    const handleDeleteProperty = () => {
        dispatch({
            type: 'CONFIRM_DIALOG', value: {
                visible: true,
                handleConfirm: () => {
                    deleteColumn(dispatch, doc.hid, doc.meta, properties.findIndex(p => p.name == property.name))
                },
                content: intl.formatMessage({ id: 'confirm_delete_property' }, { label: property.label }),
            }
        })
    };

    const newOption = useCallback((label) => {
        if (addOptionToProperty(label, property)) {
            setProperty(property);
            setOptionInput('');
        }
    }, [property, setProperty]);

    const onSort = useCallback((name, order) => {
        dispatch({
            type: VIEW_SORT_ACTIONS.updated,
            item: {
                viewId,
                sorts: [{
                    name,
                    order
                }]
            }
        });
    }, [dispatch, viewId]);

    const onFilter = useCallback((name) => {
        const op = getDefaultRuleOp(property.type);

        if (viewFilterState.filterGroup && viewFilterState.filterGroup.type) {
            const newFilters = viewFilterState.filterGroup.filters;
            newFilters.push({ property: name, op, value: null, type: 'rule' });

            return dispatch({
                type: VIEW_FILTER_ACTIONS.updated,
                item: {
                    ...viewFilterState,
                    filterGroup: {
                        ...viewFilterState.filterGroup,
                        filters: newFilters
                    },

                    visible: true,
                }
            });
        }

        dispatch({
            type: VIEW_FILTER_ACTIONS.updated,
            item: {
                viewId,
                filterGroup: {
                    type: 'group',
                    op: 'and',
                    filters: [{
                        type: 'rule',
                        op,
                        property: name,
                        value: null
                    }]
                }
            }
        });

        setTimeout(() => {
            dispatch({
                type: VIEW_FILTER_ACTIONS.updated,
                item: {
                    viewId,
                    visible: true,
                    filterGroup: {
                        type: 'group',
                        op: 'and',
                        filters: [{
                            type: 'rule',
                            op,
                            property: name,
                            value: null
                        }]
                    }
                }
            });
        }, 100);
    }, [dispatch, viewId, property, viewFilterState]);


    if (!property) return null;

    return (
        <div style={Object.assign({ margin: 0 }, style)}>
            <div
                className='hoverStand db_table_header'
                onClick={handleClick}>
                {trigger}
                {!trigger && DB_PROPERTY_TYPES.find(t => property.advancedType ? property.advancedType === t.advancedType : t.value === property.type).icon}
                {!trigger && property.label}
            </div>

            <Menu
                id="basic-menu"
                anchorEl={anchorEl}
                open={open}
                onClose={handleClose}
                MenuListProps={{
                    'aria-labelledby': 'basic-button',
                }}
            >
                <div style={{ margin: '10px 16px' }}>
                    <input
                        type="text"
                        style={{ border: '1px solid #ccc', outline: 'none', width: '-webkit-fill-available', borderRadius: 4, padding: 5 }}
                        value={property.label}
                        onFocus={(e) => {
                            e.target.select();
                        }}
                        onChange={(e) => {
                            setProperty({ ...property, label: e.target.value });
                        }}
                    />
                </div>
                <FormControl
                    variant="standard"
                    sx={{ m: 1, minWidth: 120, marginLeft: 2, width: '156px' }}
                >
                    <InputLabel id="demo-simple-select-standard-label"> <span
                        style={{
                            textSize: 12,
                            color: 'grey',
                        }}
                    >
                        {
                            intl.formatMessage({ id: 'property_type' })
                        }
                    </span></InputLabel>
                    <Select
                        labelId="demo-customized-select-label"
                        id="demo-customized-select"
                        value={property.advancedType ? property.type + '_' + property.advancedType : property.type}
                        disabled={property.type === 'Title' || property.advancedType == 'tags'}
                        onChange={(event) => {
                            let value = event.target.value;
                            const [type, advancedType] = value.split('_');

                            if (type === 'Date') {
                                setProperty({ ...property, type, advancedType, dateFormat: 'YYYY-MM-DD' });
                            } else if (type === 'Select' || type === 'MultiSelect') {
                                setProperty({ ...property, type, options: [], advancedType });
                            } else {
                                setProperty({ ...property, type, advancedType });
                            }
                        }}
                    >
                        <div
                            style={{
                                color: 'gray',
                                fontSize: 13,
                                marginLeft: 12
                            }}
                        >
                            {
                                intl.formatMessage({ id: 'basic_type' })
                            }
                        </div>
                        {
                            property.type === 'Title' ?
                                <MenuItem value={property.type} dense={true}>{intl.formatMessage({ id: 'type_title' })}</MenuItem> :
                                DB_PROPERTY_TYPES.filter(t => !t.advancedType).map(t => {
                                    if (t.value === 'Title') {
                                        return null;
                                    }

                                    return <MenuItem
                                        key={t.value}
                                        value={t.value}
                                        dense={true}
                                    >
                                        {t.icon} {t.label}
                                    </MenuItem>
                                })
                        }

                        <Divider />

                        <div
                            style={{
                                color: 'gray',
                                fontSize: 13,
                                marginLeft: 12
                            }}
                        >
                            {
                                intl.formatMessage({ id: 'advanced_type' })
                            }
                        </div>
                        {
                            DB_PROPERTY_TYPES.filter(t => !!t.advancedType).map(t => {
                                return <MenuItem
                                    key={t.value + t.advancedType}
                                    value={t.value + '_' + t.advancedType}
                                    disabled={t.disabled}
                                    dense={true}
                                >
                                    {t.icon} {t.label}
                                </MenuItem>
                            })
                        }
                    </Select>
                </FormControl>
                {
                    property.type === 'Date' &&
                    <div style={{ margin: '10px 12px 10px 12px', display: 'flex', flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between' }}>
                        <div style={{
                            fontSize: 12,
                            color: 'grey',
                            marginLeft: 4,
                        }}>
                            {
                                intl.formatMessage({ id: 'has_end_date' })
                            }
                        </div>

                        <Switch
                            checked={!!property.hasEndDate}
                            onChange={(event) => setProperty({ ...property, hasEndDate: event.target.checked })}
                            name="checkedB"
                            size='small'
                        />
                    </div>
                }

                {
                    property.type === 'Date' &&
                    <div style={{ margin: '10px 12px 10px 12px', display: 'flex', flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between' }}>
                        <div style={{
                            fontSize: 12,
                            color: 'grey',
                            marginLeft: 4,
                        }}>
                            {
                                intl.formatMessage({ id: 'has_time' })
                            }
                        </div>

                        <Switch
                            checked={!!property.hasTime}
                            onChange={(event) => setProperty({ ...property, hasTime: event.target.checked })}
                            name="checkedC"
                            size='small'
                        />
                    </div>
                }
                {
                    property.type === 'Date' &&
                    <div style={{ margin: '10px 12px 10px 12px', display: 'flex', flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between' }}>
                        <div style={{
                            fontSize: 12,
                            color: 'grey',
                            marginLeft: 4,
                        }}>
                            {
                                intl.formatMessage({ id: 'date_format' })
                            }
                        </div>
                        <Selector
                            value={property.dateFormat}
                            onChange={(value) => setProperty({ ...property, dateFormat: value })}
                            options={[
                                { value: 'YYYY-MM-DD', label: intl.formatMessage({ id: 'date_format_ymd_dash' }) },
                                { value: 'MM-DD-YYYY', label: intl.formatMessage({ id: 'date_format_mdy_dash' }) },
                                { value: 'DD-MM-YYYY', label: intl.formatMessage({ id: 'date_format_dmy_dash' }) },
                                { value: 'YYYY/MM/DD', label: intl.formatMessage({ id: 'date_format_ymd_slash' }) },
                                { value: 'MM/DD/YYYY', label: intl.formatMessage({ id: 'date_format_mdy_slash' }) },
                                { value: 'DD/MM/YYYY', label: intl.formatMessage({ id: 'date_format_dmy_slash' }) },
                            ]}
                        />
                    </div>
                }


                {
                    (property.type === 'Select' || property.type === 'MultiSelect' && !property.advancedType) &&
                    <div style={{ margin: '10px 12px 0px 12px' }}>
                        <div style={{
                            fontSize: 12,
                            color: 'grey',
                            marginLeft: 4,
                        }}>
                            {
                                intl.formatMessage({ id: 'select_options' })
                            }
                        </div>

                        {
                            property.options &&
                            <OptionList
                                options={property.options}
                                updateOptions={(options) => {
                                    setProperty({ ...property, options });
                                }}
                            />
                        }
                    </div>
                }
                {
                    (property.type === 'Select' || property.type === 'MultiSelect' && !property.advancedType) &&
                    !showOptionInput &&
                    <MenuItem
                        onClick={() => {
                            setShowOptionInput(true);
                        }}
                    >
                        <div style={styles.menuItem}>
                            <Plus size={18} style={styles.icon} />
                            {
                                intl.formatMessage({ id: 'add_option' })
                            }
                        </div>
                    </MenuItem>
                }

                {
                    showOptionInput &&
                    <div
                        style={{
                            display: 'flex',
                            flexDirection: 'row',
                            alignItems: 'center',
                            margin: '4px 16px 8px 16px',
                        }}
                    >
                        <input
                            type="text"
                            style={{
                                border: '1px solid #ccc',
                                outline: 'none',
                                // width: '-webkit-fill-available',
                                borderRadius: 4,
                                padding: 5,
                                marginRight: 4,
                            }}
                            autoFocus={true}
                            placeholder={intl.formatMessage({ id: 'option_input_placeholder' })}
                            value={optionInput}
                            onChange={(e) => {
                                setOptionInput(e.target.value);
                            }}
                            onKeyPress={(e) => {
                                if (e.key === 'Enter' && optionInput) {
                                    newOption(optionInput);
                                }
                            }}
                        />
                        {
                            optionInput &&
                            <KeyboardReturn size={20} style={{ cursor: 'pointer' }} onClick={(e) => {
                                newOption(optionInput);
                            }} />

                        }
                        {
                            !optionInput &&
                            <Close size={20} style={{ cursor: 'pointer' }} onClick={() => setShowOptionInput(false)} />
                        }

                    </div>
                }
                <Divider />

                {
                    ['table_header', 'timeline'].includes(showPlace) &&
                    <MenuItem
                        key={'asc'}
                        onClick={() => {
                            onSort(columnId, 'asc');
                            handleClose();
                        }}
                    >
                        <div style={styles.menuItem}>
                            <ArrowUp size={18} style={styles.icon} />
                            {
                                intl.formatMessage({ id: 'sort_ascending' })
                            }
                        </div>
                    </MenuItem>
                }
                {
                    ['table_header', 'timeline'].includes(showPlace) &&
                    <MenuItem
                        key={'desc'}
                        onClick={() => {
                            onSort(columnId, 'desc');
                            handleClose();
                        }}
                    >
                        <div style={styles.menuItem}>
                            <ArrowDown size={18} style={styles.icon} />
                            {
                                intl.formatMessage({ id: 'sort_descending' })
                            }
                        </div>
                    </MenuItem>
                }
                {
                    ['table_header', 'timeline'].includes(showPlace) &&
                    <MenuItem
                        key={'filter'}
                        onClick={() => {
                            onFilter(columnId);
                            handleClose();
                        }}
                    >
                        <div style={styles.menuItem}>
                            <Filter size={18} style={styles.icon} />
                            {
                                intl.formatMessage({ id: 'filter' })
                            }
                        </div>
                    </MenuItem>
                }

                {
                    ['table_header'].includes(showPlace) &&
                    <div>
                        <Divider style={{ margin: '2px 8px' }} />
                        <MenuItem
                            key={'insert_right'}
                            onClick={() => insertColumn(dispatch, intl, doc.hid, doc.meta, columnIndex + 1)}
                        >
                            <div style={styles.menuItem}>
                                <ArrowRight size={18} style={styles.icon} />
                                {
                                    intl.formatMessage({ id: 'insert_right' })
                                }
                            </div>
                        </MenuItem>

                        <MenuItem
                            key={'insert_left'}
                            onClick={() => insertColumn(dispatch, intl, doc.hid, doc.meta, columnIndex)}
                        >
                            <div style={styles.menuItem}>
                                <ArrowLeft size={18} style={styles.icon} />
                                {
                                    intl.formatMessage({ id: 'insert_left' })
                                }
                            </div>
                        </MenuItem>
                    </div>
                }

                {
                    showPlace === 'table_header' &&
                    property.type !== 'Title' &&
                    <MenuItem
                        key={'hide'}
                        onClick={() => onHideProperty(columnId)}
                    >
                        <div style={styles.menuItem}>
                            <Visibility size={18} style={styles.icon} />
                            {
                                intl.formatMessage({ id: 'hide_property' })
                            }
                        </div>
                    </MenuItem>
                }

                {
                    property.type != 'Title' && property.advancedType !='tags' &&
                    <MenuItem
                        key={'delete'}
                        onClick={handleDeleteProperty}
                    >
                        <div style={styles.menuItem}>
                            <Trash size={18} style={styles.icon} />
                            {
                                intl.formatMessage({ id: 'delete_property' })
                            }
                        </div>
                    </MenuItem>
                }
            </Menu>


        </div>
    );
}

const styles = {
    menuItem: {
        display: 'flex',
        width: '160px',
        margin: '0px',
        flexDirection: 'row',
        justifyContent: 'flex-start',
        alignContent: 'center',
        alignItems: 'center'
    },

    icon: {
        marginRight: 5
    }
}