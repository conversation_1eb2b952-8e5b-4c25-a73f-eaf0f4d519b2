import { useEffect, useState } from 'react';
import { useIntl } from 'react-intl';
import { useSelector } from 'react-redux';
import { OPTION_BG_COLORS } from 'src/constants/constants';
import { getStateByUser } from 'src/reducers/listReducer';
import { MemberChip } from './MemberChip';

export const MemberSelector = ({ value, updateValue, }) => {
    const intl = useIntl();

    const loginUser = useSelector(state => state.loginIn.user);
    const orgs = useSelector(state => getStateByUser(state.org_lists, loginUser));
    const workingSpace = orgs.items.find(org => org._id === loginUser.workingOrgId) || { users: [] };
    const members = workingSpace.users.filter(user => !!user.user).map(user => user.user);

    const [search, setSearch] = useState('');
    const [filteredMembers, setFilteredMembers] = useState(members);
    const [selectedItems, setSelectedItems] = useState([]);
    const [itemTargeted, setItemTargeted] = useState(0);

    useEffect(() => {
        setSelectedItems((!value || typeof value !='object') ? [] : value.map(v => { return members.find(o => o._id === v) }).filter(o => !!o));
    }, [value]);

    useEffect(() => {
        search && setFilteredMembers(members.filter(o => o.nickname.toLowerCase().includes(search.toLowerCase()) || o.username.toLowerCase().includes(search.toLowerCase())) || []);
    }, [search, members]);

    const onKeyPress = (event) => {
        if (event.key === 'Enter') {
            if (itemTargeted < 0 || itemTargeted >= filteredMembers.length) {
                return;
            }

            if (itemTargeted >= 0 && selectedItems.indexOf(filteredMembers[itemTargeted]) === -1) {
                handleItemSelected(filteredMembers[itemTargeted]);

                setItemTargeted(0);
            }

            setSearch("");
            event.preventDefault();
            event.stopPropagation();
        }
    }

    const onKeyDown = (event) => {
         if (event.key === 'Backspace') {
            if (search.length === 0) {
                setSelectedItems(selectedItems.slice(0, selectedItems.length - 1));
            }
        } else if (event.key === 'ArrowDown') {
            if (itemTargeted < filteredMembers.length - 1) {
                setItemTargeted(itemTargeted + 1);
            } else {
                setItemTargeted(0);
            }
        } else if (event.key === 'ArrowUp') {
            if (itemTargeted > 0) {
                setItemTargeted(itemTargeted - 1);
            } else {
                setItemTargeted(filteredMembers.length - 1);
            }
        }
    }

    const handleDelete = (chipToDelete) => () => {
        updateValue(value.filter(v => v !== chipToDelete._id));
    }

    const handleItemSelected = (item) => {
        if (!value) {
            updateValue([item._id]);
        }
        if (value && value.indexOf(item._id) === -1) {
            let newValue = [...value, item._id];
            updateValue(newValue);
        }
    }

    return <div
        style={{
            display: 'flex',
            flexDirection: 'column',
            width: '-webkit-fill-available',
            minWidth: '200px',
            height: 'fit-content',
            padding: '8px',
        }}
    >
        <div style={{ display: 'flex', flexWrap: 'wrap', rowGap: '6px', columnGap: '6px' }}>
            {selectedItems.map((item, index) => (
                <MemberChip
                    key={index}
                    tabIndex={-1}
                    member={item}
                    onDelete={handleDelete(item)}
                />
            ))}
        </div>

        <input
            style={{
                margin: '8px 0px 8px 0px',
                padding: '8px',
                border: '1px solid lightgray',
                borderRadius: '4px',
                outline: 'none',
            }}
            value={search}

            placeholder={intl.formatMessage({ id: 'options_search_placeholder' })}

            onChange={event => {
                event.target.value && event.target.value.trim() ? setSearch(event.target.value.trim()) : setSearch("");
            }}
            onKeyPress={onKeyPress}
            onKeyDown={onKeyDown}
            autoFocus={true}
        />

        <div style={{
            overflowY: 'auto',
            maxHeight: '300px',
        }}>
            {filteredMembers.map((member, index) => {
                return <div
                    key={index}
                    style={{
                        display: 'flex',
                        padding: '2px',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        backgroundColor: index === itemTargeted ? '#eee' : 'transparent', cursor: 'pointer'
                    }}
                    onClick={(e) => {
                        handleItemSelected(member);
                    }}

                    onMouseEnter={() => {
                        setItemTargeted(index);
                    }}

                    onMouseLeave={() => {
                        setItemTargeted(-1);
                    }}
                >
                    <MemberChip
                        member={member}
                    />
                </div>
            })}
        </div>
    </div>
}