import React, { useCallback, useEffect, useState } from 'react'
import ReactDOM from 'react-dom'

import { Plus } from '@styled-icons/boxicons-regular/Plus';
import { MoreHoriz } from '@styled-icons/material';

import { useDispatch, useSelector } from 'react-redux';
import { useIntl } from 'react-intl';
import { Button, Divider } from '@mui/material';
import { Link } from '@styled-icons/material';
import { getState } from 'src/reducers/listReducer';
import DocEditor from '../editor/DocEditor';
import PropertyMenu from './PropertyMenu';
import { DataInput } from './DataInput';
import { CellView } from './CellView';
import { fillAdvancedDbData, insertColumn, updateDBProperties, updateDBProperty, updateRowCellData } from './DBUtil';
import { SortableCard } from '../dndlist/SortableCard';
import update from 'immutability-helper'
import { TitleEditor } from '../common/TitleEditor';
import { getDoc } from 'src/actions/ticketAction';
import { REFRESH_EDITOR_CONTENT } from 'src/constants/actionTypes';
import LoadingScreen from '../LoadingScreen';

const DataEditorCore = ({ dataSourceHid, dataId, toHtml }) => {

  const intl = useIntl();
  const dispatch = useDispatch();

  const [doc, setDoc] = React.useState([]);
  const docs = useSelector(state => state.docs);
  const dbdata = useSelector(state => getState(state.dbdata_lists, dataSourceHid));
  const loginUser = useSelector(state => state.loginIn.user);

  const [data, setData] = React.useState();
  const [docLoading, setDocLoading] = useState(false);

  useEffect(() => {
    if (docs && docs.byId[dataSourceHid]) {
      setDoc(docs.byId[dataSourceHid]);
    }
  }, [dataSourceHid, docs]);

  useEffect(() => {
    if (data?.doc) {
      setDocLoading(true);
      dispatch(getDoc({ hid: data.doc }, (doc) => {
        setDocLoading(false);
        dispatch({ type: REFRESH_EDITOR_CONTENT, value: data.doc });
      }, (doc) => {
        setDocLoading(false);
        dispatch({ type: REFRESH_EDITOR_CONTENT, value: data.doc });
      }, 'data_editor'));
    }
  }, [data?.doc]);

  const [properties, setProperties] = React.useState([]);
  useEffect(() => {
    if (doc && doc.meta && doc.meta.properties) {
      setProperties(doc.meta.properties);
    }
  }, [doc]);

  useEffect(() => {
    if (!dbdata || !dbdata.items || !doc || !doc.meta) {
      return;
    }

    let data = dbdata.items.find(d => d._id === dataId);
    if (!data || !data.data) {
      data = {
        _id: dataId,
        data: {}
      };
    }

    data = fillAdvancedDbData([data], properties)[0];

    setData(data);

    if (data && data.doc) {
      setShowDocEditor({ focus: false });
    } else {
      setShowDocEditor(null);
    }

    if (!properties || properties.length === 0) {
      return;
    }

    const titleProperty = properties.find(p => p.type === 'Title').name;
    setTitleProperty(titleProperty);
    setTitle(data.data[titleProperty]);

  }, [dataId, dbdata, doc, properties]);


  const [titleProperty, setTitleProperty] = React.useState();
  const [title, setTitle] = React.useState('');
  const [showDocEditor, setShowDocEditor] = React.useState();
  const [inputState, setInputState] = React.useState({});
  const [itemTargeted, setItemTargeted] = React.useState(null);

  const onDrop = (item, dropIndex) => {
    updateDBProperties(dispatch, dataSourceHid, doc.meta, properties);
  }

  const onTitleSubmit = () => {
    if (title != data.data[titleProperty]) {
      updateData(titleProperty, title);
    }
  }

  const updateData = useCallback((property, value) => {
    updateRowCellData(dispatch, dataSourceHid, data, property, value);
  }, [dataSourceHid, data]);

  const moveCard = useCallback((dragIndex, hoverIndex) => {
    setProperties((prevCards) =>
      update(prevCards, {
        $splice: [
          [dragIndex, 1],
          [hoverIndex, 0, prevCards[dragIndex]],
        ],
      }),
    )
  }, [])

  return (
    <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', overflowY: 'auto', width: '100%' }}>
      <div style={{ display: 'flex', flexDirection: 'column', width: '100%', maxWidth: '800px', }}>
        <TitleEditor
          title={title}
          doc={{ hid: dataSourceHid }}
          saveTitleExternal={true}
          onChange={setTitle}
          onSubmit={onTitleSubmit}
          containerStyle={{
            marginLeft: 14,
            paddingLeft: 0,
          }}
        />

        <div
          style={{
            display: 'flex',
            flexDirection: 'column',
            width: '-webkit-fit-avaliable',
            paddingLeft: '10px',
            paddingRight: '10px',
          }}
          onDrop={onDrop}
        >
          {properties && data && data.data && properties.map((property, i) => {
            if (property.type === 'Title') {
              return null;
            }

            return (
              <SortableCard
                key={property.name}
                index={i}
                moveCard={moveCard}
              >
                <div
                  style={{
                    display: 'flex',
                    flexDirection: 'row',
                    alignItems: 'center',
                    width: '100%',
                    margin: '0px 0px',
                    color: '#555'
                  }}>
                  <PropertyMenu
                    columnId={property.name}
                    hid={dataSourceHid}
                    style={{ width: '192px' }}
                    onDrop={onDrop}
                    moveCard={moveCard}
                    isHorizontal={false}
                  />
                  <div
                    tabIndex="0"
                    style={{
                      display: 'flex',
                      position: 'relative',
                      minHeight: '36px',
                      fontSize: '14px',
                      alignItems: 'center',
                      padding: '0px 6px',
                      width: '100%',
                      backgroundColor: itemTargeted === i ? '#f5f5f5' : '#fff',
                    }}

                    onClick={(e) => {
                      if (property.type == 'Checkbox') {
                        updateData(property.name, !data.data[property.name]);
                        return;
                      }

                      if ((!inputState || !inputState.anchorEl) && !property.advancedType) {
                        setInputState({
                          anchorEl: e.currentTarget,
                          property,
                          value: data.data[property.name] || '',
                        });
                      }

                    }}

                    onMouseEnter={(e) => {
                      setItemTargeted(i);
                    }}

                    onMouseLeave={(e) => {
                      setItemTargeted(null);
                    }}
                  >
                    <CellView
                      property={property}
                      value={data.data[property.name]}
                      editMode={true}
                      aliginLeft={true}
                    />
                    {
                      data.data[property.name] &&
                      property.type === 'Url' &&
                      i === itemTargeted &&
                      <div className='open_db_data_button'
                        onClick={(e) => {
                          window.open(data.data[property.name])
                          e.preventDefault();
                          e.stopPropagation();
                        }}
                      >
                        <Link
                          style={{
                            width: '18px',
                            height: '18px',
                            marginRight: '3px',
                          }}
                        />

                        {intl.formatMessage({ id: 'open' })}
                      </div>
                    }
                  </div>
                </div>
              </SortableCard>
            )
          }
          )}

          <div className='hoverStand'
            style={{
              borderRadius: '2px',
              paddingLeft: '5px',
              paddingTop: '5px',
              paddingBottom: '5px',
              columnGap: '1px',
              color: 'gray',
            }}
            onClick={() => insertColumn(dispatch, intl, dataSourceHid, doc.meta, doc.meta.properties.length)}
          >
            <Plus size={20} style={{ color: 'gray' }} /> {intl.formatMessage({ id: 'add_option' })}
          </div>
        </div>

        <Divider style={{ margin: '6px 10px' }} />
        {
          data && showDocEditor && !docLoading &&
          <DocEditor
            hid={data.doc}
            dbDataId={dataId}
            forceTitle={false}
            autoFocus={showDocEditor.focus}
            saveOnBlockSwitch={!!data.doc}
            toHtml={toHtml}
          />
        }
        {
          data && showDocEditor && docLoading &&
          <LoadingScreen />
        }
        {
          data && !data.doc && !showDocEditor &&
          <div style={{
            margin: 14,
            maringLeft: 24,
            color: '#999',
          }}
            onClick={() => {
              setShowDocEditor({ focus: true })
            }}
          >
            {
              intl.formatMessage({ id: 'data_editor_new_page_desc' })
            }
          </div>
        }
      </div>
      <DataInput
        state={inputState}
        updateProperty={(property) => {
          updateDBProperty(dispatch, dataSourceHid, doc.meta, property);
        }}

        closeAndSaveValue={(value) => {
          updateData(inputState.property.name, value);
          setInputState({ anchorEl: null })
        }}

      />
    </div>
  )
}


export default DataEditorCore;
