import { TextareaAutosize, Popover } from "@mui/material";
import { cloneDeep } from "lodash";
import { useEffect, useMemo, useState } from "react";
import { CellView } from "./CellView";
import { MemberSelector } from "./MemberSelector";
import { OptionSelector } from "./OptionSelector";
import { DateRange, Calendar } from 'react-date-range';

export const FilterDataInput = ({ value, op, property, updateValue }) => {
    const lineInputStyle = {
        border: 'none',
        outline: 'none',
        borderRadius: '6px',
        margin: '0px 0px 4px 0px',
        padding: '4px 6px',
        margin: '0',
        fontSize: '14px',
    }

    const [anchorEl, setAnchorEl] = useState(null);

    const setValue = (value) => {
        updateValue(value);
    }

    const closeAndSaveValue = (value) => {
        updateValue(value);
        setAnchorEl(null);
    }


    let inputEl;
    if (property.type === 'Select' || property.type === 'MultiSelect') {
        inputEl = <OptionSelector
            value={value}
            enableMulti={property.type === 'MultiSelect'}
            options={property.options || []}
            optionsReadOnly={true}

            selectDone={(value) => {
                closeAndSaveValue(value)
            }}

            updateValue={(value) => {
                setValue(value);
            }}
        />
    } else if (property.type === 'Person') {
        inputEl = <MemberSelector
            value={value}

            updateValue={(value) => {
                setValue(value);
            }}
        />
    } else if (property.type === 'Date') {
        if (op === 'between') {
            inputEl = <DateRange
                editableDateInputs={true}
                onChange={item => {
                    setValue([item.selection.startDate, item.selection.endDate]);
                }}
                showDateDisplay={false}
                moveRangeOnFirstSelection={false}
                ranges={[{
                    startDate: !value ? new Date() : (Array.isArray(value) ? new Date(value[0]) : (new Date(value) !== "Invalid Date" && !isNaN(new Date(value)) ? new Date(value) : new Date())),
                    endDate: !value ? new Date() : (Array.isArray(value) ? new Date(value[1]) : (new Date(value) !== "Invalid Date" && !isNaN(new Date(value)) ? new Date(value) : new Date())),
                    key: 'selection',
                }]}
            />
        } else {
            inputEl = <Calendar
            date={!value ? new Date() : (Array.isArray(value) ? new Date(value[0]) : (new Date(value) !== "Invalid Date" ? new Date(value) : new Date()))}
                onChange={(date) => {
                    setValue(date);
                }}
            />
        }
    } else {
        return <div className=".round-corner-input"
            style={{
                border: '1px solid #ccc',
                borderRadius: '4px',
                minWidth: '160px',
                display: 'flex',
                flexDirection: 'row',
                alignItems: 'center',
            }}
            onClick={(e) => {
                setAnchorEl(e.target);
            }}
        > <input
                style={lineInputStyle}
                type={property.type === 'Number' && 'number'
                    || property.type === 'Date' && 'date'
                    || property.type === 'Email' && 'email'
                    || property.type === 'Phone' && 'phone'
                    || 'text'}
                value={value || ''}
                onChange={(e) => setValue(e.target.value)}

                autoFocus
            />
        </div>
    }

    return <>

        <div className=".round-corner-input"
            style={{
                border: '1px solid #ccc',
                borderRadius: '4px',
                minWidth: '160px',
                display: 'flex',
                flexDirection: 'row',
                alignItems: 'center',
                padding: '4px 2px',
            }}
            onClick={(e) => {
                setAnchorEl(e.target);
            }}
        >
            <CellView
                property={property}
                value={value}
            />
        </div>

        <Popover
            open={Boolean(anchorEl)}
            onClose={() => closeAndSaveValue(value)}
            anchorEl={anchorEl}
            anchorOrigin={{
                vertical: property.type === 'MultiSelect' ? 'bottom' : 'top',
                horizontal: 'left',
            }}
            transformOrigin={{
                vertical: 'top',
                horizontal: 'left',
            }}

        >
            <div style={{ width: 'fit-content', padding: 0 }}>
                {inputEl}
            </div>
        </Popover>
    </>
};