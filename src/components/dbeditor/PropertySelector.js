import { useEffect, useState, useCallback } from 'react';
import { useIntl } from 'react-intl';
import { Menu } from '@mui/material';
import { useDispatch, useSelector } from 'react-redux';


import { ViewPropertiesMenu } from './ViewPropertiesMenu';
import { KeyboardArrowDown } from '@styled-icons/material';

export const PropertySelector = ({ properties, value, onChange }) => {
    const intl = useIntl();
    const dispatch = useDispatch();
    const [anchorEl, setAnchorEl] = useState(null);


    const handleClose = () => {
        setAnchorEl(null);
    }

    const handleItemClick = (property) => {
        setAnchorEl(null);
        onChange(property.name);
    }

    return (<>
        <div className='round-corner-input'
            onClick={(e) => {
                setAnchorEl(e.currentTarget);
            }}
        >
            {
                value ? properties.find(p => p.name === value).label : 'Select'
            }
            {<KeyboardArrowDown size={17} style={{ paddingLeft: 2, marginTop: 1, color: 'gray' }} />}
        </div>
        <ViewPropertiesMenu
            anchorEl={anchorEl}
            onClose={handleClose}
            onChange={handleItemClick}
            properties={properties}
        />
    </>
    )
}


const styles = {
    menuItem: {
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    menuContent: {
        display: 'flex',
        width: '130px',
        margin: '0px',
        flexDirection: 'row',
        justifyContent: 'flex-start',
        alignContent: 'center',
        alignItems: 'center'
    },

    icon: {
        marginRight: 4
    }
}