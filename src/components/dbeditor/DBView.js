import { useState, useEffect, useCallback, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useIntl } from 'react-intl';
import { fetchDbData, getDBView, getDoc, setViewDataSource } from 'src/actions/ticketAction';

import TableView from './TableView';
import BoardView from './BoardView';
import ListView from './ListView';
import GalleryView from './GalleryView';
import { SortSettings } from './SortSettings';
import { FilterSettings } from './FilterSettings';
import { Divider } from '@mui/material';
import TimelineView from './TimelineView';
import { PageChooser } from '../PageChooser';
import { useHistory, useLocation } from 'react-router-dom';
import ChartView from './ChartView';

const BlankView = ({ hid, view }) => {
    const dispatch = useDispatch();
    const [showPageChooser, setShowPageChooser] = useState(false);

    return (<div style={{
        width: '100%',
        height: 400,
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        position: 'relative'
    }}>
        <div
            className='hoverStand'
            onClick={() => {
                setShowPageChooser(true);
            }}
        >
            Choose data source
        </div>

        {
            showPageChooser &&
            <div style={{
                position: 'absolute',
                right: 0,
                top: 0,
                width: 300,
                height: '-webkit-fill-available',
                backgroundColor: '#fff',
                border: '1px solid #ccc',
            }}>
                <PageChooser
                    accepts={['db']}
                    onSelect={(item) => {
                        setShowPageChooser(false);

                        dispatch(setViewDataSource({
                            hid,
                            viewId: view._id,
                            dataSource: item.hid,
                        }))
                    }}
                    onClose={() => {
                        setShowPageChooser(false);
                    }}
                />
            </div>
        }
    </div>);
}

const DBView = (props) => {
    const dispatch = useDispatch();
    const intl = useIntl();
    const docs = useSelector(state => state.docs);
    const views = useSelector(state => state.views);
    const history = useHistory();
    const mode = 'embedToOthers';

    const currentLocation = useLocation();
    const params = new Proxy(new URLSearchParams(currentLocation.search), {
        get: (searchParams, prop) => searchParams.get(prop) || '',
    });
    const viewId = params.id;
    const [view, setView] = useState(null);
    const pageBy = 'orderFactor';

    useEffect(() => {
        dispatch(getDBView({ viewId }));
    }, [viewId]);

    useEffect(() => {
        setView(views.byId[viewId]);
    }, [views]);

    useEffect(() => {
        if (view && view.dataSource) {
            dispatch(getDoc({ hid: view.dataSource }, null, null, 'dbview'));
            dispatch(fetchDbData({ hid: view.dataSource, pageBy }));
        }
    }, [view]);

    const sortSettingsState = useSelector(state => state.viewSorts.byId[viewId]) || {};
    const filterSettingsState = useSelector(state => state.viewFilters.byId[viewId]) || {};

    const containerRef = useRef(null);
    const settingsContainerRef = useRef(null);
    const [settingsContainerHeight, setSettingsContainerHeight] = useState(0);

    useEffect(() => {
        if (settingsContainerRef.current) {
            if (sortSettingsState.sorts && sortSettingsState.sorts.length > 0 || filterSettingsState.filters && sortSettingsState.filters.length > 0) {
                setSettingsContainerHeight(settingsContainerRef.current.clientHeight);
            } else {
                setSettingsContainerHeight(0);
            }
        }
    }, [settingsContainerRef.current, sortSettingsState, filterSettingsState]);

    useEffect(() => {
        if (!containerRef.current || !view) {
            return;
        }

        if (view.layout === 'timeline') {
            containerRef.current.style.overflow = 'hidden';
        } else {
            containerRef.current.style.overflow = 'auto';
        }
    }, [containerRef.current, view]);

    const [containerSize, setContainerSize] = useState({});
    useEffect(() => {
        if (containerRef.current) {
            setContainerSize({
                width: containerRef.current.clientWidth,
                height: containerRef.current.clientHeight
            });
        }
    }, [containerRef.current && containerRef.current.clientWidth]);

    const containerPaddingLeft = Math.max(containerSize.width / 2 - 400, 0);

    if (!view || !docs || !docs.byId[view.dataSource]) {
        return <div></div>;
    }

    let panel = null;

    if (!view.dataSource) {
        panel = <BlankView view={view} hid={view.dataSource} />;
    } else {
        if (view.layout === 'table') {
            panel = <TableView view={view} mode={mode} containerPaddingLeft={containerPaddingLeft} />
        } else if (view.layout === 'board') {
            panel = <BoardView view={view} mode={mode} />
        } else if (view.layout === 'list') {
            panel = <ListView view={view} mode={mode} />
        } else if (view.layout === 'gallery') {
            panel = <GalleryView view={view} mode={mode} />
        } else if (view.layout === 'timeline') {
            panel = <TimelineView view={view} mode={mode}
                settingsContainerHeight={settingsContainerHeight}
            />
        } else if (view.layout === 'chart') {
            panel = <ChartView view={view} mode={mode} />
        }
    }


    return (
        <div
            ref={containerRef}
            style={{
                minWidth: '600px',
                width: '-webkit-fill-available',
                position: 'relative',
                display: 'flex',
                flexDirection: 'column',
                zIndex: 1,
                flexGrow: 1,
                overflow: 'auto'
            }}

            id="dbEditorContainer"
        >
            <div
                className={mode === 'embed' ? 'slate-dbviews-embed' : null}
                style={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignSelf: 'center',
                    flexShrink: 0,
                    zIndex: 99,
                    position: 'sticky',
                    width: '-webkit-fill-available',
                    left: '0px'
                }}>

                <div
                    ref={settingsContainerRef}
                    style={{
                        display: 'flex',
                        flexDirection: 'column',
                        marginLeft: '54px',
                    }}
                >
                    <div className='hoverStand'
                        style={{
                            marginTop: 4,
                            padding: 2,
                            paddingLeft: 4,
                            paddingRight: 4,
                            borderRadius: '4px',
                            fontSize: '1em',
                            fontWeight: 'bold',
                            color: '#555',
                            width: 'fit-content',
                        }}

                        onClick={() => {
                            history.push(`/db?hid=${view.dataSource}`);
                        }}
                    >
                        {view.name}
                    </div>
                    <div
                        style={{
                            paddingTop: 8,
                        }}
                    >
                        {
                            (sortSettingsState.sorts && sortSettingsState.sorts.length > 0 ||
                                filterSettingsState.filterGroup && filterSettingsState.filterGroup.type) &&

                            <div
                                style={{
                                    display: 'flex',
                                    flexDirection: 'row',
                                    alignItems: 'center',
                                    marginBottom: 10
                                }}
                            >
                                {
                                    sortSettingsState.sorts && sortSettingsState.sorts.length > 0 &&
                                    <div style={{
                                        display: 'flex',
                                        flexDirection: 'row',
                                        justifyContent: 'flex-start',
                                    }}>

                                        <SortSettings doc={docs.byId[view.dataSource]} view={view} />
                                    </div>
                                }

                                {
                                    sortSettingsState.sorts && sortSettingsState.sorts.length > 0 &&
                                    filterSettingsState.filterGroup && filterSettingsState.filterGroup.type &&
                                    <div style={{
                                        marginLeft: '12px',
                                        marginRight: '12px',
                                        height: '20px',
                                        width: '2px',
                                        backgroundColor: '#eee',
                                    }} />
                                }
                                {
                                    filterSettingsState.filterGroup && filterSettingsState.filterGroup.type &&
                                    <div style={{
                                        display: 'flex',
                                        flexDirection: 'row',
                                        justifyContent: 'flex-start',
                                    }}>

                                        <FilterSettings doc={docs.byId[view.dataSource]} view={view} />
                                    </div>
                                }
                            </div>
                        }
                    </div>
                </div>
            </div>

            {
                panel
            }

        </div>
    )
}

export default DBView;