import { useEffect, useState } from 'react';
import { useIntl } from 'react-intl';
import { OPTION_BG_COLORS } from 'src/constants/constants';
import { OptionChip } from './OptionChip';

export const OptionSelector = ({ options, value, selectDone, updateValue, enableMulti, updateOptions, optionsReadOnly }) => {
    const intl = useIntl();

    const [search, setSearch] = useState('');
    const [filteredOptions, setFilteredOptions] = useState(options);
    const [selectedItems, setSelectedItems] = useState([]);
    const [itemTargeted, setItemTargeted] = useState(0);

    useEffect(() => {
        if (enableMulti) {
            setSelectedItems(!value ? [] : value.map(v => { return options.find(o => o.value === v) }).filter(o => !!o));
        } else {
            setSelectedItems(!value ? [] : [options.find(o => o.value === value)].filter(o => !!o));
        }
    }, [value]);

    useEffect(() => {
        let textedOptions = options.filter(o => o.label.toLowerCase().includes(search.toLowerCase())) || [];
        if (!optionsReadOnly && search && search.trim() && !options.find(o => o.label.toLowerCase() === search.toLowerCase())) {
            textedOptions.push({
                label: search.trim(),
                type: 'toCreate',
                value: 'o_' + options.length + Math.floor(Math.random() * 1000),
                bgColor: OPTION_BG_COLORS[options.length % OPTION_BG_COLORS.length].value
            });
        }

        setFilteredOptions(textedOptions);
    }, [search, options]);

    const onKeyDown = (event) => {
        if (event.key === 'Enter') {
            if (itemTargeted < 0 || itemTargeted >= filteredOptions.length) {
                return;
            }

            if (itemTargeted >= 0 && selectedItems.indexOf(filteredOptions[itemTargeted]) === -1) {
                if (filteredOptions[itemTargeted].type === 'toCreate') {
                    addNewOption(filteredOptions[itemTargeted]);
                }

                handleItemSelected(filteredOptions[itemTargeted]);

                setItemTargeted(0);
            }

            setSearch("");
            event.preventDefault();
            event.stopPropagation();
        } else if (event.key === 'Backspace') {
            if (search.length === 0) {
                setSelectedItems(selectedItems.slice(0, selectedItems.length - 1));
            }
        } else if (event.key === 'ArrowDown') {
            if (itemTargeted < filteredOptions.length - 1) {
                setItemTargeted(itemTargeted + 1);
            } else {
                setItemTargeted(0);
            }
        } else if (event.key === 'ArrowUp') {
            if (itemTargeted > 0) {
                setItemTargeted(itemTargeted - 1);
            } else {
                setItemTargeted(filteredOptions.length - 1);
            }
        }
    }

    const handleDelete = (chipToDelete) => () => {
        if (!enableMulti) {
            updateValue(null);
        } else {
            updateValue(value.filter(v => v !== chipToDelete.value));
        }
    }

    const handleItemSelected = (item) => {
        if (!enableMulti) {
            selectDone(item.value);
        } else {
            if (!value) {
                updateValue([item.value]);
            }
            if (value && value.indexOf(item.value) === -1) {
                let newValue = [...value, item.value];
                newValue = options.filter(o => newValue.indexOf(o.value) !== -1).map(o => o.value);
                updateValue(newValue);
            }
        }
    }

    const addNewOption = (option) => {
        delete option.type;
        options.push(option);
        updateOptions(options);
    }

    return <div
        style={{
            display: 'flex',
            flexDirection: 'column',
            width: '-webkit-fill-available',
            minWidth: '200px',
            height: 'fit-content',
            padding: '8px',
        }}
    >
        <div style={{ display: 'flex', flexWrap: 'wrap', rowGap: '6px', columnGap: '6px' }}>
            {selectedItems.map((item, index) => (
                <OptionChip
                    key={index}
                    tabIndex={-1}
                    option={item}
                    onDelete={handleDelete(item)}
                />
            ))}
        </div>

        <input
            style={{
                margin: '8px 0px 8px 0px',
                padding: '8px',
                border: '1px solid lightgray',
                borderRadius: '2px',
                outline: 'none',
            }}
            value={search}

            placeholder={intl.formatMessage({ id: 'options_search_placeholder' })}

            onChange={event => {
                event.target.value && event.target.value.trim() ? setSearch(event.target.value.trim()) : setSearch("");
            }}
            onKeyDown={onKeyDown}
            autoFocus={true}
        />

        {filteredOptions.map((option, index) => {
            return <div
                key={index}
                style={{
                    display: 'flex',
                    padding: '4px',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    backgroundColor: index === itemTargeted ? '#eee' : 'transparent', cursor: 'pointer'
                }}
                onClick={(e) => {
                    if (option.type === 'toCreate') {
                        addNewOption(option);
                    }

                    handleItemSelected(option);
                }}

                onMouseEnter={() => {
                    setItemTargeted(index);
                }}

                onMouseLeave={() => {
                    setItemTargeted(-1);
                }}
            >
                <OptionChip
                    option={option}
                />

                {
                    option.type === 'toCreate' &&
                    <div>
                        {
                            intl.formatMessage({ id: 'create' }) + '⏎'
                        }
                    </div>
                }
            </div>
        })}
    </div>
}