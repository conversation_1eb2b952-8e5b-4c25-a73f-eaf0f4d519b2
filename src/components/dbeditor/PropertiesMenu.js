import { Plus } from '@styled-icons/boxicons-regular/Plus';
import { useEffect, useState, useCallback } from 'react';
import { useIntl } from 'react-intl';
import { Divider, FormControl, ListItemText, Menu, MenuItem, Select, Switch, TextField, IconButton, Button, Popover, InputLabel, Chip } from '@mui/material';
import { useDispatch, useSelector } from 'react-redux';
import { createDbView, updateDbView } from 'src/actions/ticketAction';

import { updateDBProperty, nonTagId, getGroupByOptions, addOptionToProperty, getViewProperties, updateDBProperties, insertColumn } from './DBUtil';
import { cloneDeep } from 'lodash';
import { Nut } from '@styled-icons/bootstrap/Nut';
import { PropertyList } from './PropertyList';

export const PropertiesMenu = ({ view }) => {
    const intl = useIntl();
    const dispatch = useDispatch();
    const [anchorEl, setAnchorEl] = useState(null);
    const [properties, setProperties] = useState([]);
    const doc = useSelector(state => state.docs.byId[view.dataSource]);

    const handleClose = () => {
        setAnchorEl(null);
    }

    useEffect(() => {
        if (!doc || !doc.meta || !doc.meta.properties) return;

        setProperties(getViewProperties(doc.meta.properties, view));
    }, [doc, view]);


    const handleUpdateProperties = (properties, action) => {
        dispatch(updateDbView({
            viewId: view._id,
            hid: view.hid,
            pageBy: 'orderFactor',
            data: { properties }
        }));

        if (action === 'moveCard' || action === 'visibility') {
            return;
        }


        updateDBProperties(dispatch, doc.hid, doc.meta, properties.map(p => {
            let newProperty = cloneDeep(p);
            delete newProperty.hide;
            return newProperty;
        }));
    }

    return (<>
        <div className='hoverStand'
            style={styles.button}
            onClick={(e) => {
                setAnchorEl(e.currentTarget);
            }}
        >
            <Nut size={15} style={styles.icon} />
            {
                intl.formatMessage({ id: 'properties_setting' })
            }
        </div>
        <Menu
            id="basic-menu"
            anchorEl={anchorEl}
            open={Boolean(anchorEl)}
            onClose={handleClose}
            MenuListProps={{
                'aria-labelledby': 'basic-button',
            }}
        >

            <div style={{
                margin: '0px 14px 6px 14px',
                fontWeight: 'bold',
                fontSize: 14,
                minWidth: '180px',
                color: '#333'
            }}>
                {intl.formatMessage({ id: 'properties_setting_title' })}
            </div>

            <Divider />

            {
                properties &&
                <div style={{
                    margin: '6px 14px 6px 14px',

                }}>
                    <div style={{
                        fontSize: 13,
                        color: 'gray'
                    }}
                    >
                        {intl.formatMessage({ id: 'all_properties' })}

                    </div>

                    <PropertyList
                        hid={view.dataSource}
                        properties={properties}
                        updateProperties={(properties, action) => {
                            handleUpdateProperties(properties, action);
                        }}
                    />
                </div>
            }

            <Divider />

            <MenuItem
                style={{
                    marginTop: '6px',
                }}
                onClick={() => {
                    insertColumn(dispatch, intl, doc.hid, doc.meta, properties.length)
                }}
            >
                <div style={styles.menuItem}>
                    <Plus size={18} style={styles.icon} />
                    {
                        intl.formatMessage({ id: 'add_property' })
                    }
                </div>
            </MenuItem>

        </Menu>
    </>
    )
}


const styles = {
    menuItem: {
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    menuContent: {
        display: 'flex',
        width: '130px',
        margin: '0px',
        flexDirection: 'row',
        justifyContent: 'flex-start',
        alignContent: 'center',
        alignItems: 'center'
    },

    button: {
        display: 'flex',
        width: 'max-content',
        margin: '0px',
        flexDirection: 'row',
        justifyContent: 'flex-start',
        alignContent: 'center',
        alignItems: 'center',
        cursor: 'pointer',
        color: 'gray',
        fontSize: '14px',
    },

    icon: {
        marginRight: 4
    }
}