import { TextareaAutosize, Popover } from "@mui/material";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import { SORT_SETTINGS_DIALOG, VIEW_FILTER_ACTIONS, VIEW_SORT_ACTIONS } from "src/constants/actionTypes";
import { ArrowUpward } from '@styled-icons/material-outlined/ArrowUpward'
import { ArrowDownward } from '@styled-icons/material-outlined/ArrowDownward'
import { Filter } from '@styled-icons/fluentui-system-filled/';
import { KeyboardArrowDown, MoreHoriz } from '@styled-icons/material';
import { getViewProperties } from "./DBUtil";
import { Close } from '@styled-icons/material/Close';
import { Trash } from '@styled-icons/bootstrap/Trash';
import { Plus } from '@styled-icons/boxicons-regular/Plus';
import { AddToQueue } from '@styled-icons/boxicons-regular/AddToQueue'
import { useIntl } from "react-intl";
import { Divider, FormControl, ListItemText, Menu, MenuItem, Select } from '@mui/material';
import { PropertySelector } from "./PropertySelector";
import { Selector } from "../common/Selector";
import { DB_PROPERTY_TYPES, FILTER_RULE_OP_TYPES, getDefaultRuleOp } from "src/constants/constants";
import { FilterDataInput } from "./FilterDataInput";
import { formatDate } from "src/utils/timeFormater";
import { CellView } from "./CellView";


const FilterGroup = ({ group, onChange, properties, level, handleDeleteAll }) => {
    const intl = useIntl();

    const { op, filters } = group;

    const handleChangeGroup = (index, value) => {
        const newFilters = [...filters];
        newFilters[index] = value;

        onChange({
            ...group,
            filters: newFilters
        });
    }

    const handleChangeRule = (index, obj) => {
        const newFilters = [...filters];
        newFilters[index] = {
            ...newFilters[index],
            ...obj
        }

        onChange({
            ...group,
            filters: newFilters
        });
    }

    const handleAddRule = () => {
        const newFilters = [...filters];
        newFilters.push({
            type: 'rule',
            op: getDefaultRuleOp(properties[0].type),
            property: properties[0].name,
            value: null
        });
        onChange({
            ...group,
            filters: newFilters
        });
    }

    const handleAddGroup = () => {
        const newFilters = [...filters];
        newFilters.push({
            type: 'group',
            op: 'and',
            filters: [{
                type: 'rule',
                op: getDefaultRuleOp(properties[0].type),
                property: properties[0].name,
                value: null
            }]
        });
        onChange({
            ...group,
            filters: newFilters
        });
    }

    const handleDelete = (index) => {
        if (filters.length === 1) {
            return handleDeleteAll();
        }

        const newFilters = [...filters];
        newFilters.splice(index, 1);
        onChange({
            ...group,
            filters: newFilters
        });
    }

    const groupOpChanged = (value) => {
        onChange({
            ...group,
            op: value
        });
    }

    const handleRuleOpChanged = (index, value) => {
        const newFilters = [...filters];
        newFilters[index] = {
            ...newFilters[index],
            op: value
        }
        onChange({
            ...group,
            filters: newFilters
        });
    }

    const turnIntoGroup = (index) => {
        const newFilters = [...filters];
        const filter = newFilters[index];
        newFilters[index] = {
            type: 'group',
            op: 'and',
            filters: [filter]
        };
        onChange({
            ...group,
            filters: newFilters
        });
    }

    const [anchorEl, setAnchorEl] = useState(null);
    const [filterMenuState, setFilterMenuState] = useState({
        anchorEl: null,
        index: null,
    });

    const closeFilterMenu = () => {
        setFilterMenuState({
            ...filterMenuState,
            anchorEl: null,
        });
    }

    return <div style={{
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        paddingTop: level === 0 ? '10px' : '0px',
        border: level === 0 ? 'none' : '1px solid #ccc',
        backgroundColor: level === 0 ? '#fff' : (level === 1 ? '#fcfcfc' : '#f8f8f8'),
    }}>
        {
            filters.filter(filter => filter && (filter.type === 'group' || properties.find(p => p.name === filter.property))).map((filter, index) => {
                let rule_ops = filter.type === 'rule'
                    && FILTER_RULE_OP_TYPES.find(item => item.toProperties.includes(properties.find(p => p.name === filter.property).type))
                        .ruleOps.concat([{
                            value: 'is_empty',
                            label: intl.formatMessage({ id: 'is_empty' }),
                        }, {
                            value: 'is_not_empty',
                            label: intl.formatMessage({ id: 'is_not_empty' }),
                        }]);

                const property = properties.find(p => p.name === filter.property);
                if (rule_ops && property.type === 'Date' && property.hasEndDate) {
                    rule_ops = rule_ops.filter(item => item.value !== 'eq' && item.value !== 'ne');
                }

                return <div key={index} style={{
                    display: 'flex',
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    padding: '4px 6px',
                    fontSize: '14px',
                }}>
                    <div style={{ display: 'flex', flexDirection: 'row' }}>
                        <div style={{ width: 60, alignSelf: 'flex-start', textAlign: 'end' }}>
                            {index === 0 &&
                                <div style={{ marginTop: 5, paddingRight: 4 }}>
                                    {intl.formatMessage({ id: 'filter_where' })}
                                </div>
                            }
                            {index === 1 &&
                                <Selector
                                    value={op}
                                    onChange={groupOpChanged}
                                    options={[
                                        {
                                            label: intl.formatMessage({ id: 'filter_and' }),
                                            value: 'and'
                                        },
                                        {
                                            label: intl.formatMessage({ id: 'filter_or' }),
                                            value: 'or'
                                        }
                                    ]}
                                />}
                            {index > 1 &&
                                <div style={{ marginTop: 5, paddingRight: 4 }}>
                                    {intl.formatMessage({ id: `filter_${op}` })}
                                </div>
                            }
                        </div>
                        {
                            filter.type === 'rule' &&
                            <div style={{ flex: 1, paddingRight: 4, display: 'flex', flexDirection: 'row', height: 30, columnGap: '4px' }}>
                                <PropertySelector
                                    properties={properties}
                                    value={filter.property}
                                    onChange={(value) => {
                                        console.log(value);
                                        handleChangeRule(index, { property: value });
                                    }}
                                />
                                <Selector
                                    value={filter.op}
                                    options={rule_ops}
                                    onChange={(value) => handleRuleOpChanged(index, value)}
                                />
                                {
                                    filter.op === 'is_empty' || filter.op === 'is_not_empty' ? null :

                                        <FilterDataInput
                                            value={filter.value}
                                            op={filter.op}
                                            property={properties.find(p => p.name === filter.property)}
                                            updateValue={(value) => handleChangeRule(index, { value })}
                                        />
                                }
                            </div>
                        }
                        {
                            filter.type === 'group' &&
                            <FilterGroup
                                group={filter}
                                onChange={(value) => handleChangeGroup(index, value)}
                                properties={properties}
                                level={level + 1}
                            />
                        }
                    </div>
                    <div
                        className='hoverStand'
                        style={{ alignSelf: 'flex-start', color: '#999' }}
                        onClick={(e) => {
                            setFilterMenuState({
                                anchorEl: e.currentTarget,
                                index: index,
                                type: filter.type,
                            });
                        }} >
                        <MoreHoriz
                            size={16}
                        />
                    </div>

                </div>
            })
        }
        <div
            className="hoverStand"
            style={styles.menuItem}
            onClick={(e) => {
                if (level < 2) {
                    setAnchorEl(e.currentTarget);
                } else {
                    handleAddRule();
                }
            }}
        >
            <Plus size={18} style={styles.icon} />
            {
                intl.formatMessage({ id: 'add_filter_rule' })
            }
            {
                level < 2 && <KeyboardArrowDown size={17} style={{ paddingLeft: 2, marginTop: 1, color: 'gray' }} />
            }
        </div>
        <Popover
            open={Boolean(anchorEl)}
            anchorEl={anchorEl}
            onClose={() => {
                setAnchorEl(null);
            }}
            anchorOrigin={{
                vertical: 'bottom',
                horizontal: 'left',
            }}
            transformOrigin={{
                vertical: 'top',
                horizontal: 'left',
            }}
        >
            <div style={{ padding: '4px 6px' }}>
                <div className="hoverStand" onClick={() => { handleAddRule(); setAnchorEl(null); }}>
                    <Plus size={18} style={styles.icon} />
                    {intl.formatMessage({ id: 'add_filter_rule' })}
                </div>
                <div className="hoverStand" onClick={() => { handleAddGroup(); setAnchorEl(null); }} >
                    <AddToQueue size={18} style={styles.icon} />
                    {intl.formatMessage({ id: 'add_filter_group' })}
                </div>
            </div>
        </Popover>

        <Popover
            anchorEl={filterMenuState.anchorEl}
            open={Boolean(filterMenuState.anchorEl)}
            onClose={closeFilterMenu}
            anchorOrigin={{
                vertical: 'bottom',
                horizontal: 'left',
            }}
            transformOrigin={{
                vertical: 'top',
                horizontal: 'left',
            }}
        >
            <MenuItem onClick={() => {
                handleDelete(filterMenuState.index);
                closeFilterMenu();
            }} >
                {intl.formatMessage({ id: filterMenuState.type === 'group' ? 'delete_filter_group' : 'delete_filter_rule' })}
            </MenuItem>

            {
                filterMenuState.type === 'rule' &&
                <MenuItem onClick={() => {
                    turnIntoGroup(filterMenuState.index);
                    closeFilterMenu();
                }}>
                    {intl.formatMessage({ id: 'turn_into_filter_group' })}
                </MenuItem>
            }
        </Popover>
    </div>
}


export const FilterSettings = ({ view }) => {
    const lineInputStyle = {
        border: 'none',
        outline: 'none',
        margin: '0px 0px 4px 0px',
        padding: '10px 6px',
        margin: '0',
        fontSize: '1rem',
    }

    const btnRef = useRef(null);
    const intl = useIntl();
    const dialogState = useSelector(state => state.viewFilters.byId[view._id]) || {};
    const { visible } = dialogState;

    const { filterGroup = {} } = dialogState;
    const dispatch = useDispatch();
    const [properties, setProperties] = useState(null);
    const doc = useSelector(state => state.docs.byId[view.dataSource]);

    useEffect(() => {
        if (!doc || !doc.meta || !doc.meta.properties) return;
        let viewProperties = getViewProperties(doc.meta.properties, view);
        viewProperties = viewProperties.filter(p => !p.hide);
        setProperties(viewProperties);
    }, [doc, view]);

    const handleClose = () => {
        dispatch({
            type: VIEW_FILTER_ACTIONS.updated,
            item: {
                ...dialogState,
                visible: false,
            }
        })
    }

    const handleDeleteAll = () => {
        dispatch({
            type: VIEW_FILTER_ACTIONS.updated,
            item: {
                ...dialogState,
                filterGroup: {},
                visible: false,
            }
        })
    }

    const handleFilterChange = (value) => {
        dispatch({
            type: VIEW_FILTER_ACTIONS.updated,
            item: {
                ...dialogState,
                filterGroup: value,
            }
        })
    }

    const isFilterGroupVaild = useCallback((filter) => {
        if (!filter || !properties) return false;

        if (filter.type !== 'rule') {
            if (!filter.filters) {
                return false;
            }

            for (let rule of filter.filters) {
                let isValid = isFilterGroupVaild(rule);
                if (isValid) return true;
            }
        } else {
            const filterProperty = properties.find(vp => vp.name == filter.property);
            const validRuleOps = filterProperty && FILTER_RULE_OP_TYPES.find(op => op.toProperties.includes(filterProperty.type)).ruleOps;
            if (!validRuleOps || !validRuleOps.find(op => op.value === filter.op)) {
                return false;
            }

            if (filterProperty.type === 'Number') {
                return filter.value !== null && filter.value !== undefined && !isNaN(Number(filter.value));
            }

            return !!filter.value;
        }
    }, [properties]);

    if (!properties) return null;

    const firstFilter = filterGroup.filters[0];
    const isFirstFilterValid = firstFilter.type === 'rule' && isFilterGroupVaild(firstFilter);
    const firstFilterProperty = firstFilter && firstFilter.type === 'rule' ? properties.find(vp => vp.name == firstFilter.property) : null;
    const btnStyle = !isFilterGroupVaild(filterGroup) ? { backgroundColor: '#fafafa', color: 'gray', border: '1px solid gray' } : {};

    let firstFilterValue = firstFilter && firstFilter.value;
    let firstFilterOp;
    if (filterGroup.filters.length === 1 && isFirstFilterValid) {
        if (firstFilterProperty.type === 'Date') {
            firstFilterValue = !Array.isArray(firstFilter.value)
                ? formatDate(firstFilter.value, firstFilterProperty.dateFormat || 'YYYY/MM/DD')
                : (formatDate(firstFilter.value[0], firstFilterProperty.dateFormat || 'YYYY/MM/DD') + ' - ' + formatDate(firstFilter.value[1], firstFilterProperty.dateFormat || 'YYYY/MM/DD'));
        } else {
            firstFilterValue = <CellView
                value={firstFilter.value}
                property={firstFilterProperty}
            />
        }

        firstFilterOp = firstFilter.op != 'eq' && FILTER_RULE_OP_TYPES.find(op => op.toProperties.includes(firstFilterProperty.type)).ruleOps.find(op => op.value === firstFilter.op).label;
    }

    return <div>
        <div
            ref={btnRef}
            className='round-corner-btn'
            style={btnStyle}
            onClick={() => {
                dispatch({
                    type: VIEW_FILTER_ACTIONS.updated,
                    item: {
                        ...dialogState,
                        visible: !visible,
                    }
                })
            }}
        >

            {
                filterGroup.filters.length === 1 && firstFilterProperty && DB_PROPERTY_TYPES.find(t => t.value === firstFilterProperty.type).icon
            }
            {filterGroup.filters.length > 1 && <Filter size={14} style={{ paddingRight: 2 }} />}
            <div>
                {
                    filterGroup.filters.length === 1 &&
                    isFirstFilterValid &&
                    <div style={{
                        display: 'flex',
                        flexDirection: 'row',
                        alignItems: 'center',
                        columnGap: '2px',
                    }}>
                        {
                            `${firstFilterProperty.label}:`
                        }
                        {
                            firstFilterOp && 
                            <div style={{
                                fontSize: '12px',
                            }}>
                                {firstFilterOp}
                            </div>
                        }
                        {
                            firstFilterValue
                        }
                    </div>
                }
                {
                    filterGroup.filters.length === 1 &&
                    !isFirstFilterValid &&
                    (firstFilterProperty ? firstFilterProperty.label : '1 ' + intl.formatMessage({ id: 'filter' }))
                }

                {filterGroup.filters.length > 1 && `${filterGroup.filters.length} ${intl.formatMessage({ id: 'filters' })}`}
            </div>

            {<KeyboardArrowDown size={17} style={{ paddingLeft: 2, marginTop: 1 }} />}
        </div>
        <Popover
            open={visible && Boolean(btnRef.current)}
            onClose={handleClose}

            anchorEl={btnRef.current}
            anchorOrigin={{
                vertical: 'bottom',
                horizontal: 'left',
            }}
            transformOrigin={{
                vertical: 'top',
                horizontal: 'left',
            }}
        >
            <div style={{ minWidth: 240, width: 'fit-content', padding: 0, }}>
                <FilterGroup
                    group={filterGroup}
                    properties={properties}
                    onChange={handleFilterChange}
                    handleDeleteAll={handleDeleteAll}
                    level={0}
                />
            </div>

            <Divider />
            <div
                className="hoverStand"
                style={styles.menuItem}
                onClick={handleDeleteAll}
            >
                <Trash size={18} style={styles.icon} />
                {
                    intl.formatMessage({ id: 'delete_filter' })
                }
            </div>
        </Popover >
    </div >
};

const styles = {
    menuItem: {
        display: 'flex',
        margin: '0px',
        color: '#555',
        flexDirection: 'row',
        justifyContent: 'flex-start',
        alignContent: 'center',
        alignItems: 'center'
    },

    icon: {
        marginRight: 5
    }
}
