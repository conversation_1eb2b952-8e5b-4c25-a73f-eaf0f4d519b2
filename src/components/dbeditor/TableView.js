import React, { useCallback, useEffect, useMemo, useRef } from 'react'
import ReactDOM from 'react-dom'

import { Plus } from '@styled-icons/boxicons-regular/Plus';
import { MoreHoriz } from '@styled-icons/material';
import { DraggableCard } from '../dndlist/DraggableCard';
import {
  createTable,
  Column,
  TableInstance,
  useTableInstance,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
} from '@tanstack/react-table'
import { useDispatch, useSelector } from 'react-redux';
import { newDbData, updateDbData, updateDbView, upsertDoc, upsertViewDataOrder } from 'src/actions/ticketAction';
import { useIntl } from 'react-intl';
import { Button, Popover, Table, TableBody, TableCell, TableHead, TableRow, TextareaAutosize } from '@mui/material';
import PropertyMenu from './PropertyMenu';
import { addDBRow, confirmToRemoveSortSettings, deleteDBRow, fillAdvancedDbData, fillDbData, filterDbData, getViewProperties, insertColumn, onDeleteRow, onDuplicateRow, removeSortSettings, sortDbData, updateDBProperties, updateDBProperty, updateDocProperties, updateRowCellData } from './DBUtil';
import { DataInput } from './DataInput.js';
import { CellView } from './CellView';
import { DragIndicator } from '@styled-icons/material/DragIndicator';
import { OpenInFull } from '@styled-icons/material/OpenInFull';
import { useDrag, useDrop } from 'react-dnd'
import { getState, getStateByUser } from 'src/reducers/listReducer';
import { DATA_EDITOR_DIALOG } from 'src/constants/actionTypes';
import { Edit } from '@styled-icons/material/Edit';
import { DataMenu } from './DataMenu';
import { ResizeHandler } from '../common/ResizeHandler';

let table = createTable()

const cellHeight = '36px';

const Cell = ({ getValue, row: { index }, column: { id }, instance }) => {
  const initialValue = getValue()
  // We need to keep and update the state of the cell normally
  const [value, setValue] = React.useState(initialValue)


  // If the initialValue is changed external, sync it up with our state
  React.useEffect(() => {
    setValue(initialValue)
  }, [initialValue])

  if (id === 'newColumn') {
    return <div />
  }

  return (
    <div
      style={{
        height: '28px',
        fontSize: '14px',
        padding: '8px 6px 0px 6px',
      }}
    >
      {value}
    </div>
  )
}

// Give our default column cell renderer editing superpowers!
const defaultColumn = {
  cell: Cell,
}

const Row = ({ style, row, rowIndex, hasDocAttached, viewProperties, setDndHoverState, onDrop, onDeleteRow, onInsertRow, onDuplicateRow, updateCellData,
  selectedCell, setSelectedCell, inputState, setInputState, currentCellRef, navigateCell, openDataEditor, view, property }) => {
  // console.log('row.............', row.id, rowHovered);
  const intl = useIntl();

  const dropRef = React.useRef(null)
  const dragRef = React.useRef(null)

  const [{ opacity }, drag, preview] = useDrag(() => ({
    type: 'ROW',
    collect: (monitor) => ({
      opacity: monitor.isDragging() ? 0.4 : 1,
    }),
    item: () => {
      return { rowIndex }
    },
  }))

  const [, drop] = useDrop({
    accept: 'ROW',
    hover(item, monitor) {
      if (!dropRef.current) {
        return
      }
      const dragIndex = item.rowIndex
      const hoverIndex = rowIndex
      // Don't replace items with themselves
      if (dragIndex === hoverIndex) {
        return
      }
      // Determine rectangle on screen
      const hoverBoundingRect = dropRef.current.getBoundingClientRect()
      // Get vertical middle
      const hoverMiddleY =
        (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2
      // Determine mouse position
      const clientOffset = monitor.getClientOffset()
      // Get pixels to the top
      const hoverClientY = clientOffset.y - hoverBoundingRect.top
      // Only perform the move when the mouse has crossed half of the items height
      // When dragging downwards, only move when the cursor is below 50%
      // When dragging upwards, only move when the cursor is above 50%
      // Dragging downwards
      if (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) {
        return
      }
      // Dragging upwards
      if (dragIndex > hoverIndex && hoverClientY > hoverMiddleY) {
        return
      }
      // Time to actually perform the action
      setDndHoverState({ dragIndex, hoverIndex, position: hoverIndex + (hoverClientY > hoverMiddleY ? 1 : 0), isUp: dragIndex > hoverIndex })
      // Note: we're mutating the monitor item here!
      // Generally it's better to avoid mutations,
      // but it's good here for the sake of performance
      // to avoid expensive index searches.
      item.index = hoverIndex
    },
    drop(item, monitor) {
      if (!dropRef.current) {
        return
      }

      const didDrop = monitor.didDrop();
      if (didDrop) {
        return;
      }

      onDrop && onDrop(item);
    },
  })

  preview(drop(dropRef))
  drag(dragRef)

  const [rowHovered, setRowHovered] = React.useState(null);
  const [colHovered, setColHovered] = React.useState(null);

  return <TableRow key={row.id}
    ref={dropRef}
    className='db_table_row'
    style={{ ...style }}
    onMouseOver={() => setRowHovered(row.id)}
    onMouseLeave={() => setRowHovered(null)}
  >
    {/* <div> */}
    {row.getVisibleCells().map((cell, columnIndex) => {
      const property = viewProperties.find(p => p.name == cell.columnId);
      const isSelected = selectedCell && selectedCell.rowIndex == rowIndex && selectedCell.columnIndex == columnIndex;

      let cellStyle = {
        position: 'relative',
        padding: '0px',
        height: 1
      };

      cellStyle.borderRight = columnIndex === row.getVisibleCells().length - 1 ? 'none' : '1px solid lightgray'

      if (columnIndex === 0) {
        cellStyle.borderLeft = 'none';
        cellStyle.borderRight = 'none';
        cellStyle.borderBottom = 'none';
        cellStyle.borderTop = 'noe';
        // cellStyle.width = "40px";
      }

      const maxWidth = property && view.propertyStyles?.[property.name]?.width ? undefined : '330px';

      return <TableCell
        key={cell.id}
        className="db_table_cell"
        style={cellStyle}
        onMouseOver={() => setColHovered(cell.columnId)}
        onMouseLeave={() => setColHovered(null)}
      >
        <div
          className='focusable_cell'
          tabIndex={cell.columnId == 'newColumn' ? undefined : "0"}
          style={{
            display: 'flex',
            minHeight: cellHeight,
            width: property && view.propertyStyles?.[property.name]?.width || 'auto',
            height: '-webkit-fill-available',
            maxWidth,
            fontSize: '14px',
            alignItems: 'center',
            // padding: '0px 6px',
          }}
          ref={isSelected ? currentCellRef : null}
          onKeyDown={(e) => {
            if (e.key === 'ArrowDown' || e.key === 'ArrowUp' || e.key === 'ArrowLeft' || e.key === 'ArrowRight') {
              e.preventDefault();
              e.stopPropagation();
              navigateCell(rowIndex, columnIndex, e.key === 'ArrowDown' && 'down' || e.key === 'ArrowUp' && 'up' || e.key === 'ArrowLeft' && 'left' || e.key === 'ArrowRight' && 'right');
              return;
            }

            if (e.key === 'Enter') {
              e.preventDefault();
              e.stopPropagation();
            }

            if (!property.advancedType) {
              setInputState({
                anchorEl: e.currentTarget,
                property,
                value: cell.getValue(),
                cell: {
                  rowId: row.id,
                  columnId: cell.columnId,
                }
              });
            }
            // setInput({ visible: true, value: cell.getValue() });
          }}
          onClick={(e) => {
            if (cell.columnId == 'newColumn' || columnIndex === 0) {
              e.preventDefault();
              e.stopPropagation();
              return;
            }

            if (property.type == 'Checkbox') {
              updateCellData(rowIndex, cell.columnId, !cell.getValue());
              return;
            }

            if (property.type == 'Url' && cell.getValue()) {
              window.open(cell.getValue());
              return;
            }

            if ((!inputState || !inputState.anchorEl) && !property.advancedType) {
              setInputState({
                anchorEl: e.currentTarget,
                property,
                value: cell.getValue() || '',
                cell: {
                  rowId: row.id,
                  columnId: cell.columnId,
                }
              });
            }

            if (!isSelected) {
              setSelectedCell({
                rowIndex,
                columnIndex,
              });
            }
          }}

          onBlur={(e) => {
            if (inputState && inputState.anchorEl) {
              return;
            }

            if (isSelected) {
              setSelectedCell(null);
              return
            }
          }}
        >
          <CellView
            property={property}
            value={cell.getValue()}
            editMode={true}
            hasDocAttached={hasDocAttached}
            style={{
              margin: '0px 4px',
            }}
          />
        </div>
        {/* {cell.renderCell()} */}
        {
          row.id == rowHovered &&
          property && property.type === 'Title' &&
          <div className='open_db_data_button'
            onClick={() => openDataEditor(row.id)}
          >
            <OpenInFull
              style={{
                width: '12px',
                height: '12px',
                marginRight: '3px',
              }}
            />

            {intl.formatMessage({ id: 'open' })}
          </div>
        }
        {
          row.id == rowHovered && cell.columnId === colHovered &&
          property && property.type === 'Url' &&
          cell.getValue() &&
          <div className='open_db_data_button'
            onClick={(e) => {
              setInputState({
                anchorEl: e.currentTarget.parentElement,
                property,
                value: cell.getValue() || '',
                cell: {
                  rowId: row.id,
                  columnId: cell.columnId,
                }
              });
              e.preventDefault();
              e.stopPropagation();
            }}
          >
            <Edit
              size={17}
            />
          </div>
        }
        {
          row.id == rowHovered &&
          columnIndex === 0 &&
          <div
            className='add_db_data_handler'
            style={{
              color: 'rgba(55, 53, 47, 0.3)',
            }}
            onClick={() => {
              onInsertRow(rowIndex + 1);
            }}
          >
            <Plus size={22} />
          </div>
        }
        {
          row.id == rowHovered &&
          columnIndex === 0 &&
          <div
            className='drag_db_data_handler'
            ref={dragRef}
          >
            <DataMenu
              onClose={() => setRowHovered(null)}
              onDelete={() => {
                onDeleteRow(rowIndex);
              }}
              onDuplicate={() => {
                onDuplicateRow(rowIndex);
              }}
            >
              <DragIndicator
                size={21}
                style={{
                  color: 'rgba(55, 53, 47, 0.3)',
                  backgroundColor: 'transparent',
                }}
              />
            </DataMenu>
          </div>
        }
      </TableCell>
    })}
    {/* </div> */}
  </TableRow>
}

const minCellWidth = 100;
const HeaderCell = ({ onDrop, moveCard, onDroppedOutside, onResizeEnd, onHideProperty, property, view, hid, index }) => {
  const cellRef = React.useRef(null);

  const [width, setWidth] = React.useState(0);
  const [resize, setResize] = React.useState({});

  useEffect(() => {
    if (cellRef.current) {
      setWidth(cellRef.current.offsetWidth);
    }
  }, [cellRef && cellRef.current]);

  useEffect(() => {
    if (cellRef.current && resize.x) {
      cellRef.current.style.width = (Math.max(width + resize.x, minCellWidth)) + 'px';
      cellRef.current.style.maxWidth = undefined;
    }
  }, [resize, cellRef.current, width]);

  const maxWidth = view.propertyStyles?.[property.name]?.width ? undefined : '330px';

  return <div
    ref={cellRef}
    style={{
      display: 'flex',
      flexDirection: 'row',
      alignItems: 'center',
      position: 'relative',
      width: view.propertyStyles?.[property.name]?.width || 'auto',
      maxWidth
    }}
  >
    <DraggableCard
      itemData={{
        index: index,
        id: property.name,
      }}

      style={{
        width: '-webkit-fill-available',
      }}

      isHorizontal={true}

      onDrop={onDrop}

      moveCard={moveCard}
      onDroppedOutside={onDroppedOutside}
    >
      <PropertyMenu
        columnId={property.name}
        hid={hid}
        viewId={view._id}
        style={{ minWidth: minCellWidth, width: '-webkit-fill-available', maxWidth }}
        showPlace={'table_header'}
        onHideProperty={onHideProperty}
      />
    </DraggableCard>
    <ResizeHandler
      onResize={(coord) => {
        setResize(coord);
      }}

      onResizeEnd={(coord) => {
        onResizeEnd(Math.max(width + coord.x, minCellWidth), property.name);
      }}

      style={{
        position: 'absolute',
        right: '0px',
        height: '100%',
      }}
    />
  </div>
}

const TableView = ({ view, mode, containerPaddingLeft }) => {
  const intl = useIntl();
  const dispatch = useDispatch();

  const cellHandlerWidth = mode === 'embed' ? 'max(50% - 436px, 0px)' : '0px';

  const loginUser = useSelector(state => state.loginIn.user);
  const orgs = useSelector(state => getStateByUser(state.org_lists, loginUser));
  const workingSpace = orgs.items.find(org => org._id === loginUser.workingOrgId) || { users: [] };
  const members = workingSpace.users.filter(user => !!user.user).map(user => user.user);

  const hid = view.dataSource;
  const doc = useSelector(state => state.docs.byId[hid]);
  const dbdata_list = useSelector(state => getState(state.dbdata_lists, hid));
  const view_dataorder_list = useSelector(state => getState(state.dbview_dataorder_lists, view._id));

  const sortSettingsState = useSelector(state => state.viewSorts.byId[view._id]) || {};
  const filterSettingsState = useSelector(state => state.viewFilters.byId[view._id]) || {};

  const [dbdata, setDbdata] = React.useState([]);

  const [dndState, setDndState] = React.useState({
    isDragging: false,
    dragIndex: null,
    hoverIndex: null,
  });
  const [dndIndicator, setDndIndicator] = React.useState({
    onLeft: true,
    index: null,
  });

  const moveCard = (dragIndex, hoverIndex) => {
    setDndState(prevState => {
      prevState.isDragging = true;
      prevState.dragIndex = dragIndex;
      prevState.hoverIndex = hoverIndex;

      if (dragIndex < hoverIndex) {
        setDndIndicator({
          onLeft: false,
          index: hoverIndex + 1,  // +1 because of the hidden row handler
        });
      } else {
        setDndIndicator({
          onLeft: true,
          index: hoverIndex + 1,
        });
      }

      return prevState;
    })
  }

  const onDrop = () => {
    if (!dndState || dndState.dragIndex === dndState.hoverIndex) {
      return;
    }

    const dragCard = viewProperties[dndState.dragIndex];
    if (!dragCard) {
      return;
    }

    const newCards = [...viewProperties];
    newCards.splice(dndState.dragIndex, 1);
    newCards.splice(dndState.hoverIndex, 0, dragCard);

    doc.meta.properties.forEach(property => {
      if (!newCards.find(card => card.name === property.name)) {
        newCards.push({ ...property, hide: true });
      }
    });

    dispatch(updateDbView({
      viewId: view._id,
      hid: view.hid,
      pageBy: 'orderFactor',
      data: { properties: newCards }
    }));

    setDndState({
      isDragging: false,
      dragIndex: null,
      hoverIndex: null,
    })
    setDndIndicator(null);
  }

  const onDroppedOutside = () => {
    setDndState({
      isDragging: false,
      dragIndex: null,
      hoverIndex: null,
    })
    setDndIndicator(null);
  }

  const onHideProperty = (name) => {
    dispatch(updateDbView({
      viewId: view._id,
      hid: view.hid,
      pageBy: 'orderFactor',
      data: {
        properties: view.properties.map(property => {
          if (property.name === name) {
            property.hide = true;
          }
          return property;
        })
      }
    }));
  }

  const onResizeEnd = (width, columnId) => {
    dispatch(updateDbView({
      viewId: view._id,
      hid: view.hid,
      pageBy: 'orderFactor',
      data: {
        propertyStyles: {
          ...view.propertyStyles,
          [columnId]: {
            width,
          }
        }
      }
    }));
  }

  const [viewProperties, setViewProperties] = React.useState([]);
  const [filledAdancedDbData, setFilledAdancedDbData] = React.useState([]);
  const [filteredDbData, setFilteredDbData] = React.useState([]);
  const [filledDbData, setFilledDbData] = React.useState([]);
  const [data, setData] = React.useState([]);

  useEffect(() => {
    if (doc && doc.meta && doc.meta.properties) {
      setViewProperties(getViewProperties(doc.meta.properties, view).filter(p => !p.hide));
    }
  }, [JSON.stringify(doc), JSON.stringify(view)]);

  useEffect(() => {
    if (!dbdata_list || !dbdata_list.items || dbdata_list.items.length === 0 || !viewProperties || viewProperties.length === 0) {
      setData([]);
      return;
    }

    let advancedData = fillAdvancedDbData(dbdata_list.items, viewProperties);
    setFilledAdancedDbData(advancedData);

  }, [JSON.stringify(viewProperties), dbdata_list]);

  useEffect(() => {
    if (!filledAdancedDbData || filledAdancedDbData.length === 0) {
      return;
    }

    let dbdata = filledAdancedDbData;

    if (filterSettingsState.filterGroup && filterSettingsState.filterGroup.filters && filterSettingsState.filterGroup.filters.length > 0) {
      dbdata = filterDbData(dbdata, filterSettingsState.filterGroup, viewProperties)
    }

    setFilteredDbData(dbdata);
  }, [JSON.stringify(filterSettingsState), JSON.stringify(viewProperties), filledAdancedDbData]);

  useEffect(() => {
    setFilledDbData(fillDbData(filteredDbData, viewProperties, members));
  }, [filteredDbData]);

  useEffect(() => {
    if (!doc || !filledDbData) {
      return;
    }

    let dbdata = sortDbData(filteredDbData, filledDbData, sortSettingsState, doc.meta && doc.meta.properties, view_dataorder_list.items);

    setDbdata(dbdata);
    setData(dbdata.map(d => d.data || {}));
  }, [doc && doc.meta, sortSettingsState.sorts, filledDbData, view_dataorder_list]);

  const pageBy = 'orderFactor';

  const columns = React.useMemo(
    () => {
      const columns = viewProperties.map((property, index) => {
        return table.createDataColumn(property.name, {
          header: () => <HeaderCell
            hid={hid}
            property={property}
            index={index}
            view={view}
            onHideProperty={onHideProperty}
            moveCard={moveCard}
            onDrop={onDrop}
            onDroppedOutside={onDroppedOutside}
            onResizeEnd={onResizeEnd}
          />
        })
      });

      columns.push(table.createDataColumn(row => '', {
        id: 'newColumn',
        header: () => <div className='hoverStand'
          style={{ width: '-webkit-fill-available', minWidth: minCellWidth }}
          onClick={() => insertColumn(dispatch, intl, hid, doc.meta, doc.meta.properties.length)}
        >
          <Plus size={22} style={{ color: 'gray' }} />
        </div>
      }));

      columns.unshift(table.createDataColumn(row => '', {
        id: 'rowHandler',
        header: () => <div />
      }));

      return columns
    }, [doc, JSON.stringify(viewProperties)])

  const instance = useTableInstance(table, {
    data,
    columns,
    defaultColumn,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    // getPaginationRowModel: getPaginationRowModel(),
    // autoResetPageIndex,
    // Provide our updateData function to our table meta
    meta: {

    },
    debugTable: true,
  });

  const openDataEditor = (rowIndex) => {
    dispatch({
      type: DATA_EDITOR_DIALOG,
      value: {
        dataSourceHid: hid,
        dataId: dbdata[rowIndex]._id,
        visible: true,
      }
    })
  }

  const [selectedCell, setSelectedCell] = React.useState(null);
  const currentCellRef = useRef(null);

  useEffect(() => {
    if (currentCellRef && currentCellRef.current) {
      currentCellRef.current.focus();
    }
  }, [currentCellRef, selectedCell]);

  const updateCellData = (rowIndex, columnId, value) => {
    rowIndex = parseInt(rowIndex);
    updateRowCellData(dispatch, hid, dbdata[rowIndex], columnId, value);
  }

  const navigateCell = (rowIndex, columnIndex, direction) => {
    const totalRows = instance.getRowModel().rows.length;
    const totalColumns = viewProperties.length;

    if (direction === 'left') {
      if (columnIndex === 1) {  // start from 1, because of the hidden row handler column
        if (rowIndex === 0) {
          return;
        }
        rowIndex = rowIndex - 1;
        columnIndex = totalColumns;

      } else {
        columnIndex = columnIndex - 1;
      }
    } else if (direction === 'right') {
      if (columnIndex === totalColumns) {
        if (rowIndex === totalRows - 1) {
          return;
        }
        rowIndex = rowIndex + 1;
        columnIndex = 1;
      } else {
        columnIndex = columnIndex + 1;
      }
    } else if (direction === 'up') {
      if (rowIndex === 0) {
        return;
      }
      rowIndex = rowIndex - 1;
    } else if (direction === 'down') {
      if (rowIndex === totalRows - 1) {
        return;
      }
      rowIndex = rowIndex + 1;
    }

    setSelectedCell({ rowIndex, columnIndex });
  }

  const [inputState, setInputState] = React.useState({
    anchorEl: null,
    property: null,
    value: null,
  });

  const [rowDndState, setRowDndState] = React.useState({
    dragIndex: null,
    hoverIndex: null,
    isUp: null,
  });

  const setDndHoverState = (state) => {
    setRowDndState(state);
  }

  const onRowDndDrop = () => {
    if (!rowDndState || rowDndState.dragIndex === rowDndState.hoverIndex) {
      return;
    }

    if (sortSettingsState && sortSettingsState.sorts && sortSettingsState.sorts.length > 0) {
      confirmToRemoveSortSettings(dispatch, view._id, intl);

      setRowDndState({
        dragIndex: null,
        hoverIndex: null,
        isUp: null,
      });

      return;
    }

    const { dragIndex, position } = rowDndState;

    let orderFactor = 0;
    if (position === 0) {
      orderFactor = dbdata[0].orderFactor + Math.random() * 10;
    } else if (position > dbdata.length - 1) {
      orderFactor = dbdata[dbdata.length - 1].orderFactor - Math.random() * 10;
    } else {
      orderFactor = (dbdata[position].orderFactor + dbdata[position - 1].orderFactor) / 2;
    }

    setRowDndState({
      dragIndex: null,
      hoverIndex: null,
      isUp: null,
    });

    dispatch(upsertViewDataOrder({
      viewId: view._id,
      dataId: dbdata[dragIndex]._id,
      data: {
        orderFactor,
      }
    }));
  }

  // const onDeleteRow = useCallback((rowIndex) => {
  //   if (rowIndex < 0 || rowIndex > dbdata.length - 1) {
  //     return;
  //   }

  //   const dataId = dbdata[rowIndex]._id;

  //   deleteDBRow(dispatch, hid, dataId);
  // }, [hid, dbdata])

  const onInsertRow = useCallback((rowIndex) => {
    if (sortSettingsState && sortSettingsState.sorts && sortSettingsState.sorts.length > 0) {
      confirmToRemoveSortSettings(dispatch, view._id, intl);
      return;
    }

    rowIndex = parseInt(rowIndex);
    addDBRow(dispatch, hid, dbdata, null, rowIndex);
  }, [hid, dbdata, intl, view._id, sortSettingsState?.sorts])

  // const tableHeader = useMemo(() => {
  //   return <TableHead>
  //     {instance.getHeaderGroups().map(headerGroup => (
  //       <TableRow key={headerGroup.id}>
  //         {headerGroup.headers.map((header, index) => {
  //           let style = {
  //             padding: '0px',
  //             width: index === headerGroup.headers.length - 1 ? '-webkit-fill-available' : (index === 0 ? 54 : minCellWidth),
  //             borderRight: index === headerGroup.headers.length - 1 ? 'none' : '1px solid lightgray',
  //             borderTop: '1px solid lightgray',
  //           };

  //           if (index === 0) {
  //             style.border = 'none';
  //             style.borderTop = 'none';
  //           }

  //           return (
  //             <TableCell key={header.id} colSpan={header.colSpan}
  //               className="db_table_cell"
  //               style={style}
  //             >
  //               {header.isPlaceholder ? null : (
  //                 <div style={{
  //                   borderLeft: dndIndicator && dndIndicator.onLeft && dndIndicator.index === index ? '2px solid skyblue' : 'none',
  //                   borderRight: dndIndicator && !dndIndicator.onLeft && dndIndicator.index === index ? '2px solid skyblue' : 'none',
  //                 }}>
  //                   {header.renderHeader()}
  //                 </div>
  //               )}
  //             </TableCell>
  //           )
  //         })}
  //       </TableRow>
  //     ))}
  //   </TableHead>
  // }, [dndIndicator, columns])

  // console.log('table header..........', tableHeader)

  return (
    <div style={{
      minWidth: '600px',
      minWidth: '800px',
      paddingLeft: cellHandlerWidth,
      paddingRight: cellHandlerWidth,
    }}>
      <Table
        style={{
          width: "-webkit-fill-available",
          marginBottom: '8px',
        }}
      >
        <TableHead>
          {instance.getHeaderGroups().map(headerGroup => (
            <TableRow key={headerGroup.id}>
              {headerGroup.headers.map((header, index) => {
                let style = {
                  padding: '0px',
                  width: index === headerGroup.headers.length - 1 ? '-webkit-fill-available' : (index === 0 ? 54 : minCellWidth),
                  borderRight: index === headerGroup.headers.length - 1 ? 'none' : '1px solid lightgray',
                  borderTop: '1px solid lightgray',
                };

                if (index === 0) {
                  style.border = 'none';
                  style.borderTop = 'none';
                }

                return (
                  <TableCell key={header.id} colSpan={header.colSpan}
                    className="db_table_cell"
                    style={style}
                  >
                    {header.isPlaceholder ? null : (
                      <div style={{
                        borderLeft: dndIndicator && dndIndicator.onLeft && dndIndicator.index === index ? '2px solid skyblue' : 'none',
                        borderRight: dndIndicator && !dndIndicator.onLeft && dndIndicator.index === index ? '2px solid skyblue' : 'none',
                      }}>
                        {header.renderHeader()}
                      </div>
                    )}
                  </TableCell>
                )
              })}
            </TableRow>
          ))}
        </TableHead>
        <TableBody>
          {instance.getRowModel().rows.map((row, rowIndex) => {
            const dataId = dbdata[rowIndex]._id;

            return <Row
              key={row.id}
              row={row}
              rowIndex={rowIndex}
              view={view}
              viewProperties={viewProperties}
              hasDocAttached={dbdata[rowIndex] && dbdata[rowIndex].doc}
              updateCellData={updateCellData}
              selectedCell={selectedCell}
              setSelectedCell={setSelectedCell}
              navigateCell={navigateCell}
              openDataEditor={openDataEditor}

              inputState={inputState}
              setInputState={setInputState}
              currentCellRef={currentCellRef}
              setDndHoverState={setDndHoverState}
              onDrop={onRowDndDrop}
              onDeleteRow={() => onDeleteRow(hid, dataId, dispatch)}
              onInsertRow={onInsertRow}
              onDuplicateRow={() => onDuplicateRow(hid, view, dbdata, dataId, sortSettingsState, dispatch, intl)}

              style={{
                borderBottom: rowDndState && !rowDndState.isUp && rowDndState.hoverIndex === rowIndex ? '2px solid skyblue' : 'none',
                borderTop: rowDndState && rowDndState.isUp && rowDndState.hoverIndex === rowIndex ? '2px solid skyblue' : 'none',
              }}
            />
          })}
          <TableRow>
            <TableCell style={{ border: 'none', borderTop: 'none' }} />
            <TableCell colSpan={instance.getAllColumns().length - 1}
              className="db_table_cell"
              style={{
                padding: '0px',
              }}
            >
              <div className='hoverStand'
                style={{
                  alignItems: 'center',
                  display: 'flex'
                }}
                onClick={() => addDBRow(dispatch, hid, dbdata)}
              >
                <Plus size={22} style={{ color: 'gray' }} />
                <span style={{ color: 'gray' }}>New</span>
              </div>
            </TableCell>
          </TableRow>
        </TableBody>
      </Table>

      <DataInput
        state={inputState}
        updateProperty={(property) => {
          updateDBProperty(dispatch, hid, doc.meta, property);
        }}

        closeAndSaveValue={(value) => {
          updateCellData(inputState.cell.rowId, inputState.cell.columnId, value);
          setInputState({ anchorEl: null })
          currentCellRef && currentCellRef.current && currentCellRef.current.focus();
        }}

        navigateCell={(direction) => {
          navigateCell(selectedCell.rowIndex, selectedCell.columnIndex, direction);
        }}

      />
    </div>
  )
}


export default TableView;
