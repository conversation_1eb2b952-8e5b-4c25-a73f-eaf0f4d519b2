import { MoreHoriz } from '@styled-icons/material';
import { useState, useCallback, useEffect } from 'react';
import { SortableCard } from '../dndlist/SortableCard';
import { OptionChip } from './OptionChip';
import { OptionEditor } from './OptionEditor';
import update from 'immutability-helper';
import { DragIndicator } from '@styled-icons/material/DragIndicator';
import PropertyMenu from './PropertyMenu';
import { Visibility } from '@styled-icons/material-outlined/Visibility';
import { VisibilityOff } from '@styled-icons/material-outlined/VisibilityOff';
import { LockAlt } from '@styled-icons/boxicons-regular/LockAlt';

const PropertyItem = ({ index, item, updateProperty, moveCard, hid }) => {
    const [itemTargeted, setItemTargeted] = useState(false);

    return <SortableCard
        index={index}
        id={item.name}
        moveCard={moveCard}
    >
        <div
            style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                width: '-webkit-fill-available',
                backgroundColor: itemTargeted ? '#f5f5f5' : '#fff',
                padding: '0px',
                cursor: 'pointer',
            }}

            onMouseEnter={() => {
                setItemTargeted(true);
            }}
            onMouseLeave={() => {
                setItemTargeted(false);
            }}
        >
            <div
                style={{
                    display: 'flex',
                    alignItems: 'center',
                    flexDirection: 'row'
                }}
            >
                <DragIndicator
                    size={18}
                    style={{
                        color: 'rgba(55, 53, 47, 0.3)',
                        backgroundColor: 'transparent',
                    }}
                />
                {/* <div> {item.label} </div> */}
                <PropertyMenu
                    columnId={item.name}
                    hid={hid}
                    style={{ minWidth: '100px' }}
                />
            </div>
            <div
                className='hoverStand'
                style={{ color: 'gray', padding: '5px' }}
                onClick={(event) => {
                    if (item.type === 'Title') return;

                    updateProperty({
                        ...item,
                        hide: !item.hide
                    }, 'visibility');

                    event.preventDefault();
                    event.stopPropagation();
                }}

            >
                {
                    item.type === 'Title' &&
                    <LockAlt size={18} style={{ color: 'rgba(55, 53, 47, 0.3)' }} />
                }
                {
                    item.type !== 'Title' &&
                    item.hide &&
                    <VisibilityOff size={18} style={{ color: 'rgba(55, 53, 47, 0.3)' }} />

                }
                {
                    item.type !== 'Title' &&
                    !item.hide &&
                    <Visibility size={16} style={{ color: 'rgba(55, 53, 47, 0.3)' }} />
                }
            </div>
        </div>
    </SortableCard >
}


export const PropertyList = ({ hid, properties, updateProperties }) => {

    const [items, setItems] = useState(properties);

    useEffect(() => {
        setItems(properties);
    }, [properties]);

    const moveCard = useCallback((dragIndex, hoverIndex) => {
        setItems((prevCards) =>
            update(prevCards, {
                $splice: [
                    [dragIndex, 1],
                    [hoverIndex, 0, prevCards[dragIndex]],
                ],
            }),
        )
    }, [])


    return <div style={{
        display: 'flex',
        flexDirection: 'column',
        width: '-webkit-fill-available',
        // padding: '0px 0px 0px 4px',
    }}
        onDrop={(event) => {
            updateProperties(items, 'moveCard');
        }}
    >
        {items.map((item, index) => {
            return <PropertyItem
                key={item.name}
                hid={hid}
                index={index}
                item={item}
                moveCard={moveCard}
                updateProperty={(property, action) => {
                    updateProperties(items.map(p => {
                        if (property.name === p.name) {
                            return property;
                        }

                        return p;
                    }), action);
                }}
            />
        })}
    </div>
}