import { TextareaAutosize, Popover, Divider } from "@mui/material";
import { useEffect, useState } from "react";
import { useIntl } from "react-intl";
import { MenuItem } from "react-pro-sidebar";
import { Trash } from '@styled-icons/bootstrap/Trash';
import { OPTION_BG_COLORS } from "src/constants/constants";
import { OptionSelector } from "./OptionSelector";
import { Check, KeyboardArrowDown } from '@styled-icons/material';

export const OptionEditor = ({ state, saveValue, onDelete, onClose }) => {
    const intl = useIntl();
    const lineInputStyle = {
        border: '1px solid #ccc',
        outline: 'none',
        width: '-webkit-fill-available',
        padding: '6px',
        margin: '4px',
        fontSize: '1rem',
        borderRadius: '4px',
    }

    const { anchorEl } = state;
    const [option, setOption] = useState(state.option);

    useEffect(() => {
        setOption(state.option);
    }, [state.option]);


    const handleClose = () => {
        onClose();
        saveValue(option);
    }

    const [hoveredIndex, setHoveredIndex] = useState(null);

    return <Popover
        open={Boolean(anchorEl)}
        onClose={handleClose}

        anchorEl={anchorEl}
        anchorOrigin={{
            vertical: 'top',
            horizontal: 'right',
        }}
        transformOrigin={{
            vertical: 'top',
            horizontal: 'left',
        }}
    >
        <div style={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            padding: '6px 12px',
            justifyContent: 'center',
        }}>
            <input
                style={lineInputStyle}
                type="text"
                value={option.label}
                onChange={(e) => {
                    setOption({ ...option, label: e.target.value });
                }}
            />
        </div>

        <div style={{ width: '200px', padding: '6px 12px' }}>
            {
                OPTION_BG_COLORS.map((c, index) => (
                    <div
                        key={index}
                        style={{
                            display: 'flex',
                            flexDirection: 'row',
                            alignItems: 'center',
                            justifyContent: 'space-between',
                            backgroundColor: hoveredIndex === index ? '#eee' : 'white',
                            padding: '2px 6px 2px 6px',
                        }}
                        onMouseEnter={() => {
                            setHoveredIndex(index);
                        }}
                        onMouseLeave={() => {
                            setHoveredIndex(null);
                        }}
                        onClick={() => {
                            setOption({ ...option, bgColor: c.value });
                            // setColor(c.value);
                        }}
                    >
                        <div
                            style={{
                                display: 'flex',
                                flexDirection: 'row',
                                alignItems: 'center',
                            }}
                        >
                            <div
                                style={{
                                    width: '48px',
                                    height: '24px',
                                    backgroundColor: c.value,
                                    borderRadius: '4px',
                                    marginRight: '8px',
                                }}
                            />
                            {
                                c.label
                            }
                        </div>
                        {
                            c.value === option.bgColor &&
                            <div>
                                <Check size={15} style={{ marginRight: '0px' }} />
                            </div>
                        }
                    </div>
                ))
            }

            <Divider style={{ margin: '8px 0px' }} />

            <div
                style={{
                    display: 'flex',
                    flexDirection: 'row',
                    alignItems: 'center',
                    backgroundColor: hoveredIndex === 'delete' ? '#eee' : 'white',
                    padding: '2px 6px 2px 6px',
                    cursor: 'pointer',
                }}
                onMouseEnter={() => {
                    setHoveredIndex('delete');
                }}
                onMouseLeave={() => {
                    setHoveredIndex(null);
                }}

                onClick={() => {
                    onDelete();
                }}>
                <Trash size={18} style={styles.icon} />
                {
                    intl.formatMessage({ id: 'option_delete' })
                }
            </div>
        </div>


    </Popover>
};

const styles = {
    menuItem: {
        display: 'flex',
        width: '160px',
        margin: '0px',
        flexDirection: 'row',
        justifyContent: 'flex-start',
        alignContent: 'center',
        alignItems: 'center'
    },

    icon: {
        marginRight: 5
    }
}