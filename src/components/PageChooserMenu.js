
import { KeyboardReturn } from '@styled-icons/material/KeyboardReturn';
import { useEffect, useRef, useState } from 'react';
import { Popover, TextareaAutosize } from "@mui/material";
import { useIntl } from 'react-intl';
import { PageChooser } from './PageChooser';

export const PageChooserMenu = (props) => {
    const intl = useIntl();

    const [anchorEl, setAnchorEl] = useState();
    const { onSelect } = props;

    return (
        <div>
            <div className='hoverStand'
                style={{
                    paddingLeft: '6px', paddingRight: '6px',
                    paddingTop: '2px', paddingBottom: '2px',
                    borderRadius: '6px',
                    fontSize: '14px',
                    color: 'dodgerblue',
                    border: '1px solid dodgerblue'
                    // backgroundColor: '#eee'
                }}
                onClick={(event) => {
                    setAnchorEl(event.currentTarget);
                }}
            >
                {intl.formatMessage({ id: 'page_chooser_title' })}
            </div>
            <Popover
                open={Boolean(anchorEl)}
                onClose={() => setAnchorEl(null)}
                onKeyDown={(e) => {
                    if (e.key === 'Escape') {
                        setAnchorEl(null);
                    }
                    e.stopPropagation();
                }}
                anchorEl={anchorEl}
                anchorOrigin={{
                    vertical: 'bottom',
                    horizontal: 'left',
                }}
                transformOrigin={{
                    vertical: 'top',
                    horizontal: 'left',
                }}
                style={{
                    display: 'flex',
                    flexDirection: 'row',
                    alignItems: 'center',
                }}
            >
                <div style={{
                    width: 600,
                    height: 500,
                }}>
                    <PageChooser
                        accepts={['doc', 'slides', 'flow']}
                        onSelect={(item) => {
                            onSelect && onSelect(item);
                            setAnchorEl(null);
                        }}
                        onClose={() => setAnchorEl(null)}
                    />
                </div>
            </Popover>
        </div>

    );
}