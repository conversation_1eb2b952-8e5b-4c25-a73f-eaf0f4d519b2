
import { useDispatch, useSelector } from 'react-redux';
import { useIntl } from 'react-intl';
import Button from '@mui/material/Button';
import { restoreDoc } from 'src/actions/ticketAction'
import { SUB_DOCS_ACTIONS, WORKSPACE_DOCS_ACTIONS, PRIVATE_DOCS_ACTIONS, SHARED_DOCS_ACTIONS } from 'src/constants/actionTypes';

const RestoreDocButton = ({ item }) => {
    const intl= useIntl();
    const dispatch = useDispatch();
    const loginUser = useSelector(state => state.loginIn.user);

    return <Button
        size='small'
        variant='outlined'
        onClick={(event) => {
            dispatch(restoreDoc({ hid: item.hid, parent: item.parent }, (restoredDoc) => {
                let action = {
                    _id: restoredDoc._id,
                    item: restoredDoc,
                    params: { pageBy: 'orderFactor' }
                };

                if (restoredDoc.newSpace) {
                    const SPACE_ACTIONS = {
                        workspace: WORKSPACE_DOCS_ACTIONS,
                        private: PRIVATE_DOCS_ACTIONS,
                        shared: SHARED_DOCS_ACTIONS
                    };

                    action.type = SPACE_ACTIONS[restoredDoc.newSpace].added;
                    action.key = loginUser.workingOrgId;
                } else {
                    action.key = restoredDoc.parent;
                    action.type = SUB_DOCS_ACTIONS.added;
                }

                dispatch(action);
            }, 'trashbin'));
            event.preventDefault();
            event.stopPropagation();
        }}>
        {intl.formatMessage({id: 'restore'})}
    </Button>
}

export default RestoreDocButton;