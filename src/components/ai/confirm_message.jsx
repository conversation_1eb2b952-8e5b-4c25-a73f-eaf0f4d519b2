import { useIntl } from 'react-intl';

import Button from '@mui/material/Button';
import { useEffect, useRef, useState } from 'react';

const ConfirmMessage = ({
  content,
  onCancel,
  onConfirm,
  style,
  optionSwitcher,
  optionEnter
}) => {
  const intl = useIntl();

  const [confirmButtonSelected, setConfirmButtonSelected] = useState();

  useEffect(() => {
    optionSwitcher && setConfirmButtonSelected(prevState => {
      return !prevState
    });
  }, [optionSwitcher])

  useEffect(() => {
    if(!optionEnter) return;

    if(confirmButtonSelected) {
      onConfirm();
    } else {
      onCancel();
    }
  }, [optionEnter])

  return (
    <div
      style={{
        ...style
      }}
    >
      <div style={{fontSize: 14}}>
        {content}
        {
          !content && intl.formatMessage({ id: 'confirm_delete_content' })
        }
      </div>
      <div style={{ display: 'flex', flexDirection: 'row', justifyContent: 'space-evenly', paddingTop: 10, width: '-webkit-fill-available' }}>
        <div className='hoverStand1' style={{ ...styles.button, backgroundColor: confirmButtonSelected ? undefined : '#ddd' }} onClick={onCancel}>{intl.formatMessage({ id: 'cancel' })}</div>
        <div className='hoverStand1' style={{ ...styles.button, backgroundColor: confirmButtonSelected ? '#ddd' : undefined }} onClick={() => {
          onConfirm();
        }}>{intl.formatMessage({ id: 'confirm' })}</div>
      </div>
    </div>
  );
};

const styles = {
  button: {
    border: '1px solid #ddd',
    borderRadius: '15px',
    width: 80,
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    color: '#333',
    padding: 4,
    fontSize: 14,
    fontWeight: 400
  }
}

export default ConfirmMessage;
