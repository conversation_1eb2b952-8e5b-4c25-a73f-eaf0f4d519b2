import { useIntl } from 'react-intl';
import { useDispatch, useSelector } from 'react-redux';
import { useState } from 'react';

import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogTitle from '@mui/material/DialogTitle';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import { OCCUPATION_DIALOG } from 'src/constants/actionTypes';
import { updateUserOccupation } from 'src/actions/ticketAction';

const OccupationDialog = () => {
  const dispatch = useDispatch();
  const intl = useIntl();
  const dialogState = useSelector(state => state.uiState.occupationDialog) || {};
  const [selectedOccupation, setSelectedOccupation] = useState('');
  const [submitting, setSubmitting] = useState(false);

  const handleClose = () => {
    dispatch({ type: OCCUPATION_DIALOG, value: { visible: false } });
  };

  const handleSubmit = () => {
    if (!selectedOccupation) return;

    setSubmitting(true);
    dispatch(updateUserOccupation(
      { occupation: selectedOccupation },
      (user) => {
        setSubmitting(false);
        handleClose();
        if (dialogState.onSuccess) {
          dialogState.onSuccess(user);
        }
      },
      'occupationDialog'
    ));
  };

  const occupationOptions = [
    {
      value: 'student',
      labelKey: 'occupation_student',
      emoji: '🎓',
      descKey: 'occupation_student_desc'
    },
    {
      value: 'teacher',
      labelKey: 'occupation_teacher',
      emoji: '👩‍🏫',
      descKey: 'occupation_teacher_desc'
    },
    {
      value: 'professional',
      labelKey: 'occupation_professional',
      emoji: '💼',
      descKey: 'occupation_professional_desc'
    }
  ];

  return (
    <Dialog
      open={dialogState.visible}
      onClose={dialogState.allowClose !== false ? handleClose : undefined}
      scroll='paper'
      aria-labelledby="occupation-dialog-title"
      aria-describedby="occupation-dialog-description"
      disableEscapeKeyDown={dialogState.allowClose === false}
      maxWidth="md"
    >
      <div style={{ width: 760 }}>
        <DialogTitle id="occupation-dialog-title">
          {intl.formatMessage({ id: 'occupation_dialog_title' })}
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="occupation-dialog-description" style={{ marginBottom: '24px', textAlign: 'center' }}>
            {intl.formatMessage({ id: 'occupation_dialog_desc' })}
          </DialogContentText>
          <Box
            display="flex"
            gap={2}
            justifyContent="center"
            flexWrap="wrap"
            sx={{ minWidth: '600px' }}
          >
            {occupationOptions.map((option) => (
              <Card
                key={option.value}
                sx={{
                  minWidth: 180,
                  maxWidth: 200,
                  cursor: 'pointer',
                  border: selectedOccupation === option.value ? '2px solid #1976d2' : '1px solid #e0e0e0',
                  backgroundColor: selectedOccupation === option.value ? '#f3f8ff' : 'white',
                  transition: 'all 0.2s ease-in-out',
                  '&:hover': {
                    boxShadow: 3,
                    transform: 'translateY(-2px)'
                  }
                }}
                onClick={() => setSelectedOccupation(option.value)}
              >
                <CardContent sx={{ textAlign: 'center', padding: '24px 16px' }}>
                  <Typography
                    variant="h2"
                    component="div"
                    sx={{ fontSize: '48px', marginBottom: '12px' }}
                  >
                    {option.emoji}
                  </Typography>
                  <Typography
                    variant="h6"
                    component="div"
                    sx={{
                      marginBottom: '8px',
                      fontWeight: 600,
                      color: selectedOccupation === option.value ? '#1976d2' : 'inherit'
                    }}
                  >
                    {intl.formatMessage({ id: option.labelKey })}
                  </Typography>
                  <Typography
                    variant="body2"
                    color="text.secondary"
                    sx={{
                      fontSize: '13px',
                      lineHeight: 1.4,
                      minHeight: '40px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}
                  >
                    {intl.formatMessage({ id: option.descKey })}
                  </Typography>
                </CardContent>
              </Card>
            ))}
          </Box>
        </DialogContent>
        <DialogActions style={{ padding: '16px 24px' }}>
          {dialogState.allowClose !== false && (
            <Button onClick={handleClose} disabled={submitting}>
              {intl.formatMessage({ id: 'cancel' })}
            </Button>
          )}
          <Button
            onClick={handleSubmit}
            variant="contained"
            disabled={!selectedOccupation || submitting}
          >
            {submitting ? '...' : intl.formatMessage({ id: 'confirm' })}
          </Button>
        </DialogActions>
      </div>
    </Dialog>
  );
};

export default OccupationDialog;
