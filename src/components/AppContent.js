import React, { Suspense, useEffect } from 'react'
import { Redirect, Route, Switch, useLocation } from 'react-router-dom'
import CircularProgress from '@mui/material/CircularProgress';
// import 'prismjs/themes/prism.css';

// routes config
import routes from '../routes'
import { useDispatch, useSelector } from 'react-redux';

import { ErrorBoundary } from 'react-error-boundary'
import { useIntl } from 'react-intl';
import { APP_UPGRADE_DIALOG, PAGE_HISTORY_ACTIONS } from 'src/constants/actionTypes';

const AppContent = () => {
  const loginUser = useSelector(state => state.loginIn.user);
  const intl = useIntl();
  const dispatch = useDispatch();

  // const docs = useSelector(state => state.docs);

  // const currentLocation = useLocation();
  // const params = new Proxy(new URLSearchParams(currentLocation.search), {
  //   get: (searchParams, prop) => searchParams.get(prop) || '',
  // });

  // const { hid } = params;

  // useEffect(() => {
  //   if (hid && docs?.byId[hid]) {
  //     dispatch({
  //       type: PAGE_HISTORY_ACTIONS.added,
  //       item: {
  //         hid,
  //         title: docs.byId[hid].title,
  //         type: docs.byId[hid].type
  //       }
  //     })
  //   }

  // }, [hid])

  function ErrorFallback({ error, resetErrorBoundary }) {
    dispatch({ type: APP_UPGRADE_DIALOG, value: { visible: true, reason: 'err' } });

    return (
      <div role="alert">
        <p>{intl.formatMessage({ id: 'error_msg' })}</p>
        <pre>{error.message}</pre>
        <button onClick={resetErrorBoundary}>Try again</button>
      </div>
    )
  }

  return (
    <div className="app-content" id='app-content'>
      <Suspense fallback={<CircularProgress />}>
        <Switch>
          {routes.map((route, idx) => {
            return (
              route.component && (
                <Route
                  key={idx}
                  path={route.path}
                  exact={route.exact}
                  name={route.name}
                  render={(props) => {
                    return !!loginUser && loginUser._id ?
                      <ErrorBoundary
                        FallbackComponent={ErrorFallback}
                        onReset={() => {
                          // reset the state of your app so the error doesn't happen again
                        }}
                      >
                        <route.component {...props} />
                      </ErrorBoundary>
                      : <Redirect to={'/login'} />
                  }}
                />
              )
            )
          })}
          <Redirect from="/home" to="/" />
        </Switch>
      </Suspense>
    </div>
  )
}

export default React.memo(AppContent)
