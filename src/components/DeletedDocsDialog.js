import React, { useEffect, useLayoutEffect, useState } from 'react';
import { useIntl } from 'react-intl';
import { Link, useHistory } from 'react-router-dom';

import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';

import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
import ListItemButton from '@mui/material/ListItemButton';
import ListItemText from '@mui/material/ListItemText';

import { useDispatch, useSelector } from 'react-redux';
import { deleteDoc, fetchDeletedDocs, restoreDoc } from 'src/actions/ticketAction'
import { getState, getStateByUser } from 'src/reducers/listReducer';
import ConfirmDialog from './ConfirmDialog';
import RestoreDocButton from './RestoreDocButton';
import { linkToPage } from 'src/utils/PageLinkMaker';


const DeletedDocsDialog = ({ open, handleClose }) => {
  const intl = useIntl();

  const dispatch = useDispatch();
  const history = useHistory();

  const descriptionElementRef = React.useRef(null);
  React.useEffect(() => {
    if (open) {
      dispatch(fetchDeletedDocs({ pageBy: 'orderFactor' }));

      const { current: descriptionElement } = descriptionElementRef;
      if (descriptionElement !== null) {
        descriptionElement.focus();
      }
    }
  }, [open]);

  const deleted_docs = useSelector(state => {
    return getStateByUser(state.deleted_doc_lists, state.loginIn.user);
  });

  const openConfirmDialog = (handleConfirm) => dispatch({
    type: 'CONFIRM_DIALOG', value: {
      visible: true,
      handleConfirm,
      content: intl.formatMessage({ id: 'confirm_delete_doc' })
    }
  });

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      scroll='paper'
      aria-labelledby="scroll-dialog-title"
      aria-describedby="scroll-dialog-description"
    >
      <DialogTitle id="scroll-dialog-title" style={{ display: 'flex', justifyContent: 'space-between' }}>
        <span>{intl.formatMessage({ id: 'trashbin' })}</span>
        {
          !!deleted_docs.items.length &&
          <Button onClick={() => {
            openConfirmDialog(() => {
              dispatch(deleteDoc({}, () => {
                dispatch(fetchDeletedDocs({ pageBy: 'orderFactor' }));
              }, 'trashbin'));
            })
          }}>
            {intl.formatMessage({ id: 'delete_all' })}
          </Button>
        }
      </DialogTitle>
      <DialogContent dividers={true}>
        <List sx={{ width: '100%', width: 488, bgcolor: 'background.paper' }}>
          {deleted_docs.items.length === 0 &&
            <span>{intl.formatMessage({ id: 'nothing_in_trashbin' })}</span>
          }
          {deleted_docs.items.map((item, index) => {
            return <ListItem key={index} component="div" disablePadding>
              <ListItemButton onClick={() => { history.push(linkToPage(item)); handleClose() }}>
                <ListItemText style={{ width: '320px' }} primary={item.title} />
                <RestoreDocButton item={item} />
                <Button
                  size="small"
                  variant='outlined'
                  style={{ marginLeft: '10px' }}
                  onClick={(event) => {
                    openConfirmDialog(() => {
                      dispatch(deleteDoc({ hid: item.hid }, null, 'trashbin'));
                    });

                    event.preventDefault();
                    event.stopPropagation();
                  }}>
                  {intl.formatMessage({ id: 'delete' })}
                </Button>
              </ListItemButton>
            </ListItem>
          })}
        </List>
      </DialogContent>
      <DialogActions>
        <Button onClick={handleClose}>{intl.formatMessage({ id: 'done' })} </Button>
      </DialogActions>
    </Dialog>
  );
};


export default DeletedDocsDialog;
