'use client';

import React from 'react';
import {
  CommentProvider,
  CommentsPositioner,
  SCOPE_ACTIVE_COMMENT,
  getElementAbsolutePosition,
  useActiveCommentNode,
  useFloatingCommentsContentState,
  useFloatingCommentsState,
} from '@udecode/plate-comments';
import { PortalBody, toDOMNode, useEditorRef } from '@udecode/plate-common';

import { cn } from '@/lib/utils';
import { popoverVariants } from '@/components/plate-ui/popover';

import { CommentCreateForm } from './comment-create-form';
import { CommentItem } from './comment-item';
import { CommentReplyItems } from './comment-reply-items';

export type FloatingCommentsContentProps = {
  disableForm?: boolean;
};

export function CommentsPopoverContent(props: FloatingCommentsContentProps) {
  const { disableForm } = props;
  const { ref, activeCommentId, hasNoComment, myUserId } =
    useFloatingCommentsContentState();

  return (
    <CommentProvider
      key={activeCommentId}
      id={activeCommentId}
      scope={SCOPE_ACTIVE_COMMENT}
    >
      <div ref={ref} className={cn(popoverVariants(), 'relative w-[310px]')}>
        {!hasNoComment && (
          <>
            <CommentItem key={activeCommentId} commentId={activeCommentId} />

            <CommentReplyItems />
          </>
        )}

        {!!myUserId && !disableForm && <CommentCreateForm />}
      </div>
    </CommentProvider>
  );
}

export function CommentsPopover() {
  const { loaded, activeCommentId } = useFloatingCommentsState();
  const editor = useEditorRef();
  const [node] = useActiveCommentNode() ?? [];

  if (!loaded || !activeCommentId) return null;

  const DOMNode = toDOMNode(editor, node);
  if (!DOMNode) return;

  const DOMNodePosition = getElementAbsolutePosition(DOMNode);

  return (
    <PortalBody element={document.getElementById('app-content')}>
      {/* <CommentsPositioner className="absolute z-50 w-[418px] pb-4"> */}
      <CommentsPositioner style={{ position: 'absolute', zIndex: 50, paddingBottom: '10px', width: '418px', right: '10px', left: undefined, top: DOMNodePosition.top }}>
        <CommentsPopoverContent />
      </CommentsPositioner>
    </PortalBody>
  );
}
