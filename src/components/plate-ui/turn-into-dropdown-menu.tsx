import React, { useEffect, useState } from 'react';
import { DropdownMenuProps } from '@radix-ui/react-dropdown-menu';
import { ELEMENT_BLOCKQUOTE } from '@udecode/plate-block-quote';
import {
  collapseSelection,
  findNode,
  focusEditor,
  isBlock,
  isCollapsed,
  selectEditor,
  TElement,
  toggleNodeType,
  useEditorState,
} from '@udecode/plate-common';
import { ELEMENT_H1, ELEMENT_H2, ELEMENT_H3 } from '@udecode/plate-heading';
import { ELEMENT_PARAGRAPH } from '@udecode/plate-paragraph';

import { Icons } from '@/components/icons';

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuTrigger,
  useOpenState,
} from './dropdown-menu';
import { ToolbarButton } from './toolbar';
import { useIntl } from 'react-intl';
import { toggleIndentList } from '@udecode/plate-indent-list';
import { unwrapList } from '@udecode/plate-list';

let items = [
  {
    text_id: 'paragraph',
    value: ELEMENT_PARAGRAPH,
    label: 'Paragraph',
    description: 'Paragraph',
    icon: Icons.paragraph,
  },
  {
    text_id: 'cmd_h1',
    value: ELEMENT_H1,
    label: 'Heading 1',
    description: 'Heading 1',
    icon: Icons.h1,
  },
  {
    text_id: 'cmd_h2',
    value: ELEMENT_H2,
    label: 'Heading 2',
    description: 'Heading 2',
    icon: Icons.h2,
  },
  {
    text_id: 'cmd_h3',
    value: ELEMENT_H3,
    label: 'Heading 3',
    description: 'Heading 3',
    icon: Icons.h3,
  },
  {
    text_id: 'cmd_bulleted_list',
    value: 'ul',
    label: 'Bulleted list',
    description: 'Bulleted list',
    icon: Icons.ul,
  },
  {
    text_id: 'cmd_ordered_list',
    value: 'ol',
    label: 'Numbered list',
    description: 'Numbered list',
    icon: Icons.ol,
  },
  {
    text_id: 'cmd_blockquote',
    value: ELEMENT_BLOCKQUOTE,
    label: 'Quote',
    description: 'Quote (⌘+⇧+.)',
    icon: Icons.blockquote,
  },
];

export const useTurnIntoMenuContent = (path, onItemClick) => {
  const editor = useEditorState();
  const intl = useIntl();

  const [menu_items, set_menu_items] = useState(items);
  useEffect(() => {
    set_menu_items(items.map(item => {
      item.label = intl.formatMessage({ id: item.text_id });
      return item;
    }))
  }, [intl]);

  const defaultItem = menu_items.find((item) => item.value === ELEMENT_PARAGRAPH)!;

  let value: string = ELEMENT_PARAGRAPH;
  if (isCollapsed(editor?.selection)) {
    const entry = findNode<TElement>(editor!, {
      match: (n) => isBlock(editor, n),
    });
    if (entry) {
      value =
        menu_items.find((item) => item.value === entry[0].type)?.value ??
        ELEMENT_PARAGRAPH;
    }
  }

  const selectedItem =
    menu_items.find((item) => item.value === value) ?? defaultItem;

  const menuContent = <div>
    <DropdownMenuLabel style={{ color: '#aaa' }}>{intl.formatMessage({ id: 'turn_into' })}</DropdownMenuLabel>
    {menu_items.map(({ value: itemValue, label, icon: Icon }) => (
      <div
        key={itemValue}
        // value={itemValue}
        className="min-w-[180px] hoverStand"

        onClick={() => {
          if (onItemClick) {
            onItemClick(itemValue);
          }

          if (path?.length > 0) {
            selectEditor(editor, { at: path });
          }
          if (itemValue === 'ul' || itemValue === 'ol') {
            // if (settingsStore.get.checkedId(KEY_LIST_STYLE_TYPE)) {
            toggleIndentList(editor, {
              listStyleType: itemValue === 'ul' ? 'disc' : 'decimal',
            });
            // } else if (settingsStore.get.checkedId('list')) {
            //   toggleList(editor, { type });
            // }
          } else {
            unwrapList(editor);
            toggleNodeType(editor, { activeType: itemValue });
          }

          collapseSelection(editor);
          focusEditor(editor);
        }}
      >
        <Icon className="mr-2 h-5 w-5" />
        {label}
      </div>
    ))}
  </div>

  return {
    selectedItem,
    menuContent
  }
}

export function TurnIntoDropdownMenu(props: DropdownMenuProps) {
  const openState = useOpenState();
  const intl = useIntl();

  const { selectedItem, menuContent } = useTurnIntoMenuContent([], null);
  const { icon: SelectedItemIcon, label: selectedItemLabel } = selectedItem;

  return (
    <DropdownMenu modal={false} {...openState} {...props}>
      <DropdownMenuTrigger asChild>
        <ToolbarButton
          pressed={openState.open}
          tooltip={intl.formatMessage({ id: 'turn_into' })}
          isDropdown
          className="lg:min-w-[70px]"
        >
          <SelectedItemIcon className="h-5 w-5 lg:hidden" />
          <span className="max-lg:hidden">{selectedItemLabel}</span>
        </ToolbarButton>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start" className="min-w-0">
        {menuContent}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
