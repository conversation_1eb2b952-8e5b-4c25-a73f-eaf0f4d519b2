import React from 'react';
import {
  MARK_BOLD,
  MARK_CODE,
  MARK_ITALIC,
  MARK_STRIKETHROUGH,
  MARK_UNDERLINE,
} from '@udecode/plate-basic-marks';
import { useEditorReadOnly } from '@udecode/plate-common';

import { Icons, iconVariants } from '@/components/icons';
import { CommentToolbarButton } from '@/components/plate-ui/comment-toolbar-button';
import { LinkToolbarButton } from '@/components/plate-ui/link-toolbar-button';

import { MarkToolbarButton } from './mark-toolbar-button';
import { MoreDropdownMenu } from './more-dropdown-menu';
import { TurnIntoDropdownMenu } from './turn-into-dropdown-menu';
import { ToolbarButton, ToolbarGroup } from './toolbar';
import { useIntl } from 'react-intl';
import { useDispatch } from 'react-redux';
import { AI_ASSISTANT_DIALOG } from '@/constants/actionTypes';
import { Magic } from '@styled-icons/bootstrap/Magic'
import { Wand } from '@styled-icons/fluentui-system-filled/Wand'
import { TextGrammarWand } from '@styled-icons/fluentui-system-regular/TextGrammarWand'
import { ColorDropdownMenu } from './color-dropdown-menu';
import { MARK_BG_COLOR, MARK_COLOR } from '@udecode/plate-font';
import { AlignDropdownMenu } from './align-dropdown-menu';


export function FloatingToolbarButtons() {
  const intl = useIntl();
  const dispatch = useDispatch();
  const readOnly = useEditorReadOnly();

  return (
    <>
      {!readOnly && (
        <>
          <TurnIntoDropdownMenu />

          <MarkToolbarButton nodeType={MARK_BOLD} tooltip={`${intl.formatMessage({ id: 'bold' })} (⌘+B)`}>
            <Icons.bold />
          </MarkToolbarButton>
          <MarkToolbarButton nodeType={MARK_ITALIC} tooltip={`${intl.formatMessage({ id: 'italic' })} (⌘+I)`}>
            <Icons.italic />
          </MarkToolbarButton>
          <MarkToolbarButton
            nodeType={MARK_UNDERLINE}
            tooltip={`${intl.formatMessage({ id: 'underline' })} (⌘+U)`}
          >
            <Icons.underline />
          </MarkToolbarButton>

          {/* <ToolbarGroup> */}
          <AlignDropdownMenu />
          {/* <LineHeightDropdownMenu /> */}

          {/* <IndentListToolbarButton nodeType={ListStyleType.Disc} />
            <IndentListToolbarButton nodeType={ListStyleType.Decimal} /> */}
          {/* </ToolbarGroup> */}
          <ColorDropdownMenu nodeType={MARK_COLOR} tooltip={intl.formatMessage({ id: 'text_color' })}>
            <Icons.color className={iconVariants({ variant: 'toolbar' })} />
          </ColorDropdownMenu>
          <ColorDropdownMenu
            nodeType={MARK_BG_COLOR}
            tooltip={intl.formatMessage({ id: 'highlight_color' })}
          >
            <Icons.bg className={iconVariants({ variant: 'toolbar' })} />
          </ColorDropdownMenu>
        </>
      )}

      <CommentToolbarButton />

      <ToolbarButton
        tooltip={`${intl.formatMessage({ id: 'cmd_ai_optimize' })} `}
        onClick={() => {
          dispatch({
            type: AI_ASSISTANT_DIALOG,
            value: {
              caller: 'plate',
              visible: true,
              trigger: 'ballonToolbar',
              action: 'optimize'
            }
          })
        }}
      >
        <TextGrammarWand color='dodgerblue' />
      </ToolbarButton>
      <ToolbarButton
        tooltip={`${intl.formatMessage({ id: 'cmd_ai_continue_desc' })} `}
        onClick={() => {
          dispatch({
            type: AI_ASSISTANT_DIALOG,
            value: {
              caller: 'plate',
              visible: true,
              trigger: 'ballonToolbar',
              action: 'continue'
            }
          })
        }}
      >
        <Wand color='dodgerblue' />
      </ToolbarButton>
      <ToolbarButton
        tooltip={`${intl.formatMessage({ id: 'askAI' })} `}
        onClick={() => {
          dispatch({
            type: AI_ASSISTANT_DIALOG,
            value: {
              caller: 'plate',
              visible: true,
              trigger: 'ballonToolbar'
            }
          })
        }}
      >
        <Magic color='dodgerblue' />
      </ToolbarButton>
      <MoreDropdownMenu />
    </>
  );
}
