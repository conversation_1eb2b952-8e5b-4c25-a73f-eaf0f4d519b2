import React, { useCallback, useEffect, useState } from 'react'
import { Divider, Popover } from '@mui/material'
import { getEndPoint, removeNodes, setSelection, useEditorRef, useEventPlateId } from '@udecode/plate-common';
import { useIntl } from 'react-intl';

import Tippy from '@tippyjs/react';
import 'tippy.js/themes/light.css';
import { useDispatch, useSelector } from 'react-redux';

import { ELEMENT_BLOCKQUOTE } from '@udecode/plate-block-quote';
import {
    ELEMENT_CODE_BLOCK,
    insertEmptyCodeBlock,
} from '@udecode/plate-code-block';
import {
    focusEditor,
    insertEmptyElement,
    useEditorState,
} from '@udecode/plate-common';
import {
    ELEMENT_H1,
    ELEMENT_H2,
    ELEMENT_H3,
    ELEMENT_H4,
    ELEMENT_H5,
    ELEMENT_H6,
} from '@udecode/plate-heading';
import { ELEMENT_HR } from '@udecode/plate-horizontal-rule';
import {
    KEY_LIST_STYLE_TYPE,
    toggleIndentList,
} from '@udecode/plate-indent-list';
import { ELEMENT_LINK, triggerFloatingLink } from '@udecode/plate-link';
import { toggleList } from '@udecode/plate-list';
import {
    ELEMENT_IMAGE,
    ELEMENT_MEDIA_EMBED,
    insertMedia,
} from '@udecode/plate-media';
import { ELEMENT_PARAGRAPH } from '@udecode/plate-paragraph';
import { ELEMENT_TABLE, insertTable } from '@udecode/plate-table';

import { Icons } from '@/components/icons';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
    useOpenState,
} from '@/components/plate-ui/dropdown-menu';
import { exitBreak } from '../editor/config/components/Toolbars';
import { ELEMENT_MATH_EQUATION_BLOCK } from '@/plate-plugins/math-eqaution';
import { MathFormula } from '@styled-icons/fluentui-system-filled';
import { ELEMENT_SUB_PAGE } from '@/plate-plugins/sub-page-link/createSubPageLinkPlugin';
import { FileSlides, FileText } from '@styled-icons/bootstrap';
import { IMAGE_UPLOAD_DIALOG, LINK_INPUT_DIALOG } from '@/constants/actionTypes';
import { ELEMENT_SLIDES_EMBED, insertSlidesEmbed } from '@/plate-plugins/slides-embeded';

const items = [
    {
        text_id: 'basic_blocks',
        label: 'Basic blocks',
        items: [
            {
                text_id: 'paragraph',
                value: ELEMENT_PARAGRAPH,
                label: 'Paragraph',
                description: 'Paragraph',
                icon: Icons.paragraph,
            },
            {
                text_id: 'cmd_h1',
                value: ELEMENT_H1,
                label: 'Heading 1',
                description: 'Heading 1',
                icon: Icons.h1,
            },
            {
                text_id: 'cmd_h2',
                value: ELEMENT_H2,
                label: 'Heading 2',
                description: 'Heading 2',
                icon: Icons.h2,
            },
            {
                text_id: 'cmd_h3',
                value: ELEMENT_H3,
                label: 'Heading 3',
                description: 'Heading 3',
                icon: Icons.h3,
            },
            // {
            //     value: ELEMENT_H4,
            //     label: 'Heading 4',
            //     description: 'Heading 4',
            //     icon: Icons.h4,
            // },
            // {
            //     value: ELEMENT_H5,
            //     label: 'Heading 5',
            //     description: 'Heading 5',
            //     icon: Icons.h5,
            // },
            // {
            //     value: ELEMENT_H6,
            //     label: 'Heading 6',
            //     description: 'Heading 6',
            //     icon: Icons.h6,
            // },
            {
                text_id: 'cmd_table',
                value: ELEMENT_TABLE,
                label: 'Table',
                description: 'Table',
                icon: Icons.table,
            },
            {
                text_id: 'cmd_bulleted_list',
                value: 'ul',
                label: 'Bulleted list',
                description: 'Bulleted list',
                icon: Icons.ul,
            },
            {
                text_id: 'cmd_ordered_list',
                value: 'ol',
                label: 'Numbered list',
                description: 'Numbered list',
                icon: Icons.ol,
            },
            {
                text_id: 'cmd_blockquote',
                value: ELEMENT_BLOCKQUOTE,
                label: 'Quote',
                description: 'Quote (⌘+⇧+.)',
                icon: Icons.blockquote,
            },
            {
                text_id: 'cmd_hr',
                value: ELEMENT_HR,
                label: 'Divider',
                description: 'Divider (---)',
                icon: Icons.hr,
            },
        ],
    },
    {
        text_id: 'advanced_blocks',
        label: 'Advanced blocks',
        items: [
            {
                text_id: 'cmd_link',
                value: ELEMENT_LINK,
                label: 'Link',
                description: 'Link',
                icon: Icons.link,
            },
            {
                text_id: 'cmd_link_to_page',
                value: ELEMENT_SUB_PAGE,
                label: 'Sub page',
                description: 'Link to page',
                icon: FileText,
            },
        ],
    },
    {
        text_id: 'media',
        label: 'Media',
        items: [
            {
                text_id: 'cmd_codeblock',
                value: ELEMENT_CODE_BLOCK,
                label: 'Code',
                description: 'Code (```)',
                icon: Icons.codeblock,
            },
            {
                text_id: 'cmd_mathblock',
                value: ELEMENT_MATH_EQUATION_BLOCK,
                label: 'Math',
                description: 'Math',
                icon: MathFormula,
            },
            {
                text_id: 'cmd_image',
                value: ELEMENT_IMAGE,
                label: 'Image',
                description: 'Image',
                icon: Icons.image,
            },
            {
                text_id: 'cmd_media_embed',
                value: ELEMENT_MEDIA_EMBED,
                label: 'Embed',
                description: 'Embed',
                icon: Icons.embed,
            },
            {
                text_id: 'service_name_xslides',
                value: ELEMENT_SLIDES_EMBED,
                label: 'Slides',
                description: 'Slides',
                icon: FileSlides,
            },
            // {
            //     value: ELEMENT_EXCALIDRAW,
            //     label: 'Excalidraw',
            //     description: 'Excalidraw',
            //     icon: Icons.excalidraw,
            // },
        ],
    },
];


export const InsertMenu = ({ element }) => {
    const intl = useIntl();
    const dispatch = useDispatch();
    const [anchorEl, setAnchorEl] = React.useState(null);

    const editor = useEditorRef(useEventPlateId());
    const path = editor && editor.children ? [editor.children.findIndex(e => e.id === element.id)] : [0];

    const [menu_items, set_menu_items] = useState(items);
    useEffect(() => {
        set_menu_items(items => {
            return items.map(item => {
                item.label = intl.formatMessage({ id: item.text_id })
                item.items = item.items.map(item_item => {
                    item_item.label = intl.formatMessage({ id: item_item.text_id });
                    return item_item;
                })
                return item;
            });
        })
    }, [intl]);


    const handleClose = () => {
        setAnchorEl(null);
    }

    return <div
        className='hoverStand'
        style={{
            padding: 3
        }}
    >
        <Icons.add
            style={{
                width: 20,
                height: 20,
                color: 'rgba(55, 53, 47, 0.3)',
            }}
            onClick={(e) => {
                // select(editor, path);
                // focusEditor(editor);
                setAnchorEl(e.currentTarget);
            }} />

        <Popover
            open={Boolean(anchorEl)}
            onClose={handleClose}

            anchorEl={anchorEl}
            anchorOrigin={{
                vertical: 'top',
                horizontal: 'right',
            }}
            transformOrigin={{
                vertical: 'top',
                horizontal: 'left',
            }}
        >
            <div style={{ width: '200px', padding: '6px' }}>
                {menu_items.map(({ items: nestedItems, label }, index) => (
                    <React.Fragment key={label}>
                        {index !== 0 && <DropdownMenuSeparator />}

                        <div style={{
                            color: '#aaa',
                            fontSize: 14,

                        }}>{label}</div>
                        {nestedItems.map(
                            ({ value: type, label: itemLabel, icon: Icon }) => (
                                <div
                                    key={type}
                                    className="min-w-[180px] hoverStand"
                                    style={{
                                        display: 'flex',
                                        flexDirection: 'row',
                                        alignItems: 'center',
                                        // fontSize: 14,
                                        // backgroundColor: hoveredIndex === index ? '#eee' : 'white',
                                        padding: '4px 6px 4px 6px',
                                    }}
                                    onClick={async () => {
                                        handleClose();
                                        exitBreak(editor, false, path);

                                        switch (type) {
                                            case ELEMENT_CODE_BLOCK: {
                                                insertEmptyCodeBlock(editor);

                                                break;
                                            }
                                            case ELEMENT_IMAGE: {
                                                // await insertMedia(editor, { type: ELEMENT_IMAGE });
                                                dispatch({ type: IMAGE_UPLOAD_DIALOG, value: { visible: true, trigger: 'doc_editor' } });

                                                break;
                                            }
                                            case ELEMENT_MEDIA_EMBED: {
                                                await insertMedia(editor, {
                                                    type: ELEMENT_MEDIA_EMBED,
                                                });

                                                break;
                                            }
                                            case 'ul':
                                            case 'ol': {
                                                insertEmptyElement(editor, ELEMENT_PARAGRAPH, {
                                                    select: true,
                                                    nextBlock: true,
                                                });

                                                // if (settingsStore.get.checkedId(KEY_LIST_STYLE_TYPE)) {
                                                toggleIndentList(editor, {
                                                    listStyleType: type === 'ul' ? 'disc' : 'decimal',
                                                });
                                                // } else if (settingsStore.get.checkedId('list')) {
                                                //     toggleList(editor, { type });
                                                // }

                                                break;
                                            }
                                            case ELEMENT_TABLE: {
                                                insertTable(editor);

                                                break;
                                            }
                                            case ELEMENT_LINK: {
                                                // triggerFloatingLink(editor, { focused: true });
                                                dispatch({
                                                    type: LINK_INPUT_DIALOG,
                                                    value: {
                                                        visible: true,
                                                        textEnabled: true,
                                                        trigger: 'link'
                                                    }
                                                });

                                                break;
                                            }
                                            case ELEMENT_SLIDES_EMBED: {
                                                insertSlidesEmbed(editor, { hid: '' });

                                                break;
                                            }
                                            default: {
                                                insertEmptyElement(editor, type, {
                                                    select: true,
                                                    nextBlock: true,
                                                });
                                            }
                                        }
                                        if ([ELEMENT_SLIDES_EMBED].includes(type)) {
                                            removeNodes(editor);
                                        } else if (![ELEMENT_LINK, ELEMENT_IMAGE].includes(type)) {
                                            setSelection(editor, { anchor: { path: [path[0] + 1], offset: 0 }, focus: { path: [path[0] + 1], offset: 0 } })
                                            removeNodes(editor)
                                        }

                                        focusEditor(editor);
                                    }}
                                >
                                    <Icon className="mr-2 h-5 w-5" />
                                    {itemLabel}
                                </div>
                            )
                        )}
                    </React.Fragment>
                ))}
            </div>
        </Popover>
    </div>
}