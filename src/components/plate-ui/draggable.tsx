'use client';

import React, { forwardRef } from 'react';
import { ClassNames, PlateElementProps, TEditor } from '@udecode/plate-common';
import {
  DragItemNode,
  useDraggable,
  useDraggableState,
} from '@udecode/plate-dnd';
import { DropTargetMonitor } from 'react-dnd';

import { cn } from '@/lib/utils';
import { Icons } from '@/components/icons';

import { Tooltip, TooltipContent, TooltipTrigger } from './tooltip';
import { Grabbler } from '../editor/config/components/Grabber';
import { useIntl } from 'react-intl';
import { InsertMenu } from './float-insert-dropdown-menu';

export interface DraggableProps
  extends PlateElementProps,
  ClassNames<{
    /**
     * Block and gutter.
     */
    blockAndGutter: string;

    /**
     * Block.
     */
    block: string;

    /**
     * Gutter at the left side of the editor.
     * It has the height of the block
     */
    gutterLeft: string;

    /**
     * Block toolbar wrapper in the gutter left.
     * It has the height of a line of the block.
     */
    blockToolbarWrapper: string;

    /**
     * Block toolbar in the gutter.
     */
    blockToolbar: string;

    blockWrapper: string;

    /**
     * Button to dnd the block, in the block toolbar.
     */
    dragHandle: string;

    /**
     * Icon of the drag button, in the drag icon.
     */
    dragIcon: string;

    /**
     * Show a dropline above or below the block when dragging a block.
     */
    dropLine: string;
  }> {
  /**
   * Intercepts the drop handling.
   * If `false` is returned, the default drop behavior is called after.
   * If `true` is returned, the default behavior is not called.
   */
  onDropHandler?: (
    editor: TEditor,
    props: {
      monitor: DropTargetMonitor<DragItemNode, unknown>;
      dragItem: DragItemNode;
      nodeRef: any;
      id: string;
    }
  ) => boolean;
}

const Draggable = forwardRef<HTMLDivElement, DraggableProps>(
  ({ className, classNames = {}, onDropHandler, ...props }, ref) => {
    const { children, element } = props;

    const state = useDraggableState({ element, onDropHandler });
    const { dropLine, isDragging, isHovered } = state;
    const {
      groupProps,
      droplineProps,
      gutterLeftProps,
      previewRef,
      handleRef,
    } = useDraggable(state);
    const intl = useIntl();

    return (
      <div
        className={cn(
          'relative',
          isDragging && 'opacity-50',
          'group',
          className
        )}
        ref={ref}
        {...groupProps}
      >
        <div
          className={cn(
            'pointer-events-none absolute top-0 pr-5 flex h-full -translate-x-full cursor-text opacity-0 group-hover:opacity-100',
            classNames.gutterLeft
          )}
          {...gutterLeftProps}
        >
          <div className={cn('flex h-[1.5em]', classNames.blockToolbarWrapper)}>
            <div
              className={cn(
                'pointer-events-auto mr-1 flex items-center',
                classNames.blockToolbar
              )}
            >
              <div ref={handleRef} className="w-4" style={{ display: 'flex', flexDirection: 'row', alignItems: 'center' }}>
                <Tooltip>
                  <TooltipTrigger ref={handleRef}>
                    <InsertMenu element={element} />
                  </TooltipTrigger>
                  <TooltipContent side='right' style={{ fontSize: 12, backgroundColor: '#555', color: 'white', zIndex: 10000, padding: '4px', paddingTop: '2px', paddingBottom: '2px' }}>
                    <div>
                      {intl.formatMessage({ id: 'click' })} <span style={{ color: 'rgba(255, 255, 255, 0.45)' }}> {intl.formatMessage({ id: 'to_open_menu' })} </span>
                    </div>
                  </TooltipContent>
                </Tooltip>
                <Tooltip>
                  <TooltipTrigger ref={handleRef}>
                    <Grabbler
                      element={element}
                    />
                  </TooltipTrigger>
                  <TooltipContent side='right' style={{ fontSize: 12, backgroundColor: '#555', color: 'white', zIndex: 10000, padding: '4px', paddingTop: '0px', paddingBottom: '0px' }}>
                    <div>
                      {intl.formatMessage({ id: 'drag' })} <span style={{ color: 'rgba(255, 255, 255, 0.45)' }}> {intl.formatMessage({ id: 'to_move' })} </span>
                    </div>
                    <div>
                      {intl.formatMessage({ id: 'click' })} <span style={{ color: 'rgba(255, 255, 255, 0.45)' }}> {intl.formatMessage({ id: 'to_open_menu' })} </span>
                    </div>
                  </TooltipContent>
                </Tooltip>
              </div>
            </div>
          </div>
        </div>

        <div className={cn('', classNames.blockWrapper)} ref={previewRef}>
          {children}

          {!!dropLine && (
            <div
              className={cn(
                'absolute inset-x-0 h-0.5 opacity-100',
                'bg-ring',
                dropLine === 'top' && '-top-px',
                dropLine === 'bottom' && '-bottom-px',
                classNames.dropLine
              )}
              {...droplineProps}
            />
          )}
        </div>
      </div>
    );
  }
);
Draggable.displayName = 'Draggable';

export { Draggable };
