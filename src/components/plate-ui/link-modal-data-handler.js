import { LINK_INPUT_DIALOG } from "@/constants/actionTypes";
import { useEditorRef } from "@udecode/plate-common";
import { upsertLink } from "@udecode/plate-link";
import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";

export const LinkModalDataHandler = () => {
    const linkInputState = useSelector(state => state.uiState.linkInputDialog) || {};
    const editor = useEditorRef();
    const dispatch = useDispatch();

    useEffect(() => {
        const { data } = linkInputState;
        if (linkInputState.visible || !data) {
            return;
        }

        upsertLink(editor, {
            url: data.link,
            text: data.text, 
            target: '_blank',
            skipValidation: true
        });

        dispatch({
            type: LINK_INPUT_DIALOG,
            value: {
                visible: false
            }
        })
    }, [linkInputState]);

    return null;
}

