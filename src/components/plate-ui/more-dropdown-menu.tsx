import React from 'react';
import { DropdownMenuProps } from '@radix-ui/react-dropdown-menu';
import { MARK_CODE, MARK_STRIKETHROUGH, MARK_SUBSCRIPT, MARK_SUPERSCRIPT } from '@udecode/plate-basic-marks';
import { collapseSelection, focusEditor, getEditorString, toggleMark, toggleNodeType, useEditorState, useMarkToolbarButton, useMarkToolbarButtonState } from '@udecode/plate-common';

import { Icons } from '@/components/icons';

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  useOpenState,
} from './dropdown-menu';
import { ToolbarButton } from './toolbar';
import { useIntl } from 'react-intl';
import { MarkToolbarButton } from './mark-toolbar-button';
import { MARK_KBD } from '@udecode/plate-kbd';
import { LinkToolbarButton } from './link-toolbar-button';
import { useDispatch } from 'react-redux';
import { LINK_INPUT_DIALOG } from '@/constants/actionTypes';
import { OutdentToolbarButton } from './outdent-toolbar-button';
import { useIndentButton, useOutdentButton } from '@udecode/plate-indent';

export function MoreDropdownMenu(props: DropdownMenuProps) {
  const editor = useEditorState();
  const openState = useOpenState();
  const intl = useIntl();
  const dispatch = useDispatch();
  const mark_code_state = useMarkToolbarButtonState({ nodeType: MARK_CODE });
  const { props: code_button_props } = useMarkToolbarButton(mark_code_state);
  const { props: outdent_button_props } = useOutdentButton();
  const { props: indent_button_props } = useIndentButton();

  return (
    <DropdownMenu modal={false} {...openState} {...props}>
      <DropdownMenuTrigger asChild>
        <ToolbarButton pressed={openState.open} tooltip={intl.formatMessage({id: 'more_menu'})}>
          <Icons.more />
        </ToolbarButton>
      </DropdownMenuTrigger>

      <DropdownMenuContent
        align="start"
        className="flex max-h-[500px] min-w-[180px] flex-col gap-0.5 overflow-y-auto"
      >
        {/* <LinkToolbarButton /> */}
        <DropdownMenuItem
          onSelect={() => {
            dispatch({
              type: LINK_INPUT_DIALOG,
              value: {
                visible: true,
                textEnabled: true,
                data: {
                  text: getEditorString(editor, editor.selection)
                },
                trigger: 'link'
              }
            })
            focusEditor(editor);
          }}
        >
          <Icons.link className="mr-2 h-5 w-5" />
          {intl.formatMessage({ id: 'cmd_link' })}
        </DropdownMenuItem>
        <DropdownMenuItem
          onSelect={() => {
            code_button_props?.onClick();
            focusEditor(editor);
          }}
        >
          <Icons.code className="mr-2 h-5 w-5" />
          {intl.formatMessage({ id: 'cmd_codeline' })} (⌘+E)
        </DropdownMenuItem>
        <DropdownMenuItem
          onSelect={() => {
            toggleMark(editor, {
              key: MARK_KBD,
            });
            collapseSelection(editor, { edge: 'end' });
            focusEditor(editor);
          }}
        >
          <Icons.kbd className="mr-2 h-5 w-5" />
          {intl.formatMessage({ id: 'keyboard' })}
        </DropdownMenuItem>
        <DropdownMenuItem
          onSelect={() => {
            toggleMark(editor, {
              key: MARK_SUBSCRIPT,
              clear: MARK_SUPERSCRIPT,
            });
            focusEditor(editor);
          }}
        >
          <Icons.subscript className="mr-2 h-5 w-5" />
          {intl.formatMessage({ id: 'cmd_subscript' })}
          {/* (⌘+,) */}
        </DropdownMenuItem>
        <DropdownMenuItem
          onSelect={() => {
            toggleMark(editor, {
              key: MARK_SUPERSCRIPT,
              clear: MARK_SUBSCRIPT,
            });
            focusEditor(editor);
          }}
        >
          <Icons.superscript className="mr-2 h-5 w-5" />
          {intl.formatMessage({ id: 'cmd_superscript' })}
          {/* (⌘+.) */}
        </DropdownMenuItem>
        <DropdownMenuItem
          onSelect={() => {
            toggleMark(editor, {
              key: MARK_STRIKETHROUGH,
            });
            focusEditor(editor);
          }}
        >
          <Icons.strikethrough className="mr-2 h-5 w-5" />
          {intl.formatMessage({ id: 'strikethrough' })}
        </DropdownMenuItem>

        <DropdownMenuItem
          onSelect={() => {
            indent_button_props?.onClick();
            focusEditor(editor);
          }}
        >
          <Icons.indent className="mr-2 h-5 w-5" />
          {intl.formatMessage({ id: 'cmd_indent' })}
        </DropdownMenuItem>
        <DropdownMenuItem
          onSelect={() => {
            outdent_button_props?.onClick();
            focusEditor(editor);
          }}
        >
          <Icons.outdent className="mr-2 h-5 w-5" />
          {intl.formatMessage({ id: 'cmd_outdent' })}
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
