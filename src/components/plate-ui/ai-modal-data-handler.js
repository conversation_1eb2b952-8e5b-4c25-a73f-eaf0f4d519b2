import { upsertDoc } from "@/actions/ticketAction";
import { AI_ASSISTANT_DIALOG, TEXT_SELECTION, TITLE_SELECTION } from "@/constants/actionTypes";
import { parseMarkdownTable } from "@/utils/SlateMarkdown";
import { exitBreak } from "@udecode/plate-break";
import { getNode, insertBreak, insertN<PERSON>, removeNodes, selectEditor, setSelection, useEditorRef } from "@udecode/plate-common";
import { deserializeMd } from "@udecode/plate-serializer-md";
import { cloneDeep } from "lodash";
import { useCallback, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Editor as SlateEditor, Transforms } from 'slate';
import { ReactEditor } from "slate-react";
import { ELEMENT_MATH_EQUATION_INLINE } from "@/plate-plugins/math-eqaution/createMathEquationInlinePlugin";
import { ELEMENT_MATH_EQUATION_BLOCK as MATH_BLOCK_CONSTANT } from "@/plate-plugins/math-eqaution/createMathEquationBlockPlugin";

export const AIModalDataHandler = () => {
    const aiState = useSelector(state => state.uiState.aiDialog) || {};
    const dispatch = useDispatch();
    const editor = useEditorRef();
    const selection = useSelector(state => state.uiState.selection);

    useEffect(() => {
        if (!editor) {
            return;
        }

        const { response, trigger, doing, operation, usingTitle, visible, caller, path } = aiState;
        if (visible || caller != 'plate' || !response) {
            if (!visible) {
                dispatch({
                    type: TEXT_SELECTION,
                    value: null
                });
                dispatch({
                    type: TITLE_SELECTION,
                    value: false
                });

                ReactEditor.focus(editor);
            }
            return;
        }

        if (trigger === 'blockHandler') {
            setSelection(editor, {
                anchor: SlateEditor.start(editor, path || [editor.children.length - 1]),
                focus: SlateEditor.end(editor, path || [editor.children.length - 1]),
            })
            selectEditor(editor, { at: path || [editor.children.length - 1] });
        }

        if (operation === 'insertBelow' || trigger == 'entrance') {
            insertBelow(doing, response, trigger);
        } else if (operation === 'replace') {
            replaceSelection(doing, response, trigger, usingTitle);
        }

        dispatch({
            type: AI_ASSISTANT_DIALOG,
            value: {
                visible: false
            }
        });

        dispatch({
            type: TEXT_SELECTION,
            value: null
        });
        dispatch({
            type: TITLE_SELECTION,
            value: false
        });

        // ReactEditor.focus(editor);
    }, [aiState])

  /**
     * @description 将包含数学公式的 Markdown 字符串稳健地转换为 Slate 节点数组。
     * 核心思想：
     * 1. 使用正则表达式先将原始 Markdown 字符串中的所有 LaTeX 公式（行内和块级）完整地提取出来，
     * 同时将原字符串中的公式部分替换为唯一的、无特殊字符的占位符。
     * 这样做可以保护 LaTeX 代码不被下一步的 Markdown 解析器破坏。
     * 2. 使用 plate 的 `deserializeMd` 解析这个“净化”后（不含 LaTeX）的 Markdown 字符串。
     * 现在解析器可以安全地处理标题、列表、加粗等，不会误伤公式。
     * 3. 递归遍历第二步生成的 Slate 节点树，查找并替换之前埋入的占位符。
     * 4. 当找到一个占位符时，用它对应的、未经破坏的原始 LaTeX 表达式创建一个新的数学节点
     * （math_inline 或 math_block），并替换掉该占位符文本节点。
     *
     * @param {string} content - 原始的 Markdown 字符串，可能包含 LaTeX。
     * @returns {Node[]} - Slate 节点数组。
     */
    const markdownToSlateWithMath = (content) => {
        // 如果内容为空，直接返回空段落
        if (!content || !content.trim()) {
            return [{ type: 'p', children: [{ text: '' }] }];
        }
        
        // 1. 提取数学公式并生成占位符
        const mathExpressions = [];
        const placeholderPrefix = '___MATH_PLACEHOLDER___';
        const latexRegex = /(\$\$[\s\S]*?\$\$|\$(?!\$)[^$\n]*?\$|\\\[[\s\S]*?\\\])/g;

        const sanitizedContent = content.replace(latexRegex, (match) => {
            const placeholder = `${placeholderPrefix}${mathExpressions.length}`;
            mathExpressions.push(match); // 存储完整的 LaTeX 字符串，包括$$等分隔符
            return placeholder;
        });

        // 2. 解析不含数学公式的 Markdown
        // 注意：这里的 deserializeMd 可能会将占位符包裹在不同的节点中
        let initialNodes = deserializeMd(editor, sanitizedContent);
        
        // 3. 递归替换占位符为数学公式节点
        const replacePlaceholders = (nodes) => {
            const result = [];
            
            for (const node of nodes) {
                if (node.text !== undefined) {
                    // 处理文本节点
                    const text = node.text;
                    const placeholderRegex = new RegExp(`${placeholderPrefix}(\\d+)`, 'g');
                    let lastIndex = 0;
                    let match;
                    
                    while ((match = placeholderRegex.exec(text)) !== null) {
                        // 添加占位符之前的文本
                        if (match.index > lastIndex) {
                            result.push({ ...cloneDeep(node), text: text.substring(lastIndex, match.index) });
                        }
                        
                        // 添加数学公式节点
                        const expressionIndex = parseInt(match[1], 10);
                        const fullExpression = mathExpressions[expressionIndex];
                        
                        // 从完整的表达式中提取出纯公式内容和类型
                        let expression = '';
                        let isBlock = false;

                        if (fullExpression.startsWith('$$') || fullExpression.startsWith('\\[')) {
                            isBlock = true;
                            expression = fullExpression.slice(2, -2).trim();
                        } else { // 行内公式 $...$
                            expression = fullExpression.slice(1, -1).trim();
                        }

                        // **关键修复点**: 保持原始表达式中的所有反斜杠
                        // 因为我们是从原始字符串中提取的，所以 `expression` 变量中的 '\\' 是完好的
                        if (expression) {
                             const mathNode = {
                                type: isBlock ? MATH_BLOCK_CONSTANT : ELEMENT_MATH_EQUATION_INLINE,
                                expression: expression, // 这里的 expression 包含了正确的 '\\'
                                children: [{ text: '' }],
                            };
                            
                            // 对于块级公式，它本身就是一个独立的顶级节点
                            if (isBlock) {
                                result.push(mathNode);
                            } else {
                                // 行内公式是行内元素
                                result.push(mathNode);
                            }
                        }

                        lastIndex = match.index + match[0].length;
                    }
                    
                    // 添加最后一个占位符之后的文本
                    if (lastIndex < text.length) {
                        result.push({ ...cloneDeep(node), text: text.substring(lastIndex) });
                    }

                } else if (node.children) {
                    // 递归处理子节点
                    const newChildren = replacePlaceholders(node.children);
                    
                    // 优化：如果一个段落只包含一个独立的块级公式，则将该公式提升为顶级节点
                    if (node.type === 'p' && newChildren.length === 1 && newChildren[0].type === MATH_BLOCK_CONSTANT) {
                         result.push(newChildren[0]);
                    } else {
                        result.push({ ...node, children: newChildren });
                    }
                } else {
                    result.push(node);
                }
            }

            return result;
        };
        
        let finalNodes = replacePlaceholders(initialNodes);

        // 清理可能产生的空段落
        finalNodes = finalNodes.filter(node => !(node.type === 'p' && node.children.length === 1 && node.children[0].text === ''));
        
        return finalNodes;
    }

    // ... 剩余代码保持不变 ...
    const mdToSlate = (doing, content) => {
        // 使用新的、更稳健的解析函数
        let nodes = markdownToSlateWithMath(content);
        
        // --- 之后的所有代码（表格解析、todos 解析等）可以保持不变 ---
        // --- 因为它们是在节点已经正确生成之后运行的 ---
        
        nodes = nodes.map(node => {
            // 表格解析逻辑... (保持不变)
            if (node.type === 'p') {
                const firstChild = node.children[0];
                if (!firstChild || !firstChild.text) {
                    return node;
                }
                const table = parseMarkdownTable(node.children.map(child=>child.text).join(''));
                if (!table) return node;

                const children = [];
                if (table.headers) {
                    children.push({
                        type: 'tr',
                        children: table.headers.map(header => {
                            return {
                                type: 'th',
                                children: [{
                                    type: 'p',
                                    children: [{
                                        text: header
                                    }]
                                }]
                            }
                        })
                    });
                }

                if (table.rows) {
                    table.rows.forEach(row => {
                        children.push({
                            type: 'tr',
                            children: row.map(col => {
                                return {
                                    type: 'td',
                                    children: [{
                                        type: 'p',
                                        children: [{
                                            text: col
                                        }]
                                    }]
                                }
                            })
                        })
                    })
                }

                node.type = 'table';
                node.children = children;
                return node;
            }
            return node;
        });

        if (doing?.action === 'todos') {
            let listType = 'ul';
            for (let n of nodes) {
                if (['ul', 'ol'].includes(n.type)) {
                    listType = n.type;
                    break;
                }
            }
            nodes = nodes.map(node => {
                if (node.type === listType) {
                    return node.children.map(child => {
                        child.type = 'action_item'
                        if (!child.children) {
                            return null;
                        }
                        if (child.children[0]?.type != 'text') {
                            child.children = child.children[0].children;
                        }
                        return child;
                    })
                }

                return node;
            }).flat().filter(n => !!n);
        }

        return nodes;
    }

    const insertBelow = (doing, response, trigger) => {
        // const newNodes = mdToSlate(doing, (aiResponse || item)?.content || '');
        const newNodes = mdToSlate(doing, response?.content || '');

        if (trigger === 'ballonToolbar') {
            let focus = cloneDeep(editor.selection.focus);
            if (editor.selection.focus.path[0] < editor.selection.anchor.path[0]) {
                focus = cloneDeep(editor.selection.anchor);
            } else if (editor.selection.focus.path[0] === editor.selection.anchor.path[0]) {
                if (editor.selection.focus.offset < editor.selection.anchor.offset) {
                    focus = cloneDeep(editor.selection.anchor);
                }
            }

            setSelection(editor, { focus, anchor: focus });
        }

        try {
            exitBreak(editor, true);
        } catch (err) {
            console.error(err);
        }

        let insertAnchor = cloneDeep(editor.selection)
        if (!insertAnchor) {
            insertAnchor = {
                anchor: {
                    offset: 0,
                    path: [0, 0]
                },
                focus: {
                    offset: 0,
                    path: [0, 0]
                }
            }
        }

        insertNodes(editor, newNodes);
        setSelection(editor, insertAnchor);
        removeNodes(editor);

        setTimeout(() => {
            setSelectionToGeneratedContent(newNodes, insertAnchor.anchor.path);
        }, 300);
    }

    const replaceDocContent = (doing, content, trigger) => {
        if (!content || !selection) {
            return;
        }

        const newNodes = mdToSlate(doing, content);

        if (['blockHandler', 'cmd'].includes(trigger)) {
            let steps = selection.focus.path[0] - selection.anchor.path[0] + 1;
            for (let i = 0; i < steps; i++) {
                removeNodes(editor, { at: selection.anchor.path })
            }

            insertNodes(editor, newNodes, { at: selection.anchor.path });
            setTimeout(() => {
                setSelectionToGeneratedContent(newNodes, selection.anchor.path);
            }, 300);
        } else if (trigger === 'ballonToolbar') {
            Transforms.delete(editor, editor.selection);

            let anchor

            let node = getNode(editor, editor.selection.anchor.path)
            if (!!node?.text) {
                insertBreak(editor);
                anchor = cloneDeep(editor.selection.anchor);
            } else {
                anchor = cloneDeep(editor.selection.anchor);
                removeNodes(editor, { at: editor.selection.anchor.path })
            }

            insertNodes(editor, newNodes);
            setTimeout(() => {
                setSelectionToGeneratedContent(newNodes, anchor.path);
            }, 300);
        }
    }

    const replaceSelection = (doing, response, trigger, usingTitle) => {
        let content = response.content;

        if (!!editor.id && (doing.action === 'title' || usingTitle)) {
            const lines = content?.trim()?.split('\n') || [];

            let title = lines[0]?.replace(/^["“”]+|["“”]+$/g, '') || '';
            if (doing.action !== 'title' && !title.startsWith('# ')) {
                return replaceDocContent(doing, content, trigger);
            }

            title = title.replace(/^#* /, '');

            if (lines.length > 1) {
                content = lines.slice(1).join('\n')?.trim();
            } else {
                content = null;
            }

            if (!title) {
                if (doing.action === 'title') {
                    return;
                } else {
                    return replaceDocContent(doing, content, trigger);
                }
            }

            dispatch(upsertDoc({ data: { doc: { title, hid: editor.id } } }, (updatedDoc) => {
                if (doing.action !== 'title') {
                    replaceDocContent(doing, content, trigger);
                }

                // handleClose(true);
            }))
        } else {
            replaceDocContent(doing, content, trigger);
        }

    }

    const getStartLeaf = useCallback((node) => {
        const { children } = node;

        let startLeaf = {
            path: [0],
        }
        if (children[0].text) {
            startLeaf.text = children[0].text;

        } else {
            let childStartLeaf = getStartLeaf(children[0]);

            startLeaf.path = startLeaf.path.concat(childStartLeaf.path)
            startLeaf.text = childStartLeaf.text
        }

        return startLeaf;
    }, []);

    const getEndLeaf = useCallback((node) => {
        const { children } = node;

        const lastChildIndex = children.length - 1;

        let startLeaf = {
            path: [lastChildIndex],
        }
        if (children[lastChildIndex].text) {
            startLeaf.text = children[lastChildIndex].text;

        } else {
            let childStartLeaf = getStartLeaf(children[lastChildIndex]);

            startLeaf.path = startLeaf.path.concat(childStartLeaf.path)
            startLeaf.text = childStartLeaf.text
        }

        return startLeaf;
    }, []);

    const setSelectionToGeneratedContent = (newNodes, startPath) => {
        let startLeaf = getStartLeaf(newNodes[0]);
        let endLeaf = getEndLeaf(newNodes[newNodes.length - 1]);

        // console.log('anchor..............', startPath, startLeaf, endLeaf)


        let parentPath = startPath.length > 1 ? startPath.slice(0, -1) : cloneDeep(startPath);

        let newSelection = {
            anchor: {
                path: [parentPath].concat(startLeaf.path),
                offset: 0
            },
            focus: {
                path: parentPath.slice(0, -1).concat([parentPath[parentPath.length - 1] + newNodes.length - 1].concat(endLeaf.path)),
                offset: endLeaf.text.length
            }
        }

        try {
            setSelection(editor, newSelection)

        } catch (err) {
            console.error('err in setting selection............', err);
        }
    }

    return null;
}