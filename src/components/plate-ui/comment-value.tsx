'use client';

import React from 'react';
import {
  CommentEditActions,
  CommentEditTextarea,
} from '@udecode/plate-comments';

import { cn } from '@/lib/utils';
import { buttonVariants } from '@/components/plate-ui/button';
import { inputVariants } from '@/components/plate-ui/input';

export function CommentValue() {

  return (
    <div className="my-2 flex flex-col items-end gap-2">
      <CommentEditTextarea className={cn(inputVariants(), 'min-h-[60px]')} />

      <div className="flex space-x-2">
        <CommentEditActions.CancelButton
          className={buttonVariants({ variant: 'outline', size: 'xs' })}
        >
          Cancel
        </CommentEditActions.CancelButton>

        <CommentEditActions.SaveButton
          className={buttonVariants({ variant: 'default', size: 'xs' })}
        >
          Save
        </CommentEditActions.SaveButton>
      </div>
    </div>
  );
}
