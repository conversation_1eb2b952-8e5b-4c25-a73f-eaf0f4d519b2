import { FC } from 'react';
import { ELEMENT_BLOCKQUOTE } from '@udecode/plate-block-quote';
import { ELEMENT_CODE_BLOCK } from '@udecode/plate-code-block';
import { createNodesWithHOC } from '@udecode/plate-common';
import {
  WithDraggableOptions,
  withDraggable as withDraggablePrimitive,
} from '@udecode/plate-dnd';
import {
  ELEMENT_H1,
  ELEMENT_H2,
  ELEMENT_H3,
  ELEMENT_H4,
  ELEMENT_H5,
  ELEMENT_H6,
} from '@udecode/plate-heading';
import { ELEMENT_OL, ELEMENT_TODO_LI, ELEMENT_UL } from '@udecode/plate-list';
import { ELEMENT_PARAGRAPH } from '@udecode/plate-paragraph';

import { Draggable, DraggableProps } from './draggable';
import { ELEMENT_SUB_PAGE } from '@/plate-plugins/sub-page-link/createSubPageLinkPlugin';
import { ELEMENT_DB_EMBED } from '@/plate-plugins/db-embeded';
import { ELEMENT_SLIDES_EMBED } from '@/plate-plugins/slides-embeded';
import { ELEMENT_TABLE } from '@udecode/plate-table';
import { ELEMENT_IMAGE, ELEMENT_MEDIA_EMBED } from '@udecode/plate-media';
import { ELEMENT_MATH_EQUATION_BLOCK } from '@/plate-plugins/math-eqaution';
import { ELEMENT_RIL_HIGHLIGHT } from '@/plate-plugins/ril-highlight/createRILHighlightPlugin';

export const withDraggable = (
  Component: FC,
  options?: WithDraggableOptions<
    Partial<Omit<DraggableProps, 'editor' | 'element' | 'children'>>
  >
) =>
  withDraggablePrimitive<DraggableProps>(Draggable, Component, options as any);

export const withDraggablesPrimitive = createNodesWithHOC(withDraggable);

export const withDraggables = (components: any) => {
  return withDraggablesPrimitive(components, [
    {
      keys: [ELEMENT_PARAGRAPH, ELEMENT_UL, ELEMENT_OL, 
        ELEMENT_SUB_PAGE, ELEMENT_DB_EMBED, ELEMENT_SLIDES_EMBED, ELEMENT_MATH_EQUATION_BLOCK, 
        ELEMENT_RIL_HIGHLIGHT,
        ELEMENT_TABLE, 
        ELEMENT_IMAGE, 
        ELEMENT_MEDIA_EMBED
      ],
      level: 0,
    },
    {
      key: ELEMENT_H1,
      draggableProps: {
        classNames: {
          gutterLeft: 'pr-[30px] pb-1 text-[1.875em]',
          blockToolbarWrapper: 'h-[1.3em]',
        },
      },
    },
    {
      key: ELEMENT_H2,
      draggableProps: {
        classNames: {
          gutterLeft: 'pr-[30px] pb-1 text-[1.5em]',
          blockToolbarWrapper: 'h-[1.3em]',
        },
      },
    },
    {
      key: ELEMENT_H3,
      draggableProps: {
        classNames: {
          gutterLeft: 'pt-[2px] pr-[30px] pb-1 text-[1.25em]',
          blockToolbarWrapper: 'h-[1.3em]',
        },
      },
    },
    {
      keys: [ELEMENT_H4, ELEMENT_H5],
      draggableProps: {
        classNames: {
          gutterLeft: 'pt-[3px] pr-[30px] pb-0 text-[1.1em]',
          blockToolbarWrapper: 'h-[1.3em]',
        },
      },
    },
    {
      keys: [ELEMENT_PARAGRAPH],
      draggableProps: {
        classNames: {
          gutterLeft: 'pt-[4px] pr-[30px] pb-0',
        },
      },
    },
    {
      keys: [ELEMENT_H6, ELEMENT_UL, ELEMENT_OL],
      draggableProps: {
        classNames: {
          gutterLeft: 'pr-[30px] pb-0',
        },
      },
    },
    {
      keys: [ELEMENT_TODO_LI],
      draggableProps: {
        classNames: {
          gutterLeft: 'pt-[4px] pr-[30px]',
        },
      },
    },
    {
      keys: [ELEMENT_BLOCKQUOTE, ELEMENT_RIL_HIGHLIGHT, ELEMENT_TABLE],
      draggableProps: {
        classNames: {
          gutterLeft: 'pr-[30px] pb-0',
        },
      },
    },
    {
      keys: [ELEMENT_IMAGE, ELEMENT_MEDIA_EMBED],
      draggableProps: {
        classNames: {
          gutterLeft: 'pt-3 pr-[30px] pb-0',
        },
      },
    },
    {
      key: ELEMENT_CODE_BLOCK,
      draggableProps: {
        classNames: {
          gutterLeft: 'pt-8 pr-[30px] pb-0',
        },
      },
    },
    {
      keys: [ELEMENT_SUB_PAGE, ELEMENT_DB_EMBED, ELEMENT_MATH_EQUATION_BLOCK, ELEMENT_SLIDES_EMBED],
      draggableProps: {
        classNames: {
          gutterLeft: 'pt-[8px] pr-[30px] pb-0',
        },
      },
    },
  ]);
};
