import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useIntl } from 'react-intl';
import {
  Dialog,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Box,
  Typography,
  CircularProgress
} from '@mui/material';
import { Close, Download, ContentCopy } from '@styled-icons/material';
import { Save } from '@styled-icons/remix-line';
import Draggable from 'react-draggable';
import { useMediaQuery } from 'react-responsive';
import { MOBILE_MEDIA_QUERY } from '../../utils/constants';
import { Tooltip, Popover } from '@mui/material';

// 创建可拖拽的对话框组件
function PaperComponent(props) {
  return (
    <Draggable
      handle="#draggable-dialog-title"
      cancel={'[class*="MuiDialogContent-root"]'}
    >
      <div {...props} />
    </Draggable>
  );
}

const ArtifactEditModal = ({
  visible,
  artifact,
  onSave,
  onClose
}) => {
  const intl = useIntl();
  const isMobile = useMediaQuery({ query: MOBILE_MEDIA_QUERY });
  const [content, setContent] = useState('');
  const [isSavingFile, setIsSavingFile] = useState(false);
  const [isCopying, setIsCopying] = useState(false);
  const textInputRef = useRef(null);

  useEffect(() => {
    if (visible && artifact) {
      setContent(artifact.content || '');
      // 延迟聚焦，确保模态框已完全渲染
      setTimeout(() => {
        if (textInputRef.current) {
          textInputRef.current.focus();
        }
      }, 100);
    }
  }, [visible, artifact]);

  const handleCopyToClipboard = useCallback(async () => {
    if (isCopying) return;

    setIsCopying(true);
    try {
      await navigator.clipboard.writeText(content);
      // 这里可以添加成功提示，比如 toast 或者临时改变按钮状态
    } catch (error) {
      console.error('复制到剪贴板时出错:', error);
      // 降级方案：使用传统的复制方法
      const textArea = document.createElement('textarea');
      textArea.value = content;
      document.body.appendChild(textArea);
      textArea.select();
      try {
        document.execCommand('copy');
      } catch (fallbackError) {
        console.error('降级复制方法也失败:', fallbackError);
      }
      document.body.removeChild(textArea);
    } finally {
      // 短暂延迟后重置状态，给用户视觉反馈
      setTimeout(() => setIsCopying(false), 500);
    }
  }, [content, isCopying]);

  const handleSaveFile = useCallback(async () => {
    if (isSavingFile) return;

    setIsSavingFile(true);
    try {
      const fileExtension = artifact.type.toLowerCase();
      const mimeType = artifact.type === 'SVG' ? 'image/svg+xml' : 'text/plain';
      const blob = new Blob([content], { type: mimeType });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.href = url;
      link.download = `artifact.${fileExtension}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('保存文件时出错:', error);
    } finally {
      setIsSavingFile(false);
    }
  }, [content, artifact.type]);

  const handleSave = () => {
    if (onSave) {
      onSave(content);
    }
    onClose();
  };

  const handleClose = () => {
    setContent('');
    onClose();
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Escape') {
      handleClose();
    } else if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
      handleSave();
    }
  };

  if (!visible || !artifact) {
    return null;
  }

  const getTitle = () => {
    switch (artifact.type) {
      case 'Mermaid':
        return intl.formatMessage({ id: 'edit_mermaid_code' }, { defaultMessage: 'Edit Mermaid Code' });
      case 'SVG':
        return intl.formatMessage({ id: 'edit_svg_code' }, { defaultMessage: 'Edit SVG Code' });
      default:
        return intl.formatMessage({ id: 'edit_code' }, { defaultMessage: 'Edit Code' });
    }
  };

  return (
    <Dialog
      open={visible}
      PaperComponent={PaperComponent}
      aria-labelledby="draggable-dialog-title"
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          position: 'fixed',
          left: '50%',
          top: '20%',
          transform: 'translate(-50%, -50%)',
          bgcolor: 'white',
          borderRadius: '8px',
          boxShadow: '0px 4px 20px rgba(0, 0, 0, 0.15)',
          border: '1px solid #e0e0e0',
          zIndex: 1300,
          width: '80vw',
          maxWidth: '800px',
          height: '70vh',
          maxHeight: '600px',
          minHeight: '400px',
          m: 0,
          display: 'flex',
          flexDirection: 'column'
        },
      }}
      onKeyDown={handleKeyDown}
    >
      {/* 标题栏 */}
      <Box
        id="draggable-dialog-title"
        className="handle"
        sx={{
          cursor: 'move',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          p: 2,
          borderBottom: '1px solid #e0e0e0',
          bgcolor: '#f5f5f5',
          borderRadius: '8px 8px 0 0'
        }}
      >
        <Typography variant="h6" component="div">
          {getTitle()}
        </Typography>
        <div
          style={{
            cursor: 'pointer',
            padding: '4px',
            borderRadius: '4px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}
          onClick={handleClose}
        >
          <Close size={20} />
        </div>
      </Box>

      {/* 内容区域 */}
      <DialogContent
        sx={{
          flex: 1,
          p: 2,
          display: 'flex',
          flexDirection: 'column',
          overflow: 'hidden'
        }}
      >
        <TextField
          inputRef={textInputRef}
          multiline
          fullWidth
          value={content}
          onChange={(e) => setContent(e.target.value)}
          placeholder={
            artifact.type === 'Mermaid'
              ? 'graph TD\n    A[Start] --> B[Process]\n    B --> C[End]'
              : artifact.type === 'SVG'
                ? '<svg width="100" height="100">\n  <circle cx="50" cy="50" r="40" fill="blue" />\n</svg>'
                : 'Enter your code here...'
          }
          sx={{
            flex: 1,
            '& .MuiInputBase-root': {
              height: '100%',
              alignItems: 'flex-start',
              overflow: 'hidden',
              paddingRight: 0,
              paddingTop: '6px',
              paddingBottom: '6px'
            },
            '& .MuiInputBase-input': {
              height: '100% !important',
              fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
              fontSize: '14px',
              lineHeight: '1.5',
              resize: 'none',
              overflow: 'auto !important',
              paddingRight: 0,
              scrollbarWidth: 'thin',
              scrollbarColor: '#ccc #f5f5f5',
              '&::-webkit-scrollbar': {
                width: '8px'
              },
              '&::-webkit-scrollbar-track': {
                background: '#f5f5f5',
                borderRadius: '6px'
              },
              '&::-webkit-scrollbar-thumb': {
                background: '#ccc',
                borderRadius: '6px',
                '&:hover': {
                  background: '#999'
                }
              }
            },
            '& .MuiOutlinedInput-root': {
              '& fieldset': {
                border: '1px solid #e0e0e0',
                borderRadius: '6px'
              },
              '&:hover fieldset': {
                borderColor: '#1976d2'
              },
              '&.Mui-focused fieldset': {
                borderColor: '#1976d2',
                borderWidth: '2px'
              }
            }
          }}
        />
      </DialogContent>

      {/* 按钮区域 */}
      <DialogActions
        sx={{
          p: 2,
          borderTop: '1px solid #e0e0e0',
          bgcolor: '#f9f9f9',
          borderRadius: '0 0 8px 8px',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}
      >
        {/* 左侧图标按钮组 */}
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          {/* 复制按钮 */}
          <Tooltip
            title={intl.formatMessage({ id: 'copy' }, { defaultMessage: 'Copy Code' })}
            placement="top"
          >
            <div
              className="transparent-background"
              style={{
                width: 32,
                height: 32,
                color: isCopying ? '#4caf50' : '#777',
                cursor: isCopying ? 'not-allowed' : 'pointer',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                borderRadius: '4px',
                transition: 'all 0.2s ease',
                backgroundColor: isCopying ? '#e8f5e8' : 'transparent'
              }}
              onMouseEnter={(e) => {
                if (!isCopying) {
                  e.target.style.backgroundColor = '#f0f0f0';
                }
              }}
              onMouseLeave={(e) => {
                if (!isCopying) {
                  e.target.style.backgroundColor = 'transparent';
                }
              }}
              onClick={handleCopyToClipboard}
            >
              {isCopying ? (
                <CircularProgress size={16} color="success" />
              ) : (
                <ContentCopy size={20} />
              )}
            </div>
          </Tooltip>

          {/* 保存文件按钮 */}
          <Tooltip
            title={intl.formatMessage({ id: 'save_file' }, { defaultMessage: 'Save File' })}
            placement="top"
          >
            <div
              className="transparent-background"
              style={{
                width: 32,
                height: 32,
                color: isSavingFile ? '#1976d2' : '#777',
                cursor: isSavingFile ? 'not-allowed' : 'pointer',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                borderRadius: '4px',
                transition: 'all 0.2s ease'
              }}
              onMouseEnter={(e) => {
                if (!isSavingFile) {
                  e.target.style.backgroundColor = '#f0f0f0';
                }
              }}
              onMouseLeave={(e) => {
                if (!isSavingFile) {
                  e.target.style.backgroundColor = 'transparent';
                }
              }}
              onClick={handleSaveFile}
            >
              {isSavingFile ? (
                <CircularProgress size={16} />
              ) : (
                <Save size={20} />
              )}
            </div>
          </Tooltip>
        </Box>

        {/* 右侧主要操作按钮组 */}
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Button
            onClick={handleClose}
            variant="outlined"
            sx={{
              borderColor: '#e0e0e0',
              color: '#666',
              '&:hover': {
                borderColor: '#ccc',
                backgroundColor: '#f5f5f5'
              }
            }}
          >
            {intl.formatMessage({ id: 'cancel' }, { defaultMessage: 'Cancel' })}
          </Button>
          <Button
            onClick={handleSave}
            variant="contained"
            color="primary"
            sx={{
              minWidth: '120px',
              '&:hover': {
                backgroundColor: '#1565c0'
              }
            }}
          >
            {intl.formatMessage({ id: 'save' }, { defaultMessage: 'Save' })} (Ctrl+Enter)
          </Button>
        </Box>
      </DialogActions>
    </Dialog>
  );
};

export default ArtifactEditModal;