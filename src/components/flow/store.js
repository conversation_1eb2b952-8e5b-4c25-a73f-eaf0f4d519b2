import { create } from 'zustand';
import {
    addEdge,
    applyNodeChanges,
    applyEdgeChanges,
    MarkerType
} from '@xyflow/react';
import { cloneDeep } from 'lodash';
import { temporal } from 'zundo';
import equal from 'fast-deep-equal/react';

// this is our useStore hook that we can use in our components to get parts of the store and call actions
const useStoreWithUndo = create()(temporal((set, get) => ({
    nodes: [],
    edges: [],
    onNodesChange: (changes) => {
        set({
            nodes: applyNodeChanges(changes, get().nodes),
        });
    },
    onEdgesChange: (changes) => {
        set({
            edges: applyEdgeChanges(changes, get().edges),
        });
    },
    onConnect: (connection) => {
        set({
            edges: addEdge({ ...connection, type: 'float_edge', markerEnd: { type: MarkerType.ArrowClosed } }, get().edges),
        });
    },

    setNodes: (nodes) => {
        set({ nodes });
    },
    setEdges: (edges) => {
        set({ edges });
    },

    addNode: (newNode) => {
        const nodes = get().nodes || [];
        set({ nodes: [...nodes, newNode] })
        set({ newNode })
    },

    deleteNode: (nodeId) => {
        const nodes = get().nodes;
        const node = nodes.find(node => node.id === nodeId);
        if (node.parentId && nodes.find(n => n.id === node.parentId)) {
            return;
        }

        const filteredNodes = nodes.filter(node => node.id !== nodeId);
        const filteredEdges = (get().edges || []).filter(edge => edge.source !== nodeId && edge.target !== nodeId);

        set({ nodes: filteredNodes });
        set({ edges: filteredEdges })
    },

    getNode: (id) => {
        // 返回浅拷贝而不是深拷贝，提高性能
        const node = get().nodes?.find(node => node.id == id);
        return node ? { ...node } : {};
    },

    updateNode: (updatedNode) => {
        const nodes = get().nodes;
        const index = nodes.findIndex(node => node.id === updatedNode.id);
        if (index > -1) {
            const updatedNodes = [...nodes];
            updatedNodes[index] = updatedNode;
            set({ nodes: updatedNodes })
        }
    },

    updateNodeData: (nodeId, updatedData) => {
        const nodes = get().nodes;
        const nodeIndex = nodes.findIndex(n => n.id == nodeId);

        if (nodeIndex === -1) return;

        // 只拷贝需要更新的节点，而不是整个数组
        const updatedNodes = [...nodes];
        updatedNodes[nodeIndex] = {
            ...nodes[nodeIndex],
            data: {
                ...(nodes[nodeIndex].data || {}),
                ...updatedData
            }
        };

        set({ nodes: updatedNodes })
    },

    addSubNode: (parentId, newNode) => {
        const nodes = get().nodes || [];
        const edges = get().edges || [];

        set({ nodes: [...nodes, newNode] })
        if (parentId) {
            const newEdge = {
                id: newNode.id,
                type: 'float_edge',
                source: parentId,
                target: newNode.id,
                markerEnd: { type: MarkerType.ArrowClosed }
            };
            set({ edges: [...edges, newEdge] });
        }

        set({ newNode })
    },

    getNodeEdges: (nodeId, direction) => {
        return [...(get().edges?.filter(edge => {
            if (direction == 'source') {
                return edge.source == nodeId
            } else if (direction == 'target') {
                return edge.target == nodeId
            }

            return edge.source == nodeId || edge.target == nodeId;

        }) || [])]
    }
}),
    {
        // equality: (pastState, currentState) => equal(pastState, currentState)
        equality: (pastState, currentState) =>
            equal({
                ...pastState,
                nodes: cloneDeep(pastState.nodes)?.map(node => {
                    node.position = undefined;
                    node.width = undefined;
                    node.height = undefined;
                    node.selected = false;
                    node.data = {
                        title: node.data.title
                    }
                    return node
                })
            }, {
                ...currentState,
                nodes: cloneDeep(currentState.nodes)?.map(node => {
                    node.position = undefined;
                    node.width = undefined;
                    node.height = undefined;
                    node.selected = false;
                    node.data = {
                        title: node.data.title
                    }
                    return node
                })
            }),
    }
));

export const useStore = create((set, get) => ({
    tempNotes: [],
    setContextMenu: (context_menu) => {
        set({ context_menu });
    },

    setLang: (lang) => {
        set({ lang })
    },

    setReadOnly: (readOnly) => {
        set({ readOnly })
    },

    setLocked: (locked) => {
        set({ locked })
    },

    setBoardTitle: (title) => {
        set({ boardTitle: title });
    },

    setShareUrl: (shareUrl) => {
        set({ shareUrl })
    },

    setSavingTrigger: (trigger) => {
        set({ savingTrigger: trigger })
    },

    setTempLlmModel: (model) => {
        set({ tempLlmModel: model })
    },

    setTempNotes: (notes) => {
        set({ tempNotes: notes })
    },

    addTempNote: (note) => {
        let notes = cloneDeep(get().tempNotes);

        set({ tempNotes: (notes || []).concat(note) })
    },

    deleteTempNote: (note) => {
        let notes = cloneDeep(get().tempNotes);

        set({ tempNotes: notes.filter(n => n.id !== note.id) })
    }
}))

export default useStoreWithUndo;
