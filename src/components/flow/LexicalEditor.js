/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */
import { AutoFocusPlugin } from '@lexical/react/LexicalAutoFocusPlugin';
import { LexicalComposer } from '@lexical/react/LexicalComposer';
import { ContentEditable } from '@lexical/react/LexicalContentEditable';
import LexicalErrorBoundary from '@lexical/react/LexicalErrorBoundary';
import { HistoryPlugin } from '@lexical/react/LexicalHistoryPlugin';
import { RichTextPlugin } from '@lexical/react/LexicalRichTextPlugin';
import { LinkPlugin } from '@lexical/react/LexicalLinkPlugin'
import { MarkdownShortcutPlugin } from '@lexical/react/LexicalMarkdownShortcutPlugin'
import { OnChangePlugin } from '@lexical/react/LexicalOnChangePlugin'
import { ListPlugin } from '@lexical/react/LexicalListPlugin'
import { CheckListPlugin } from '@lexical/react/LexicalCheckListPlugin'
import { HorizontalRulePlugin } from '@lexical/react/LexicalHorizontalRulePlugin'
import { TabIndentationPlugin } from '@lexical/react/LexicalTabIndentationPlugin'
// import { TablePlugin } from '@lexical/react/LexicalTablePlugin'
import ToolbarPlugin from '@/components/lexical-plugins/ToolbarPlugin';
import ContextMenuPlugin from '@/components/lexical-plugins/ContextMenuPlugin';

import {
    $convertFromMarkdownString,
    $convertToMarkdownString,
    TRANSFORMERS,
} from '@lexical/markdown';

import LexicalTheme from './LexicalTheme';
import '@/components/lexical-plugins/index.css'
import './LexicalEditor.css'

// import type {Klass, LexicalNode} from 'lexical';
import { CodeHighlightNode, CodeNode } from '@lexical/code';
import { HashtagNode } from '@lexical/hashtag';
import { AutoLinkNode, LinkNode } from '@lexical/link';
import { ListItemNode, ListNode } from '@lexical/list';
import { MarkNode } from '@lexical/mark';
import { OverflowNode } from '@lexical/overflow';
import { HorizontalRuleNode } from '@lexical/react/LexicalHorizontalRuleNode';
import { HeadingNode, QuoteNode } from '@lexical/rich-text';
import { TableCellNode, TableNode, TableRowNode } from '@lexical/table';
import { $getSelection, SELECTION_CHANGE_COMMAND } from 'lexical';
import { useEffect, useState } from 'react';
import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';

import { useStore } from './store';
import { useShallow } from 'zustand/react/shallow';

// import {EmojiNode} from '@lexical/react/No';
// import {EquationNode} from './EquationNode';
// import {ImageNode} from './ImageNode';

const Nodes = [
    HeadingNode,
    ListNode,
    ListItemNode,
    QuoteNode,
    CodeNode,
    TableNode,
    TableCellNode,
    TableRowNode,
    HashtagNode,
    CodeHighlightNode,
    AutoLinkNode,
    LinkNode,
    OverflowNode,
    //   ImageNode,
    //   EmojiNode,
    //   EquationNode,
    HorizontalRuleNode,
    MarkNode,
];

function Placeholder() {
    return <div className="editor-placeholder">Type note here</div>;
}

// const OnSelectionChangePlugin = () => {
//     const [editor] = useLexicalComposerContext();
//     editor.registerCommand(SELECTION_CHANGE_COMMAND, () => {
//         const selection = $getSelection();

//         if (!selection?.getTextContent()) return;

//         onContextMenu({ type: 'text_selection', event, selected_text: text, nodeId, setContextMenu })
//     }, 4);
// }

export default function App({ id, value, onChange, color_theme, hovered, getSelectionText, onContextMenu, isMobile }) {
    const { setContextMenu } = useStore(useShallow(state => ({
        setContextMenu: state.setContextMenu,
    })))

    const [content, setContent] = useState(value);

    function onEditorChange(editorState) {
        editorState.read(() => {
            const markdown = $convertToMarkdownString(TRANSFORMERS);
            // onChange(markdown);
            setContent(markdown);
        });
    }

    useEffect(() => {
        !hovered && value !== content && onChange(content)
    }, [hovered])

    return (
        <LexicalComposer
            initialConfig={{
                namespace: id,
                nodes: Nodes,
                editorState: () => $convertFromMarkdownString(content || '', TRANSFORMERS),
                // Handling of errors during update
                onError(error) {
                    throw error;
                },
                // The editor theme
                theme: LexicalTheme
            }}
        >
            <div
                className={isMobile ? "editor-container" : "editor-container nodrag"}
                style={{
                    backgroundColor: color_theme.content_bg
                }}
            >
                <div style={{
                    position: 'absolute',
                    bottom: 'calc(100%)',
                    left: -6
                }}>
                    <ToolbarPlugin setIsLinkEditMode={true} visible={hovered} />
                </div>
                <div className="editor-inner">
                    <RichTextPlugin
                        contentEditable={
                            // <div className="editor" ref={onRef}>
                            <ContentEditable
                                className="editor-input"
                                onMouseUp={(event) => {
                                    const text = getSelectionText();

                                    if (text) {
                                        onContextMenu({ type: 'text_selection', event, selected_text: text, nodeId: id, setContextMenu })
                                    }
                                }}
                                onMouseDown={(event) => {
                                    window.getSelection().removeAllRanges();
                                    setContextMenu(null);
                                }}
                                onClick={(event) => {
                                    const text = getSelectionText();

                                    if (text) {
                                        event.preventDefault();
                                        event.stopPropagation();
                                    }
                                }}
                            />
                            // </div>
                        }
                        placeholder={<Placeholder />}
                        ErrorBoundary={LexicalErrorBoundary}
                    />
                    <OnChangePlugin onChange={onEditorChange} ignoreSelectionChange={true} />
                    {/* <OnSelectionChangePlugin /> */}
                    <HistoryPlugin />
                    <AutoFocusPlugin />
                    <MarkdownShortcutPlugin transformers={TRANSFORMERS} />
                    {/* <CodeHighlightPlugin /> */}
                    <ListPlugin />
                    <CheckListPlugin />
                    {/* <TableCellResizer /> */}
                    {/* <ListMaxIndentLevelPlugin maxDepth={7} /> */}
                    {/* <TablePlugin
                        hasCellMerge={tableCellMerge}
                        hasCellBackgroundColor={tableCellBackgroundColor}
                    />
                    <TableCellResizer /> */}
                    <LinkPlugin />
                    <ContextMenuPlugin />
                    {/* <LexicalClickableLinkPlugin disabled={true} /> */}
                    <HorizontalRulePlugin />
                    <TabIndentationPlugin />
                    {/* <CollapsiblePlugin /> */}
                    {/* <EquationsPlugin />
                    <TabFocusPlugin />
                    <TabIndentationPlugin />
                    {floatingAnchorElem && !isSmallWidthViewport && (
                        <>
                            <FloatingLinkEditorPlugin
                                anchorElem={floatingAnchorElem}
                                isLinkEditMode={isLinkEditMode}
                                setIsLinkEditMode={setIsLinkEditMode}
                            />
                            <TableCellActionMenuPlugin
                                anchorElem={floatingAnchorElem}
                                cellMerge={true}
                            />
                            <FloatingTextFormatToolbarPlugin
                                anchorElem={floatingAnchorElem}
                                setIsLinkEditMode={setIsLinkEditMode}
                            />
                        </>
                    )} */}

                </div>
            </div>
        </LexicalComposer>
    );
}