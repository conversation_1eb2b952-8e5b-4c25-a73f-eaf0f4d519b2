import React, { useState, useEffect, useRef } from 'react';
import { useIntl } from 'react-intl';
import { 
  Dialog, 
  DialogContent, 
  DialogActions, 
  Button, 
  TextField,
  Box,
  Typography
} from '@mui/material';
import { Close } from '@styled-icons/material';
import Draggable from 'react-draggable';

// 创建可拖拽的对话框组件
function PaperComponent(props) {
  return (
    <Draggable
      handle="#draggable-input-dialog-title"
      cancel={'[class*="MuiDialogContent-root"]'}
    >
      <div {...props} />
    </Draggable>
  );
}

const UserInputModal = ({
  visible, 
  title,
  placeholder,
  onConfirm, 
  onCancel 
}) => {
  const intl = useIntl();
  const [userInput, setUserInput] = useState('');
  const textInputRef = useRef(null);

  useEffect(() => {
    if (visible) {
      setUserInput('');
      // 延迟聚焦，确保模态框已完全渲染
      setTimeout(() => {
        if (textInputRef.current) {
          textInputRef.current.focus();
        }
      }, 100);
    }
  }, [visible]);

  const handleConfirm = () => {
    if (userInput.trim()) {
      onConfirm(userInput.trim());
    }
    handleClose();
  };

  const handleClose = () => {
    setUserInput('');
    onCancel();
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Escape') {
      handleClose();
    } else if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
      handleConfirm();
    }
  };

  if (!visible) {
    return null;
  }

  return (
    <Dialog
      open={visible}
      PaperComponent={PaperComponent}
      aria-labelledby="draggable-input-dialog-title"
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          position: 'fixed',
          left: '50%',
          top: '30%',
          transform: 'translate(-50%, -50%)',
          bgcolor: 'white',
          borderRadius: '12px',
          boxShadow: '0px 8px 32px rgba(0, 0, 0, 0.12)',
          border: '1px solid #e0e0e0',
          zIndex: 1300,
          width: '90vw',
          maxWidth: '600px',
          minHeight: '300px',
          m: 0,
          display: 'flex',
          flexDirection: 'column'
        },
      }}
      onKeyDown={handleKeyDown}
    >
      {/* 标题栏 */}
      <Box
        id="draggable-input-dialog-title"
        className="handle"
        sx={{
          cursor: 'move',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          p: 2,
          pt: 1,
          pb: 1,
        }}
      >
        <Typography variant="h6" component="div" sx={{ 
          fontWeight: 600,
          color: '#333',
          fontSize: '18px'
        }}>
          {title || intl.formatMessage({ id: 'user_input' }, { defaultMessage: 'User Input' })}
        </Typography>
        <div
          style={{
            cursor: 'pointer',
            padding: '8px',
            borderRadius: '8px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            transition: 'background-color 0.2s',
            backgroundColor: 'transparent'
          }}
          onMouseEnter={(e) => e.target.style.backgroundColor = '#f5f5f5'}
          onMouseLeave={(e) => e.target.style.backgroundColor = 'transparent'}
          onClick={handleClose}
        >
          <Close size={20} color="#666" />
        </div>
      </Box>

      {/* 内容区域 */}
      <DialogContent
        sx={{
          flex: 1,
          p: 2,
          pt: 1,
          pb: 1,
          display: 'flex',
          flexDirection: 'column'
        }}
      >
        <TextField
          inputRef={textInputRef}
          multiline
          fullWidth
          rows={6}
          value={userInput}
          onChange={(e) => setUserInput(e.target.value)}
          placeholder={placeholder || intl.formatMessage({ id: 'please_enter_your_requirements' }, { defaultMessage: 'Please enter your modification requirements...' })}
          variant="outlined"
          sx={{
            '& .MuiOutlinedInput-root': {
              borderRadius: '6px',
              fontSize: '14px',
              lineHeight: '1.5',
              paddingTop: '6px',
              paddingBottom: '6px',
              paddingRight: 0,
              '& fieldset': {
                borderColor: '#e0e0e0'
              },
              '&:hover fieldset': {
                borderColor: '#1976d2'
              },
              '&.Mui-focused fieldset': {
                borderColor: '#1976d2',
                borderWidth: '1px'
              }
            },
            '& .MuiInputBase-input': {
              paddingRight: 0, // 移除右侧内边距，让滚动条贴边
            }
          }}
        />
        
        <Typography 
          variant="caption" 
          sx={{ 
            mt: 1, 
            color: '#666',
            fontSize: '12px'
          }}
        >
          {intl.formatMessage({ id: 'input_hint' }, { defaultMessage: 'Tip: Press Ctrl+Enter to confirm quickly, press Esc to cancel' })}
        </Typography>
      </DialogContent>

      {/* 按钮区域 */}
      <DialogActions
        sx={{
          p: 2,
          gap: 2
        }}
      >
        <Button 
          onClick={handleClose}
          variant="outlined"
          sx={{ 
            borderRadius: '8px',
            textTransform: 'none',
            minWidth: '80px',
            borderColor: '#e0e0e0',
            color: '#666',
            '&:hover': {
              borderColor: '#ccc',
              backgroundColor: '#f9f9f9'
            }
          }}
        >
          {intl.formatMessage({ id: 'cancel' }, { defaultMessage: 'Cancel' })}
        </Button>
        <Button 
          onClick={handleConfirm}
          variant="contained"
          disabled={!userInput.trim()}
          sx={{ 
            borderRadius: '8px',
            textTransform: 'none',
            minWidth: '80px',
            backgroundColor: '#1976d2',
            '&:hover': {
              backgroundColor: '#1565c0'
            },
            '&:disabled': {
              backgroundColor: '#e0e0e0',
              color: '#999'
            }
          }}
        >
          {intl.formatMessage({ id: 'confirm' }, { defaultMessage: 'Confirm' })}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default UserInputModal;
