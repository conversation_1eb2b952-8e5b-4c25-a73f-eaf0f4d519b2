import React, { memo } from 'react';
import { <PERSON><PERSON>, Position } from '@xyflow/react';

export default memo(({ data }) => {
  return (
    <>
      {data.label}
      <Handle type="source" position={Position.Top} id="a" />
      <Handle type="source" position={Position.Right} id="b" />
      <Handle type="source" position={Position.Bottom} id="c" />
      <Handle type="source" position={Position.Left} id="d" />
    </>
  );
});