import * as React from 'react';
import { useIntl } from 'react-intl';
import useStoreWithUndo from './store';
import { useShallow } from 'zustand/react/shallow';
import { useMediaQuery } from 'react-responsive';
import ContentEditable from 'react-contenteditable';
import TurndownService from 'turndown';
import { ArrowDownS, ArrowRightS, ArrowUpS } from '@styled-icons/remix-line'
import { selector } from './AINode';
import { Tooltip } from '@mui/material'
import { DropDownMenu } from './DropDownMenu';
import { PromptTypeDropdownAndActionButton, doableAfterTriggeredTypes } from './PromptTypeDropdownAndActionButton';
import ReactGA from "react-ga4";
import { MOBILE_MEDIA_QUERY } from '@/utils/constants';
import { useSelector } from 'react-redux';
import { isYoutube } from '@/utils/videoUtils';

const urlRegex = require('url-regex');

const Topic_Types = [{
  id: 'thematic_learning'
}, {
  id: 'personal_growth'
}, {
  id: 'decision_analysis'
}, {
  id: 'startup_idea'
}, {
  id: 'bussiness_analysis'
}, {
  id: 'investment_analysis'
}, {
  id: 'product_innovation'
}, {
  id: 'solutions_design'
}, {
  id: 'project_planning'
}, {
  id: 'marketing_strategies'
}, {
  id: 'branding_building'
}, {
  id: 'advertisiing_compaign_ideas'
}, {
  id: 'root_cause_analysis'
}, {
  id: 'team_management'
}, {
  id: 'operation_optimization'
}, {
  id: 'innovative_ideas'
}, {
  id: 'event_planning'
}, {
  id: 'meeting_disucssions'
}, {
  id: 'travel_experiences'
}, {
  id: 'writing_ideas'
}, {
  id: 'research_topics',
}, {
  id: 'curriculum_design',
}, {
  id: 'social_issue_solutions'
}, {
  id: 'other'
}]

const PROMPT_EXAMPLES = {
  brainstorming: [
    'Critical thinking',
    'Ideas for reducing stress',
    'Marketing strategies for a new product'
  ],
  brainstorming_decision_analysis: [
    'Should I buy an electric car?',
    'Which is better for future career growth: working or pursuing further education?'
  ],
  dynamic: [
    'Explain photosynthesis',
    'Impact of AI on jobs',
    'What is emotional intelligence?'
  ],
  ask: [
    'Explain blockchain simply',
    'How does machine learning work?',
    'What is the future of education?'
  ],
  search: [
    'AI trends 2025',
    'Climate change solutions',
    'Remote work best practices'
  ],
  breakdown: [
    'DNA structure',
    'Weight loss',
    'Content marketing'
  ],
  todos: [
    'Plan a 14 day trip to New Zealand',
    'Start a side project',
    'Learn to play the guitar'
  ],
  link: [
    'https://funblocks.net/aiflow.html',
    'https://funblocks.net/'
  ],
  book: [
    'Thinking, Fast and Slow',
    'The Little Prince',
    'The Dialogues Of Plato'
  ],
  video: [
    'https://youtu.be/tPjuWOjpJIs',
    'https://youtu.be/pD540sYUaLw'
  ]
};

const Examples = ({ examples, onSelect, color_theme }) => {
  if (!examples.length) return null;

  return (
    <div
      style={{
        position: 'absolute',
        top: -45,
        left: 0,
        right: 0,
        display: 'flex',
        gap: 8,
        // flexWrap: 'wrap'
      }}
    >
      {examples.map((example, index) => (
        <div
          key={index}
          onClick={() => onSelect(example)}
          style={{
            fontSize: 12,
            padding: '2px 8px',
            borderRadius: 12,
            backgroundColor: color_theme.content_bg,
            border: `1px solid ${color_theme.border}`,
            cursor: 'pointer',
            whiteSpace: 'nowrap',
            color: color_theme.text_secondary
          }}
          className="hover-highlight"
        >
          {example}
        </div>
      ))}
    </div>
  );
};

export const PromptNode = ({ nodeId, useCase, selected, defaultValue, onKeyDown, onChange, onQueryTypeChange, queryTypeOptions, handleConfirm, saveUserInput, triggered, loading, standAlone, queryType, color_theme, queryTypeChangable, callAI, hasAIMenu }) => {
  const intl = useIntl();
  const [inputChanged, setInputChanged] = React.useState();
  const [isValid, setIsValid] = React.useState();
  const [menuDroppedDown, setMenuDroppedDown] = React.useState();
  const [showOptions, setShowOptions] = React.useState();
  const [scenario, setScenario] = React.useState();
  const [scenarioId, setScenarioId] = React.useState();
  const [thinking_model, set_thinking_model] = React.useState();

  const textInputRef = React.useRef();
  const scenarioInputRef = React.useRef();
  const thinkingModelInputRef = React.useRef();
  const isMobile = useMediaQuery(MOBILE_MEDIA_QUERY);
  const [showInputTips, setShowInputTips] = React.useState();
  const [isMultiline, setIsMultiline] = React.useState(false);
  const [initialHeight, setInitialHeight] = React.useState(null);
  const [input, setInput] = React.useState();
  const [isInputZh, setIsInputZh] = React.useState();

  const Thinking_Models = React.useMemo(() => {
    if (scenarioId === 'decision_analysis') {
      return [{
        id: 'pros_cons'
      }, {
        id: 'swot_analysis'
      }, {
        id: 'first_principle'
      }, {
        id: 'six_thinking_hats'
      }, {
        id: 'cost_benefit_analysis'
      }, {
        id: 'decision_tree'
      }, {
        id: 'decision_matrix'
      }, {
        id: 'casual_chain'
      }, {
        id: 'systems_thinking'
      }, {
        id: 'rephrazing'
      }, {
        id: 'changing_perspectives'
      }, {
        id: 'reverse_thinking'
      }, {
        id: 'other'
      }];
    }

    return [{
      id: 'swot_analysis'
    }, {
      id: 'business_model_canvas'
    }, {
      id: 'first_principle'
    }, {
      id: 'fivew1h_method'
    }, {
      id: 'scamper_method'
    }, {
      id: 'six_thinking_hats'
    }, {
      id: 'pdca'
    }, {
      id: 'systems_thinking'
    }, {
      id: 'steep_analysis'
    }, {
      id: 'five_forces'
    }, {
      id: 'four_p'
    }, {
      id: 'triz'
    }, {
      id: 'rephrazing'
    }, {
      id: 'learning_pyramid',
    }, {
      id: 'kwl'
    }, {
      id: 'changing_perspectives'
    }, {
      id: 'reverse_thinking'
    }, {
      id: 'role_playing'
    }, {
      id: 'mckinsey_7S_framework'
    }, {
      id: 'value_proposition_canvas'
    }, {
      id: 'other'
    }]
  }, [scenarioId]);

  const { updateNodeData } = useStoreWithUndo(
    useShallow(selector)
  );
  const flow_settings = useSelector(state => state.uiState.flow_settings);

  const toggleOptionsButton = React.useMemo(() => !triggered && ['brainstorming'].includes(queryType) && !useCase, [triggered, queryType, useCase]);

  React.useEffect(() => {
    setScenarioId(useCase);
    setScenario(Selectable_TopicTypes.find(item => item.value === useCase)?.label);
  }, [useCase])

  const optionsButton = React.useMemo(() => {
    return toggleOptionsButton && (
      <Tooltip
        title={intl.formatMessage({ id: 'advanced_settings' })}
        placement='bottom'
      >
        <div
          className='transparent-background'
          style={{
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            padding: 4,
            color: '#777',
            cursor: 'pointer'
          }}
          onClick={() => {
            setShowOptions(prevState => !prevState);

            setTimeout(() => {
              textInputRef.current?.focus();

              const range = document.createRange();
              range.selectNodeContents(textInputRef.current);
              range.collapse(false);
              const selection = window.getSelection();
              selection.removeAllRanges();
              selection.addRange(range);
            }, 100);
          }}
        >
          {showOptions ? (isMultiline ? <ArrowUpS size={16} /> : <ArrowDownS size={16} />) : <ArrowRightS size={16} />}
        </div>
      </Tooltip>
    )
  }, [toggleOptionsButton, showOptions, isMultiline])

  React.useEffect(() => {
    setTimeout(() => {
      const editableDiv = textInputRef.current;
      if (editableDiv) {
        editableDiv.focus();
      }
    }, 100);

  }, [textInputRef?.current]);

  React.useEffect(() => {
    if (defaultValue && !input) {
      setInput(defaultValue.replaceAll('\n', '<br>'))
    }
  }, [defaultValue, input])

  React.useEffect(() => {
    const turndownService = new TurndownService();
    const mrkd = turndownService.turndown(input || '')?.trim();

    let isValid = !!mrkd?.trim();

    const isValidUrl = urlRegex({ exact: true }).test(mrkd?.trim());
    if (queryType === 'link') {
      isValid = isValidUrl;
      if (isValid && isYoutube(mrkd)) {
        handleQueryTypeChange('video');
      }
    } else if (queryType === 'video') {
      isValid = isValidUrl && isYoutube(mrkd);
    } else if (queryType !== 'note' && isValidUrl) {
      handleQueryTypeChange(isYoutube(mrkd) ? 'video' : 'link');
    }

    setIsValid(isValid);
  }, [input, queryType]);

  React.useEffect(() => {
    const turndownService = new TurndownService();
    const mrkd = turndownService.turndown(input || '')?.trim();

    setShowInputTips(selected && queryType != 'link' && (queryType != 'dynamic' && mrkd?.length > 3 || queryType == 'dynamic' && !hasAIMenu));
  }, [input, selected, queryType, hasAIMenu]);

  const handlePaste = React.useCallback((e) => {
    e.preventDefault();

    const text = e.clipboardData.getData('text/plain');
    document.execCommand('insertText', false, text);
  }, []);

  const Selectable_TopicTypes = React.useMemo(() => Topic_Types.map(item => {
    return {
      value: item.id,
      label: intl.formatMessage({ id: item.id })
    }
  }), [intl])

  const Selectable_Thinking_Models = React.useMemo(() => Thinking_Models.map(item => {
    return {
      value: item.id,
      label: intl.formatMessage({ id: item.id })
    }
  }), [intl, Thinking_Models])

  React.useEffect(() => {
    if (textInputRef.current && initialHeight === null) {
      setInitialHeight(textInputRef.current.clientHeight);
    }
  }, []);

  React.useEffect(() => {
    if (!input || queryType == 'link') {
      setIsMultiline(false);
      return;
    }

    if (textInputRef.current && initialHeight !== null && !isInputZh) {
      const currentHeight = textInputRef.current.clientHeight;
      setIsMultiline(prevState => !!prevState || currentHeight > initialHeight);
    }
  }, [input, initialHeight, queryType, isInputZh]);

  const handleInputChange = React.useCallback((event) => {
    let value = event.currentTarget.textContent || event.currentTarget.innerText;

    if (value == '\n') {
      value = '';
    }

    setInputChanged(true);
    !!onChange && onChange(value);
    setInput(['new_task', 'new_sub_topic', 'add_improve_plan', 'link'].includes(queryType) || !value?.trim() ? value : event.target.value);
  }, [queryType]);

  const handleInputConfirm = React.useCallback((eventType) => {
    const content = textInputRef.current.innerText || textInputRef.current.textContent;
    handleConfirm(eventType, content, (showOptions || !!useCase) && {
      scenario,
      thinking_model
    });

    ReactGA.event({ category: 'promptNode', action: 'trigger_ai_enter', label: queryType });

    setInput('');
  }, [input, showOptions, queryType, scenario, thinking_model, textInputRef])

  const handleQueryTypeChange = React.useCallback((queryType, value) => {
    if (onQueryTypeChange) {
      return onQueryTypeChange(queryType);
    }

    if (queryType === 'note') {
      const turndownService = new TurndownService();
      const mrkd = turndownService.turndown(value || '')?.trim();
      updateNodeData(nodeId, { nodeType: queryType, content: mrkd });
    } else {
      updateNodeData(nodeId, { queryType: queryType });
      textInputRef.current?.focus();
      const range = document.createRange();
      range.selectNodeContents(textInputRef.current);
      range.collapse(false);
      const selection = window.getSelection();
      selection.removeAllRanges();
      selection.addRange(range);
    }
  }, [])

  const showExamples = React.useMemo(() => {
    const turndownService = new TurndownService();
    const mrkd = turndownService.turndown(input || '')?.trim();

    const currentQueryType = queryType === 'dynamic_with_menu' ? 'dynamic' : queryType;
    return standAlone && !triggered && (!mrkd || mrkd.length < 4) && !flow_settings?.hideExamples && (PROMPT_EXAMPLES[currentQueryType + '_' + scenarioId] || PROMPT_EXAMPLES[currentQueryType]);
  }, [input, triggered, queryType, flow_settings?.hideExamples, standAlone, scenarioId]);


  const submitButton = React.useMemo(() => {
    return <PromptTypeDropdownAndActionButton
      nodeId={nodeId}
      color_theme={color_theme}
      queryType={queryType}
      queryTypeOptions={queryTypeOptions}
      queryTypeChangable={queryTypeChangable}
      triggered={triggered}
      loading={loading}
      isDropdown={menuDroppedDown === 'prompt_type'}
      setDropdown={(dropdown) => {
        setMenuDroppedDown(dropdown && 'prompt_type')
      }}
      actionEnabled={isValid}
      onActionButtonClicked={() => {
        isValid && handleInputConfirm('click_submit')
      }}
      onSelect={(item) => handleQueryTypeChange(item.value, input)}
    />
  }, [nodeId, color_theme, queryType, queryTypeOptions, queryTypeChangable, triggered, loading, menuDroppedDown, isValid, handleInputConfirm, handleQueryTypeChange, input]);

  return (
    <div
      style={{
        position: 'relative',
        width: '-webkit-fill-available',
        backgroundColor: queryType == 'dynamic' && triggered ? '#eee' : color_theme.content_bg,
        borderRadius: 4,
        padding: 6,
        paddingRight: queryTypeChangable && (!triggered || doableAfterTriggeredTypes.includes(queryType)) ? 0 : 6,
        paddingLeft: toggleOptionsButton && !isMultiline ? 1 : 8
      }}
      onKeyPress={(event) => {
        if (event.key == 'Enter' && (!event.shiftKey || queryType === 'link')) {
          event.preventDefault();
          event.stopPropagation();

          if (!loading) {
            handleInputConfirm('key_enter');
          }
        }
      }}

      onKeyDown={onKeyDown}

      onClick={() => {
        setMenuDroppedDown(null);
      }}
    >
      {!!showExamples?.length && (
        <Examples
          examples={showExamples}
          onSelect={(example) => {
            setInput(example);
            if (onChange) onChange(example);
          }}
          color_theme={color_theme}
        />
      )}
      <div style={{
        position: 'relative',
        display: 'flex',
        flexDirection: isMultiline ? 'column' : 'row',
        alignItems: isMultiline ? 'flex-start' : 'center',
        justifyContent: 'space-between',
        width: '-webkit-fill-available',
        columnGap: 2
      }}>
        {!isMultiline && optionsButton}
        {!loading && (!triggered || ['ask', 'ask_question', 'search', 'generate_image', 'perspective', 'mindmap_primary_branch', 'idea', 'new_sub_topic', 'new_task', 'new_insight'].includes(queryType)) && (
          <>
            <ContentEditable
              id={'promptInput_' + nodeId}
              innerRef={textInputRef}
              className={isMobile ? 'fill-available' : `fill-available nodrag ${isMultiline ? 'nowheel' : ''}`}
              style={{
                // width: `calc(100% - ${isMultiline ? 6 : 35}px)`,
                border: '0px',
                outline: 'none',
                fontSize: 15,
                alignContent: 'flex-end',
                backgroundColor: color_theme.content_bg,
                cursor: 'text',
                maxHeight: 400,
                overflowY: 'auto',
                marginLeft: isMultiline ? 2 : 0,
                paddingRight: isMultiline ? 5 : 0,
                // borderBottom: showOptions ? `1px solid ${color_theme.title_bg}` : undefined
              }}
              html={input || ''}
              onChange={(e) => {
                handleInputChange(e);
              }}
              onBlur={() => {
                inputChanged && saveUserInput && saveUserInput(input);
                setInputChanged(false);
              }}
              onCompositionStart={() => setIsInputZh(true)}
              onCompositionEnd={() => setIsInputZh(false)}
              onPaste={handlePaste}
            />
            {!input?.trim() && (
              <div
                style={{
                  position: 'absolute',
                  left: toggleOptionsButton ? 26 : 1,
                  top: '50%',
                  transform: 'translateY(-50%)',
                  pointerEvents: 'none',
                  fontSize: 13,
                  fontWeight: 380,
                  color: '#a3a3a3',
                  whiteSpace: 'nowrap',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  display: 'block',
                }}
              >
                {intl.formatMessage({ id: 'flow_' + queryType + (['decision_analysis'].includes(scenarioId) ? '_' + scenarioId : '') + '_placeholder' })}
              </div>
            )}
          </>
        )}
        {(loading || (!doableAfterTriggeredTypes.includes(queryType) && triggered)) && (
          <div style={{
            whiteSpace: 'nowrap',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
          }}
            dangerouslySetInnerHTML={{ __html: input }} />
        )}
        {
          !showOptions &&
          <div
            className={isMultiline ? 'fill-available' : undefined}
            style={{
              display: 'flex',
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: isMultiline && !!optionsButton ? 'space-between' : 'flex-end',
              marginTop: isMultiline ? 6 : 0,
            }}>
            {
              isMultiline && optionsButton
            }
            <div
              style={{
                display: 'flex',
                flexDirection: 'row',
                alignItems: 'center',
                columnGap: 6
              }}
            >
              {
                isMultiline &&
                <div style={{
                  color: 'gray',
                  fontSize: 14,
                }}>
                  {intl.formatMessage({ id: 'softbreak_tips' })}
                </div>
              }
              {
                submitButton
              }
            </div>

          </div>
        }
      </div>
      {
        (showOptions && toggleOptionsButton || useCase) && <div
          className='fill-available'
          style={{
            position: 'relative',
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            marginLeft: 4,
            marginRight: 0,
            marginTop: 6,
          }}>
          <span style={{
            color: '#777',
            fontSize: 12,
            whiteSpace: 'nowrap'
          }}>
            {intl.formatMessage({ id: 'topic_type' })}
          </span>
          <input
            ref={scenarioInputRef}
            className='nodrag fill-available'
            placeholder={intl.formatMessage({ id: 'type_or_select_placeholder' })}
            style={{
              fontSize: 13,
              border: '0px',
              outline: 'none',
              backgroundColor: 'transparent',
              minWidth: 100,
              marginLeft: 6,
              borderBottom: showOptions ? `1px solid ${color_theme.title_bg}` : undefined,
            }}

            value={scenario || ''}
            onChange={(event) => {
              setScenario(event.target.value?.trim());
              setScenarioId(Selectable_Thinking_Models.find(item => item.label == event.target.value?.trim())?.value)
            }}
          />
          <DropDownMenu
            items={Selectable_TopicTypes}
            isDropdown={menuDroppedDown === 'selectable_topics'}
            setDropdown={(dropdown) => {
              setMenuDroppedDown(dropdown && 'selectable_topics')
            }}

            tooltip={intl.formatMessage({ id: 'toggle_options' })}
            color_theme={color_theme}
            onSelect={(item) => {
              item.value != 'other' && setScenario(item.label);
              setScenarioId(item.value);
              scenarioInputRef.current?.focus();
            }}
          />
        </div>
      }

      {
        (showOptions && toggleOptionsButton || useCase) && <div
          className='fill-available'
          style={{
            position: 'relative',
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            marginLeft: 4,
            marginRight: 0,
            marginTop: 7,
            fontSize: 14,
          }}>
          <span style={{
            color: '#777',
            fontSize: 12,
            whiteSpace: 'nowrap'
          }}>
            {intl.formatMessage({ id: 'thinking_model' })}
          </span>
          <input
            ref={thinkingModelInputRef}
            className='nodrag fill-available'
            placeholder={intl.formatMessage({ id: 'type_or_select_placeholder' })}
            style={{
              fontSize: 13,
              border: '0px',
              outline: 'none',
              backgroundColor: 'transparent',
              minWidth: 100,
              marginLeft: 6,
              borderBottom: showOptions ? `1px solid ${color_theme.title_bg}` : undefined
            }}
            value={thinking_model || ''}
            onChange={(event) => {
              set_thinking_model(event.target.value?.trim())
            }}
          />
          {
            !!Selectable_Thinking_Models?.length &&
            <DropDownMenu
              items={Selectable_Thinking_Models}
              isDropdown={menuDroppedDown === 'thinking_models'}
              setDropdown={(dropdown) => {
                setMenuDroppedDown(dropdown && 'thinking_models')
              }}

              tooltip={intl.formatMessage({ id: 'toggle_options' })}
              color_theme={color_theme}
              onSelect={(item) => {
                item.value != 'other' && set_thinking_model(item.label);
                thinkingModelInputRef.current?.focus();
              }}

              explainIt={(model) => { callAI({ item: { action: 'query' }, userInput: `Please explain this classic mental model: ${model}`, queryType: 'ask', standAlone: true }) }}
            />
          }
        </div>
      }
      {
        showOptions &&
        <div
          className={isMultiline ? 'fill-available' : undefined}
          style={{
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: isMultiline && !!optionsButton ? 'space-between' : 'flex-end',
            marginTop: 6,
          }}>
          {
            isMultiline && optionsButton
          }
          <div
            style={{
              display: 'flex',
              flexDirection: 'row',
              alignItems: 'center',
              columnGap: 6
            }}
          >
            {
              isMultiline &&
              <div style={{
                color: 'gray',
                fontSize: 14,
              }}>
                {intl.formatMessage({ id: 'softbreak_tips' })}
              </div>
            }
            {
              submitButton
            }
          </div>
        </div>
      }
      {
        !isMultiline && showInputTips && !triggered && <div style={{
          position: 'absolute',
          top: '100%',
          right: 0,
          color: 'gray',
          fontSize: 14,
          paddingTop: 6
        }}>
          {intl.formatMessage({ id: 'softbreak_tips' })}
        </div>
      }
    </div>)
};

const styles = `
  .hover-highlight:hover {
    opacity: 0.8;
    transform: translateY(-1px);
    transition: all 0.2s ease;
  }
`;

if (typeof document !== 'undefined') {
  const styleSheet = document.createElement('style');
  styleSheet.innerText = styles;
  document.head.appendChild(styleSheet);
}
