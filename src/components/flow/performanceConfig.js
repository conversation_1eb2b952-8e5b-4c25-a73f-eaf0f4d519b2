/**
 * AIFlow 性能优化配置
 */

// 性能阈值配置
export const PERFORMANCE_THRESHOLDS = {
  // 节点数量阈值
  NODE_COUNT_WARNING: 50,      // 节点数量警告阈值
  NODE_COUNT_CRITICAL: 100,    // 节点数量严重阈值
  NODE_COUNT_OPTIMIZE: 150,    // 启用优化模式的节点数量
  
  // 渲染性能阈值
  RENDER_TIME_WARNING: 16,     // 渲染时间警告阈值 (ms)
  RENDER_TIME_CRITICAL: 33,    // 渲染时间严重阈值 (ms)
  
  // FPS 阈值
  FPS_WARNING: 30,             // FPS 警告阈值
  FPS_CRITICAL: 15,            // FPS 严重阈值
  
  // 内存使用阈值
  MEMORY_WARNING: 100,         // 内存使用警告阈值 (MB)
  MEMORY_CRITICAL: 200,        // 内存使用严重阈值 (MB)
};

// ReactFlow 性能优化配置
export const REACTFLOW_PERFORMANCE_CONFIG = {
  // 基础配置
  nodeOrigin: [0.5, 0.5],
  snapToGrid: false,
  snapGrid: [15, 15],
  
  // 连接线配置
  connectionLineType: "smoothstep",
  defaultEdgeOptions: {
    type: 'float_edge',
    animated: false,
  },
  
  // 缩放配置
  minZoom: 0.1,
  maxZoom: 2,
  zoomOnDoubleClick: false,
  
  // 选择配置
  multiSelectionKeyCode: "Shift",
  
  // 性能优化选项
  elevateNodesOnSelect: false,
  elevateEdgesOnSelect: false,
};

// 节点渲染优化配置
export const NODE_RENDER_CONFIG = {
  // 虚拟化配置
  enableVirtualization: true,
  virtualizedThreshold: PERFORMANCE_THRESHOLDS.NODE_COUNT_OPTIMIZE,
  
  // 节点简化配置
  simplifyNodesThreshold: PERFORMANCE_THRESHOLDS.NODE_COUNT_WARNING,
  
  // 延迟加载配置
  lazyLoadContent: true,
  lazyLoadThreshold: 20,
  
  // 节点更新优化
  batchUpdates: true,
  updateDebounceTime: 100,
};

// 状态管理优化配置
export const STATE_MANAGEMENT_CONFIG = {
  // 是否启用浅比较
  useShallowComparison: true,
  
  // 批量更新配置
  batchStateUpdates: true,
  batchUpdateDelay: 50,
  
  // 内存管理
  enableGarbageCollection: true,
  gcInterval: 30000, // 30秒
};

// 性能监控配置
export const MONITORING_CONFIG = {
  // 是否启用性能监控
  enabled: process.env.NODE_ENV === 'development',
  
  // 监控间隔
  fpsMonitorInterval: 1000,
  memoryMonitorInterval: 5000,
  
  // 是否显示详细信息
  showDetailedMetrics: true,
  
  // 是否记录性能日志
  logPerformanceMetrics: true,
};

// 获取当前性能模式
export const getPerformanceMode = (nodeCount) => {
  if (nodeCount >= PERFORMANCE_THRESHOLDS.NODE_COUNT_OPTIMIZE) {
    return 'optimized';
  } else if (nodeCount >= PERFORMANCE_THRESHOLDS.NODE_COUNT_WARNING) {
    return 'simplified';
  }
  return 'normal';
};

// 获取优化建议
export const getOptimizationSuggestions = (metrics) => {
  const suggestions = [];
  
  if (metrics.nodeCount > PERFORMANCE_THRESHOLDS.NODE_COUNT_WARNING) {
    suggestions.push({
      type: 'warning',
      message: `节点数量较多 (${metrics.nodeCount})，建议启用简化模式`,
      action: 'enableSimplifiedMode'
    });
  }
  
  if (metrics.nodeCount > PERFORMANCE_THRESHOLDS.NODE_COUNT_OPTIMIZE) {
    suggestions.push({
      type: 'critical',
      message: `节点数量过多 (${metrics.nodeCount})，建议启用优化模式`,
      action: 'enableOptimizedMode'
    });
  }
  
  if (metrics.renderTime > PERFORMANCE_THRESHOLDS.RENDER_TIME_WARNING) {
    suggestions.push({
      type: 'warning',
      message: `渲染时间过长 (${metrics.renderTime}ms)，可能影响流畅度`,
      action: 'optimizeRendering'
    });
  }
  
  if (metrics.fps < PERFORMANCE_THRESHOLDS.FPS_WARNING) {
    suggestions.push({
      type: 'warning',
      message: `FPS过低 (${metrics.fps})，建议减少节点数量或启用优化模式`,
      action: 'improveFPS'
    });
  }
  
  if (metrics.memoryUsage > PERFORMANCE_THRESHOLDS.MEMORY_WARNING) {
    suggestions.push({
      type: 'warning',
      message: `内存使用过高 (${metrics.memoryUsage}MB)，建议清理不必要的数据`,
      action: 'optimizeMemory'
    });
  }
  
  return suggestions;
};

// 性能优化工具函数
export const performanceUtils = {
  // 节流函数
  throttle: (func, limit) => {
    let inThrottle;
    return function() {
      const args = arguments;
      const context = this;
      if (!inThrottle) {
        func.apply(context, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    }
  },
  
  // 防抖函数
  debounce: (func, wait, immediate) => {
    let timeout;
    return function() {
      const context = this;
      const args = arguments;
      const later = function() {
        timeout = null;
        if (!immediate) func.apply(context, args);
      };
      const callNow = immediate && !timeout;
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
      if (callNow) func.apply(context, args);
    };
  },
  
  // 批量更新
  batchUpdate: (updates, delay = 50) => {
    return new Promise((resolve) => {
      setTimeout(() => {
        updates.forEach(update => update());
        resolve();
      }, delay);
    });
  }
};
