import { FLOW_INPUT_MODAL } from '@/constants/actionTypes';
import React, { useEffect, useState } from 'react';
import { useIntl } from 'react-intl';
import { useDispatch, useSelector } from 'react-redux';
import TurndownService from 'turndown';
import { useMediaQuery } from 'react-responsive';
import { 
  Dialog, 
  DialogContent, 
  DialogActions, 
  Button, 
  TextField, 
  InputBase,
  Paper, 
  Box
} from '@mui/material';
import ContentEditable from 'react-contenteditable';
import Draggable from 'react-draggable';
import { MOBILE_MEDIA_QUERY } from '../../utils/constants';

// 创建可拖拽的对话框组件
function PaperComponent(props) {
  return (
    <Draggable
      handle="#draggable-dialog-title"
      cancel={'[class*="MuiDialogContent-root"]'}
    >
      <Paper {...props} />
    </Draggable>
  );
}

const InputModal = () => {
  const modalState = useSelector(state => state.uiState.flow_input_modal) || {};
  const { visible, value } = modalState;
  const dispatch = useDispatch();
  const intl = useIntl();
  const [isValid, setIsValid] = useState(false);
  const [content, setContent] = useState('');
  const textInputRef = React.useRef();
  const isMobile = useMediaQuery(MOBILE_MEDIA_QUERY);

  useEffect(() => {
    setContent(value);
  }, [value]);

  useEffect(() => {
    setTimeout(() => {
      const editableDiv = textInputRef.current;
      if (editableDiv) {
        editableDiv.focus();

        if (modalState.multiline) {
          const range = document.createRange();
          range.selectNodeContents(editableDiv);
          range.collapse(false); // 将光标移动到末尾
          const selection = window.getSelection();
          selection.removeAllRanges();
          selection.addRange(range);
        }
      }
    }, 100);
  }, [textInputRef?.current, modalState?.multiline]);

  useEffect(() => {
    const turndownService = new TurndownService();
    const mrkd = turndownService.turndown(content || '')?.trim();

    let isValid = !!mrkd?.trim();

    setIsValid(isValid);
  }, [content]);

  const handlePaste = React.useCallback((e) => {
    e.preventDefault();

    const text = e.clipboardData.getData('text/plain');
    document.execCommand('insertText', false, text);
  }, []);

  const handleClose = () => {
    dispatch({
      type: FLOW_INPUT_MODAL,
      value: {
        visible: false
      }
    });
  };

  const handleChange = (event) => {
    setContent(event.target.value);
  };

  const handleConfirm = () => {
    dispatch({
      type: FLOW_INPUT_MODAL,
      value: {
        ...modalState,
        value: content,
        visible: false
      }
    });
  };

  if (!visible) {
    return null;
  }

  return (
    <Dialog
      open={visible}
      PaperComponent={PaperComponent}
      aria-labelledby="draggable-dialog-title"
      PaperProps={{
        sx: {
          position: 'fixed',
          left: 'calc(50% - 180px)',
          top: '20%',
          transform: 'translate(-50%, -50%)',
          bgcolor: 'azure',
          p: 0,
          borderRadius: '4px',
          boxShadow: '5px 5px 0px rgba(0, 0, 0, 10%)',
          border: '1px solid gray',
          zIndex: 1000,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          rowGap: 1.25,
          cursor: 'pointer',
          width: 'auto',
          m: 0
        },
      }}
    >
      <Box
        id="draggable-dialog-title"
        className="handle"
        sx={{
          cursor: 'move',
          width: '100%',
          height: '10px',
          '&:hover': {
            bgcolor: 'rgba(0,0,0,0.1)'
          }
        }}
      />
      
      <DialogContent
        sx={{
          p: 0,
          width: 360,
          '&.MuiDialogContent-root': {
            pt: 0,
            pb: 0,
            px: 0
          }
        }}
        onMouseDown={(event) => {
          event.stopPropagation();
        }}
      >
        {!modalState?.multiline ? (
          <InputBase
            id={'promptInput_single'}
            inputRef={textInputRef}
            className={`fill-available ${isMobile ? 'nodrag' : ''}`}
            sx={{
              border: '0px',
              outline: 'none',
              fontSize: 15,
              alignContent: 'flex-end',
              cursor: 'text',
              width: '100%',
              bgcolor: 'transparent',
              p: 1.25,
              pb: 0,
              '& .MuiInputBase-input': {
                p: 0
              }
            }}
            value={content || ''}
            onChange={handleChange}
            fullWidth
          />
        ) : (
          <ContentEditable
            id={'promptInput_multi'}
            innerRef={textInputRef}
            className="fill-available nodrag"
            style={{
              border: '0px',
              outline: 'none',
              fontSize: 15,
              alignContent: 'flex-end',
              cursor: 'text',
              width: 360,
              maxHeight: 400,
              padding: 10,
              paddingBottom: 0,
              overflowY: 'auto'
            }}
            html={content || ''}
            onChange={handleChange}
            onPaste={handlePaste}
          />
        )}
      </DialogContent>

      <DialogActions
        className="fill-available"
        sx={{
          display: 'flex',
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'flex-end',
          height: 'fit-content',
          p: 1.25,
          columnGap: 1.25
        }}
      >
        <Button 
          className="transparent-background"
          sx={{
            p: 0.625,
            px: 1.25,
            borderRadius: 0.5,
            cursor: 'pointer',
            whiteSpace: 'nowrap',
            color: 'GrayText',
            textTransform: 'none',
            minWidth: 'auto',
            '&:hover': {
              bgcolor: 'rgba(0,0,0,0.04)'
            }
          }}
          onClick={handleClose}
        >
          {intl.formatMessage({ id: 'cancel' })}
        </Button>
        <Button
          className="transparent-background"
          sx={{
            color: isValid ? 'inherit' : 'GrayText',
            border: `1px solid ${isValid ? 'gray' : '#eee'}`,
            boxShadow: '0px 0px 8px #bbb',
            p: 0.625,
            px: 1.25,
            borderRadius: 0.5,
            cursor: 'pointer',
            textTransform: 'none',
            minWidth: 'auto',
            '&.Mui-disabled': {
              color: 'GrayText'
            }
          }}
          onClick={handleConfirm}
          disabled={!(isValid || modalState.nullable)}
        >
          {intl.formatMessage({ id: 'confirm' })}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default InputModal;