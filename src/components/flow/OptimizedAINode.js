import React from 'react';
import { Handle, Position } from '@xyflow/react';

/**
 * 优化的 AI 节点组件，用于处理大量节点的情况
 * 这个组件只渲染最基本的内容，减少渲染开销
 */
const OptimizedAINode = React.memo(({ data, isConnectable, selected }) => {
  const { nodeType, title, content, color_theme } = data;
  
  // 简化的颜色主题
  const defaultTheme = {
    title_bg: '#e3f2fd',
    content_bg: '#ffffff',
    border: '#90caf9'
  };
  
  const theme = color_theme || defaultTheme;
  
  // 简化的样式
  const nodeStyle = {
    backgroundColor: theme.content_bg,
    border: `1px solid ${theme.border}`,
    borderRadius: '4px',
    minWidth: '200px',
    maxWidth: '300px',
    boxShadow: selected ? '0 0 0 2px #1976d2' : '0 2px 4px rgba(0,0,0,0.1)',
  };
  
  const titleStyle = {
    backgroundColor: theme.title_bg,
    padding: '8px 12px',
    fontSize: '14px',
    fontWeight: '500',
    borderBottom: `1px solid ${theme.border}`,
    borderTopLeftRadius: '3px',
    borderTopRightRadius: '3px',
  };
  
  const contentStyle = {
    padding: '12px',
    fontSize: '13px',
    lineHeight: '1.4',
    maxHeight: '200px',
    overflow: 'hidden',
  };
  
  // 截断长文本
  const truncateText = (text, maxLength = 100) => {
    if (!text || text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  };
  
  return (
    <div style={nodeStyle}>
      {/* 连接点 */}
      <Handle
        type="target"
        position={Position.Left}
        isConnectable={isConnectable}
        style={{ background: theme.border }}
      />
      
      {/* 标题 */}
      {title && (
        <div style={titleStyle}>
          {truncateText(title, 50)}
        </div>
      )}
      
      {/* 内容 */}
      {content && (
        <div style={contentStyle}>
          {truncateText(content, 150)}
        </div>
      )}
      
      {/* 节点类型指示器 */}
      {nodeType && nodeType !== 'aigc' && (
        <div style={{
          position: 'absolute',
          top: '4px',
          right: '4px',
          backgroundColor: theme.border,
          color: 'white',
          fontSize: '10px',
          padding: '2px 4px',
          borderRadius: '2px',
        }}>
          {nodeType}
        </div>
      )}
      
      <Handle
        type="source"
        position={Position.Right}
        isConnectable={isConnectable}
        style={{ background: theme.border }}
      />
    </div>
  );
}, (prevProps, nextProps) => {
  // 优化的比较函数，只在关键属性变化时重新渲染
  return (
    prevProps.data.title === nextProps.data.title &&
    prevProps.data.content === nextProps.data.content &&
    prevProps.data.nodeType === nextProps.data.nodeType &&
    prevProps.data.color_theme === nextProps.data.color_theme &&
    prevProps.selected === nextProps.selected &&
    prevProps.isConnectable === nextProps.isConnectable
  );
});

OptimizedAINode.displayName = 'OptimizedAINode';

export default OptimizedAINode;
