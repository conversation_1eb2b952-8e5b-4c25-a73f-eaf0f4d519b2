import React, { useCallback, useEffect, useRef, useState } from 'react';
import { Tooltip, Popover, CircularProgress } from '@mui/material';
import { toPng } from 'html-to-image';
import { useIntl } from 'react-intl';
import { useDispatch } from 'react-redux';
import { AI_ASSISTANT_DIALOG } from '../../constants/actionTypes';
import { Close, Download, ZoomIn, ZoomOut, Fullscreen } from '@styled-icons/material';
import MermaidRenderer from '../common/MermaidRenderer';
import { preprocessSvg } from '../../utils/svgUtils';
import { Edit, Save } from '@styled-icons/remix-line';
import { Magic } from '@styled-icons/bootstrap/Magic';
import { Check, Lightbulb, X } from '@styled-icons/bootstrap';
import UserInputModal from './UserInputModal';

// import { useViewport } from '@xyflow/react';
const Artifact = ({
    inDoc,
    selectedArtifact,
    onEdit,
    onAIAction,
    aigc_hovered,
    setSelectedArtifact,
    offsetTop,
    color_theme,
    zoom = 1,
    showAcceptReject = false,
    onAccept,
    onReject,
    path,
    aiProcessing
}) => {
    const intl = useIntl();
    const dispatch = useDispatch();
    const iframeRef = useRef(null);
    const containerRef = useRef(null);
    const [iframeSize, setIframeSize] = useState({ width: 320, height: 240 });
    const [isResizing, setIsResizing] = useState(false);
    const [resized, setResized] = useState(false);
    const [resizeDirection, setResizeDirection] = useState(null);
    const [aspectRatio, setAspectRatio] = useState(null);
    const [scale, setScale] = useState(1);
    const [isFullscreen, setIsFullscreen] = useState(false);
    const [position, setPosition] = useState({ x: 0, y: 0 });
    const [isDragging, setIsDragging] = useState(false);
    const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
    const [hovered, setHovered] = useState();
    const [aiMenuOpen, setAiMenuOpen] = useState(false);
    const [aiMenuAnchor, setAiMenuAnchor] = useState(null);
    const [userInputModalVisible, setUserInputModalVisible] = useState(false);
    const [artifact, setArtifact] = useState(selectedArtifact);
    const [isDownloading, setIsDownloading] = useState(false);

    // const { zoom } = useViewport();

    // useEffect(() => {
    //     if (iframeSize.width && iframeSize.height) {
    //         setAspectRatio(iframeSize.width / iframeSize.height);
    //     }
    // }, [iframeSize.width, iframeSize.height]);

    useEffect(() => {
        const newContent = selectedArtifact?.type === 'SVG' ? preprocessSvg(selectedArtifact.content) : selectedArtifact.content;

        setArtifact({
            ...selectedArtifact,
            content: newContent
        })
    }, [selectedArtifact.content]);

    useEffect(() => {
        if (!containerRef?.current) return;
        const rect = containerRef.current.getBoundingClientRect();
        setAspectRatio(rect.width / rect.height)
    }, [containerRef?.current])

    useEffect(() => {
        const iframe = iframeRef?.current;
        if (isResizing || !iframe || artifact?.type !== 'HTML') return;

        const checkOverflow = () => {
            const iframeDocument = iframe.contentDocument || iframe.contentWindow.document;
            const bodyElement = iframeDocument.body;

            const isOverflowingHorizontally = bodyElement.scrollWidth > iframeRef.current?.clientWidth;
            const isOverflowingVertically = bodyElement.scrollHeight > iframeRef.current?.clientHeight;

            if (isOverflowingHorizontally) {
                setIframeSize(prev => ({ ...prev, width: bodyElement.scrollWidth + 24 }));
            }
            if (isOverflowingVertically) {
                setIframeSize(prev => ({ ...prev, height: bodyElement.scrollHeight + 24 * bodyElement.scrollHeight / bodyElement.scrollWidth }));
            }
        };

        const handleIframeLoad = () => {
            checkOverflow();
        };

        iframe.addEventListener('load', handleIframeLoad);

        // Set initial content
        let content = artifact?.content || '';
        if (!content.includes('<html')) {
            content = `<html><body>${content}</body></html>`;
        }
        iframe.srcdoc = content;

        return () => {
            iframe.removeEventListener('load', handleIframeLoad);
        };
    }, [isResizing, artifact]);

    const handleMouseDown = (e, direction) => {
        e.preventDefault();
        setIsResizing(true);
        setResizeDirection(direction);
    };

    const handleMouseMove = (e) => {
        if (!isResizing) return;

        setResized(true);

        const container = containerRef.current;
        const rect = container.getBoundingClientRect();

        if (resizeDirection === 'horizontal') {
            const iframeDocument = iframeRef?.current?.contentDocument || iframeRef?.current?.contentWindow?.document;
            const bodyElement = iframeDocument?.body;

            const newWidth = (e.clientX - rect.left) / zoom;
            // if (iframeDocument) {
            //     setIframeSize({ width: newWidth, height: bodyElement.scrollHeight + 24 * bodyElement.scrollHeight / bodyElement.scrollWidth });
            // } else {
            setIframeSize(prevState => ({ ...(prevState || {}), width: newWidth, height: rect.clientHeight }));
            // }
        } else if (resizeDirection === 'vertical') {
            const newHeight = (e.clientY - rect.top) / zoom;
            setIframeSize(prev => ({ ...prev, height: newHeight }));
        } else if (resizeDirection === 'corner') {
            const newWidth = (e.clientX - rect.left) / zoom;
            const newHeight = (e.clientY - rect.top) / zoom;

            if (aspectRatio) {
                if (newWidth / newHeight > aspectRatio) {
                    setIframeSize({ width: newHeight * aspectRatio, height: newHeight });
                } else {
                    setIframeSize({ width: newWidth, height: newWidth / aspectRatio });
                }
            } else {
                setIframeSize({ width: newWidth, height: newHeight });
            }
        }
    };

    const handleMouseUp = () => {
        setIsResizing(false);
        setResizeDirection(null);
    };

    useEffect(() => {
        if (isResizing) {
            window.addEventListener('mousemove', handleMouseMove);
            window.addEventListener('mouseup', handleMouseUp);
        }
        return () => {
            window.removeEventListener('mousemove', handleMouseMove);
            window.removeEventListener('mouseup', handleMouseUp);
        };
    }, [isResizing]);

    const handleDownload = async () => {
        if (isDownloading) return;

        setIsDownloading(true);
        try {
            const element = document.getElementById(
                artifact?.type === 'SVG' && `artifact-${artifact.id}` ||
                artifact?.type === 'Mermaid' && `mermaid-chart-${artifact.id}` ||
                'artifact-content'
            );

            const dataUrl = await toPng(element);
            const link = document.createElement('a');
            link.download = 'artifact.png';
            link.href = dataUrl;
            link.click();
        } catch (error) {
            console.error('下载图片时出错:', error);
        } finally {
            setIsDownloading(false);
        }
    };

    const handleZoomIn = () => {
        setScale(prev => Math.min(prev + 0.2, 5));
        setPosition({ x: 0, y: 0 });
    };

    const handleZoomOut = () => {
        setScale(prev => Math.max(prev - 0.2, 0.5));
        setPosition({ x: 0, y: 0 });
    };

    const handleFullscreen = () => {
        setIsFullscreen(!isFullscreen);
        setPosition({ x: 0, y: 0 });
        const container = containerRef.current;

        if (!isFullscreen) {
            if (container.requestFullscreen) {
                container.requestFullscreen();
            } else if (container.webkitRequestFullscreen) {
                container.webkitRequestFullscreen();
            } else if (container.msRequestFullscreen) {
                container.msRequestFullscreen();
            }
            setScale(1); // 重置缩放
        } else {
            if (document.exitFullscreen) {
                document.exitFullscreen();
            } else if (document.webkitExitFullscreen) {
                document.webkitExitFullscreen();
            } else if (document.msExitFullscreen) {
                document.msExitFullscreen();
            }
        }
    };

    useEffect(() => {
        const handleFullscreenChange = () => {
            if (!document.fullscreenElement &&
                !document.webkitFullscreenElement &&
                !document.msFullscreenElement) {
                setIsFullscreen(false);
                setScale(1);
            }
        };

        document.addEventListener('fullscreenchange', handleFullscreenChange);
        document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
        document.addEventListener('msfullscreenchange', handleFullscreenChange);

        return () => {
            document.removeEventListener('fullscreenchange', handleFullscreenChange);
            document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
            document.removeEventListener('msfullscreenchange', handleFullscreenChange);
        };
    }, []);

    const handleContainerMouseDown = (e) => {
        if (
            e.target.closest('.resize-handle') ||
            e.target.closest('.transparent-background')
        ) {
            return;
        }

        setIsDragging(true);
        setDragStart({
            x: e.clientX - position.x,
            y: e.clientY - position.y
        });
    };

    const handleContainerMouseMove = (e) => {
        if (!isDragging) return;

        const newX = e.clientX - dragStart.x;
        const newY = e.clientY - dragStart.y;

        setPosition({
            x: newX,
            y: newY
        });
    };

    const handleContainerMouseUp = () => {
        setIsDragging(false);
    };

    // AI 菜单处理函数
    const handleAIMenuClick = (event) => {
        event.stopPropagation();
        setAiMenuAnchor(event.currentTarget);
        setAiMenuOpen(true);
    };

    const handleAIMenuClose = () => {
        setAiMenuOpen(false);
        setAiMenuAnchor(null);
    };

    const handleAIAction = (action, userInput = '') => {
        handleAIMenuClose();

        if (action === 'improve_codes' && !userInput) {
            // 如果是优化操作且没有用户输入，显示输入弹窗
            setUserInputModalVisible(true);
            return;
        }

        if (inDoc) {
            let aiContent = `[Given codes]:\n\n\`\`\`${artifact.type}\n${artifact.content}\n\`\`\``;

            if (!!userInput) {
                aiContent = aiContent + '\n\n[User input]: ' + userInput;
            }

            dispatch({
                type: AI_ASSISTANT_DIALOG,
                value: {
                    caller: 'plate',
                    visible: true,
                    trigger: 'blockHandler',
                    action,
                    selectedText: aiContent,
                    path
                }
            })
        } else if (onAIAction) {
            // 在 AINode 上下文中，使用现有的处理逻辑
            onAIAction(action, artifact, userInput);
        }
    };

    // 处理用户输入确认
    const handleUserInputConfirm = (userInput) => {
        setUserInputModalVisible(false);
        handleAIAction('improve_codes', userInput);
    };

    // 处理用户输入取消
    const handleUserInputCancel = () => {
        setUserInputModalVisible(false);
    };

    return (
        <div
            ref={containerRef}
            className='nodrag'
            onMouseDown={handleContainerMouseDown}
            onMouseMove={handleContainerMouseMove}
            onMouseUp={handleContainerMouseUp}
            // onMouseLeave={handleContainerMouseUp}
            style={{
                position: !inDoc ? 'absolute' : undefined,
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                left: 'calc(100% + 20px)',
                top: offsetTop,
                minWidth: artifact?.type === 'Mermaid' ? 480 : 320,
                minHeight: 240,
                width: inDoc ? undefined : iframeSize?.width && resized ? `${iframeSize.width}px` : 'fit-content',
                height: iframeSize?.height && resized ? `${iframeSize.height}px` : undefined,
                border: `1px solid #ccc`,
                backgroundColor: color_theme?.content_bg || 'white',
                boxShadow: inDoc ? undefined : '0px 0px 8px #bbb',
                overflow: 'hidden',
                cursor: isDragging ? 'grabbing' : 'grab',
                ...(isFullscreen && {
                    width: '100%',
                    height: '100%',
                })
            }}

            onMouseEnter={inDoc ? () => {
                setHovered(true)
            } : undefined}

            onMouseLeave={inDoc ? () => {
                setHovered(false)
                handleContainerMouseUp()
            } : handleContainerMouseUp}
        >
            <div id="artifact-content"
                className='fill-available'
                style={{
                    position: 'relative',
                    height: '100%',
                    backgroundColor: color_theme?.content_bg || 'white',
                    transform: `scale(${scale}) translate(${position.x}px, ${position.y}px)`,
                    transformOrigin: 'center center',
                    transition: isDragging ? 'none' : 'transform 0.3s ease',
                    ...(isFullscreen && {
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                    })
                }}
            >
                {
                    artifact?.type === 'SVG' &&
                    <div
                        style={{
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center'
                        }}
                        id={`artifact-${artifact.id}`}
                        dangerouslySetInnerHTML={{ __html: artifact.content }}
                    />
                }
                {
                    artifact?.type === 'HTML' &&
                    <iframe
                        ref={iframeRef}
                        style={{
                            width: `${iframeSize.width}px`,
                            height: `${iframeSize.height}px`,
                            border: 'none',
                            backgroundColor: 'white',
                        }}
                    />
                }
                {
                    artifact?.type === 'Mermaid' &&
                    <MermaidRenderer id={'mermaid-chart-' + artifact.id} source={artifact.content} />
                }
            </div>

            <div style={{
                display: (aigc_hovered || hovered) ? 'flex' : 'none',
                flexDirection: 'column',
                position: 'absolute',
                bottom: 10,
                right: 0,
                gap: '5px',
                padding: '5px',
            }}>
                <Tooltip title={intl.formatMessage({ id: 'zoom_in' })} placement='left'>
                    <div
                        className='transparent-background'
                        style={{
                            width: 28,
                            height: 28,
                            color: '#777',
                            cursor: 'pointer',
                        }}
                        onClick={handleZoomIn}
                    >
                        <ZoomIn size={24} />
                    </div>
                </Tooltip>

                <Tooltip title={intl.formatMessage({ id: 'zoom_out' })} placement='left'>
                    <div
                        className='transparent-background'
                        style={{
                            width: 28,
                            height: 28,
                            color: '#777',
                            cursor: 'pointer',
                        }}
                        onClick={handleZoomOut}
                    >
                        <ZoomOut size={24} />
                    </div>
                </Tooltip>

                <Tooltip title={intl.formatMessage({ id: 'fullscreen' })} placement='left'>
                    <div
                        className='transparent-background'
                        style={{
                            width: 28,
                            height: 28,
                            color: '#777',
                            cursor: 'pointer',
                        }}
                        onClick={handleFullscreen}
                    >
                        <Fullscreen size={24} />
                    </div>
                </Tooltip>
            </div>

            {(aigc_hovered || hovered) && (
                <div style={{
                    display: 'flex',
                    flexDirection: 'row',
                    position: 'absolute',
                    top: inDoc ? 8 : 0,
                    right: inDoc ? 4 : 0,
                    gap: 4
                }}>
                    {
                        (inDoc || onAIAction) &&
                        <Tooltip
                            title={intl.formatMessage({ id: 'ai_assistant' }, { defaultMessage: 'AI Assistant' })}
                            placement='top'
                        >
                            <div
                                className='transparent-background'
                                style={{
                                    width: 25,
                                    height: 25,
                                    color: 'dodgerblue',
                                    cursor: 'pointer',
                                    padding: 3,
                                }}
                                onClick={(event) => { 
                                    !aiProcessing && handleAIMenuClick(event);
                                }}
                            > 
                            {
                                !aiProcessing && <Magic size={18} />
                            }
                            {
                                !!aiProcessing && <CircularProgress size={16} />
                            }
                            </div>
                        </Tooltip>
                    }

                    {
                        (inDoc || onEdit) &&
                        <Tooltip
                            title={intl.formatMessage({ id: 'edit' })}
                            placement='top'
                        >
                            <div
                                className='transparent-background'
                                style={{
                                    width: 25,
                                    height: 25,
                                    color: '#777',
                                    cursor: 'pointer',
                                    padding: 3,
                                }}
                                onClick={() => onEdit(artifact)}
                            >
                                <Edit size={18} />
                            </div>
                        </Tooltip>
                    }

                    <Tooltip
                        title={intl.formatMessage({ id: 'export_to_image' }, { defaultMessage: 'Export to Image' })}
                        placement="top"
                    >
                        <div
                            className="transparent-background"
                            style={{
                                width: 25,
                                height: 25,
                                color: isDownloading ? '#1976d2' : '#777',
                                cursor: isDownloading ? 'not-allowed' : 'pointer',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                borderRadius: '4px',
                                transition: 'all 0.2s ease',
                                '&:hover': {
                                    backgroundColor: !isDownloading && '#f0f0f0'
                                }
                            }}
                            onClick={handleDownload}
                        >
                            {isDownloading ? (
                                <CircularProgress size={16} />
                            ) : (
                                <Download size={21} />
                            )}
                        </div>
                    </Tooltip>

                    {
                        !!setSelectedArtifact &&
                        <Tooltip
                            title={intl.formatMessage({ id: 'close' })}
                            placement='top'
                        >
                            <div
                                className='transparent-background'
                                style={{
                                    width: 25,
                                    height: 25,
                                    color: '#777',
                                    cursor: 'pointer',
                                }}
                                onClick={() => setSelectedArtifact(null)}
                            >
                                <Close size={20} />
                            </div>
                        </Tooltip>
                    }

                </div>
            )}

            {/* 接受/拒绝按钮 */}
            {showAcceptReject && (
                <div style={{
                    display: 'flex',
                    flexDirection: 'row',
                    position: 'absolute',
                    top: 4,
                    left: '50%',
                    transform: 'translateX(-50%)',
                    backgroundColor: color_theme?.content_bg || 'white',
                    border: `1px solid ${color_theme?.border || '#ccc'}`,
                    borderRadius: 4,
                    padding: 4,
                    boxShadow: '0px 2px 8px rgba(0,0,0,0.1)',
                    zIndex: 1001,
                    gap: 8
                }}>
                    <Tooltip title={intl.formatMessage({ id: 'reject' }, { defaultMessage: 'Reject this changement' })} placement='top'>
                        <div
                            className='transparent-background'
                            style={{
                                height: 25,
                                color: '#d32f2f',
                                cursor: 'pointer',
                                padding: 3,
                                borderRadius: 3,
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center'
                            }}
                            onClick={onReject}
                        >
                            Reject <X size={20} />
                        </div>
                    </Tooltip>
                    <Tooltip title={intl.formatMessage({ id: 'accept' }, { defaultMessage: 'Accept this changement' })} placement='top'>
                        <div
                            className='transparent-background'
                            style={{
                                height: 25,
                                color: '#2e7d32',
                                cursor: 'pointer',
                                padding: 3,
                                borderRadius: 3,
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center'
                            }}
                            onClick={onAccept}
                        >
                            Accept <Check size={22} />
                        </div>
                    </Tooltip>
                </div>
            )}

            {/* AI 菜单 Popover */}
            <Popover
                open={aiMenuOpen}
                anchorEl={aiMenuAnchor}
                onClose={handleAIMenuClose}
                anchorOrigin={{
                    vertical: 'bottom',
                    horizontal: 'center',
                }}
                transformOrigin={{
                    vertical: 'top',
                    horizontal: 'center',
                }}
                PaperProps={{
                    sx: {
                        borderRadius: '8px',
                        boxShadow: '0px 4px 20px rgba(0, 0, 0, 0.15)',
                        border: '1px solid #e0e0e0',
                        overflow: 'hidden'
                    }
                }}
            >
                <div style={{
                    padding: '4px 0',
                    minWidth: 180,
                    backgroundColor: 'white'
                }}>
                    <div
                        style={{
                            padding: '12px 16px',
                            cursor: 'pointer',
                            fontSize: 14,
                            color: '#333',
                            display: 'flex',
                            alignItems: 'center',
                            transition: 'background-color 0.2s',
                            borderBottom: '1px solid #f0f0f0'
                        }}
                        onMouseEnter={(e) => e.target.style.backgroundColor = '#f5f5f5'}
                        onMouseLeave={(e) => e.target.style.backgroundColor = 'transparent'}
                        onClick={() => handleAIAction(!['svg', 'mermaid'].includes(artifact.type?.toLowerCase()) ? 'fix_codes_bug' : 'fix_codes_bug_' + artifact.type.toLowerCase())}
                    >
                        <Lightbulb size={16} style={{ marginRight: '8px', color: '#1976d2' }} />
                        {intl.formatMessage({ id: 'fix_codes_bug' }, { defaultMessage: '修复有问题代码' })}
                    </div>
                    <div
                        style={{
                            padding: '12px 16px',
                            cursor: 'pointer',
                            fontSize: 14,
                            color: '#333',
                            display: 'flex',
                            alignItems: 'center',
                            transition: 'background-color 0.2s'
                        }}
                        onMouseEnter={(e) => e.target.style.backgroundColor = '#f5f5f5'}
                        onMouseLeave={(e) => e.target.style.backgroundColor = 'transparent'}
                        onClick={() => handleAIAction('improve_codes')}
                    >
                        <Magic size={16} style={{ marginRight: '8px', color: '#1976d2' }} />
                        {intl.formatMessage({ id: 'improve_codes' }, { defaultMessage: '继续优化' })}
                    </div>
                </div>
            </Popover>

            {
                <>
                    <div
                        className="resize-handle"
                        style={{
                            position: 'absolute',
                            top: 0,
                            right: 0,
                            width: 5,
                            height: '100%',
                            cursor: 'ew-resize',
                        }}
                        onMouseDown={(e) => handleMouseDown(e, 'horizontal')}
                    />

                    <div
                        className="resize-handle"
                        style={{
                            position: 'absolute',
                            bottom: 0,
                            left: 0,
                            width: '100%',
                            height: 5,
                            cursor: 'ns-resize',
                        }}
                        onMouseDown={(e) => handleMouseDown(e, 'vertical')}
                    />

                    {/* <div
                        className="resize-handle"
                        style={{
                            position: 'absolute',
                            bottom: 0,
                            right: 0,
                            width: 10,
                            height: 10,
                            cursor: 'nwse-resize',
                        }}
                        onMouseDown={(e) => handleMouseDown(e, 'corner')}
                    /> */}
                </>
            }

            {/* 用户输入弹窗 */}
            <UserInputModal
                visible={userInputModalVisible}
                title={intl.formatMessage({ id: 'improve_codes' }, { defaultMessage: '继续优化' })}
                placeholder={intl.formatMessage({ id: 'improve_codes_prompt' }, { defaultMessage: '请描述您的优化要求：' })}
                onConfirm={handleUserInputConfirm}
                onCancel={handleUserInputCancel}
            />
        </div>
    );
};

export default Artifact;