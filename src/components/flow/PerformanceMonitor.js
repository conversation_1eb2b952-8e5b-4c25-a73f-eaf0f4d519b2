import React, { useState, useEffect, useRef } from 'react';
import { useSelector } from 'react-redux';

const PerformanceMonitor = ({ nodes, edges, visible = false }) => {
  const [metrics, setMetrics] = useState({
    nodeCount: 0,
    edgeCount: 0,
    renderTime: 0,
    memoryUsage: 0,
    fps: 0
  });

  const frameCount = useRef(0);
  const lastTime = useRef(performance.now());
  const renderStartTime = useRef(0);

  // 监控节点和边的数量
  useEffect(() => {
    setMetrics(prev => ({
      ...prev,
      nodeCount: nodes?.length || 0,
      edgeCount: edges?.length || 0
    }));
  }, [nodes, edges]);

  // 监控渲染性能
  useEffect(() => {
    renderStartTime.current = performance.now();
    
    const measureRenderTime = () => {
      const renderTime = performance.now() - renderStartTime.current;
      setMetrics(prev => ({
        ...prev,
        renderTime: Math.round(renderTime * 100) / 100
      }));
    };

    // 使用 requestAnimationFrame 来测量渲染时间
    const rafId = requestAnimationFrame(measureRenderTime);
    return () => cancelAnimationFrame(rafId);
  });

  // 监控 FPS
  useEffect(() => {
    const measureFPS = () => {
      frameCount.current++;
      const now = performance.now();
      const delta = now - lastTime.current;
      
      if (delta >= 1000) {
        const fps = Math.round((frameCount.current * 1000) / delta);
        setMetrics(prev => ({
          ...prev,
          fps
        }));
        frameCount.current = 0;
        lastTime.current = now;
      }
      
      requestAnimationFrame(measureFPS);
    };

    const rafId = requestAnimationFrame(measureFPS);
    return () => cancelAnimationFrame(rafId);
  }, []);

  // 监控内存使用
  useEffect(() => {
    const measureMemory = () => {
      if (performance.memory) {
        const memoryUsage = Math.round(performance.memory.usedJSHeapSize / 1024 / 1024);
        setMetrics(prev => ({
          ...prev,
          memoryUsage
        }));
      }
    };

    const interval = setInterval(measureMemory, 1000);
    return () => clearInterval(interval);
  }, []);

  // 性能警告
  const getPerformanceWarnings = () => {
    const warnings = [];
    
    if (metrics.nodeCount > 100) {
      warnings.push(`节点数量过多 (${metrics.nodeCount}), 建议使用虚拟化`);
    }
    
    if (metrics.renderTime > 16) {
      warnings.push(`渲染时间过长 (${metrics.renderTime}ms), 可能影响流畅度`);
    }
    
    if (metrics.fps < 30) {
      warnings.push(`FPS过低 (${metrics.fps}), 性能不佳`);
    }
    
    if (metrics.memoryUsage > 100) {
      warnings.push(`内存使用过高 (${metrics.memoryUsage}MB)`);
    }
    
    return warnings;
  };

  if (!visible) return null;

  const warnings = getPerformanceWarnings();

  return (
    <div style={{
      position: 'fixed',
      top: 10,
      right: 10,
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      color: 'white',
      padding: '10px',
      borderRadius: '5px',
      fontSize: '12px',
      fontFamily: 'monospace',
      zIndex: 10000,
      minWidth: '200px'
    }}>
      <div style={{ fontWeight: 'bold', marginBottom: '5px' }}>性能监控</div>
      
      <div>节点数量: {metrics.nodeCount}</div>
      <div>边数量: {metrics.edgeCount}</div>
      <div>渲染时间: {metrics.renderTime}ms</div>
      <div>FPS: {metrics.fps}</div>
      {metrics.memoryUsage > 0 && (
        <div>内存使用: {metrics.memoryUsage}MB</div>
      )}
      
      {warnings.length > 0 && (
        <div style={{ marginTop: '10px', color: '#ff6b6b' }}>
          <div style={{ fontWeight: 'bold' }}>⚠️ 性能警告:</div>
          {warnings.map((warning, index) => (
            <div key={index} style={{ fontSize: '11px', marginTop: '2px' }}>
              • {warning}
            </div>
          ))}
        </div>
      )}
      
      <div style={{ marginTop: '10px', fontSize: '10px', color: '#ccc' }}>
        按 Ctrl+Shift+P 切换显示
      </div>
    </div>
  );
};

export default PerformanceMonitor;
