// import "../../scss/editor.css"
import { EditorCore } from "src/components";
import { with<PERSON><PERSON><PERSON>, useParams, useHistory, useLocation } from "react-router-dom";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { getHistoryDoc } from "src/actions/ticketAction";
import { Alert, Button, CircularProgress } from "@mui/material";
import { DOCS_FOLDER, DOC_PERMISSION } from '../../constants/constants';
import RestoreDocButton from "src/components/RestoreDocButton";
import { SHOW_APP_LIST } from "src/constants/actionTypes";
import { useIntl } from "react-intl";
import { getStateByUser } from "src/reducers/listReducer";


const HistoryDocViewer = ({ item }) => {
    const intl = useIntl();
    const { hid, timeStamp } = item;

    const initDoc = () => {
        return {
            id: 'initDoc',
            blocks: [{ type: 'h1', children: [{ text: '' }] }, { type: 'p', id: new Date().getTime() + 1, children: [{ text: '' }] }]
        };
    }
    const [doc, setDoc] = useState(initDoc());
    // const [content, setContent] = useState([]);
    const [loading, setLoading] = useState(false);
    const currentLocation = useLocation();
    const params = new Proxy(new URLSearchParams(currentLocation.search), {
        get: (searchParams, prop) => searchParams.get(prop) || '',
    });
    const dispatch = useDispatch();

    useEffect(() => {
        if (hid) {
            setLoading(true);
            setDoc(initDoc());
            dispatch(getHistoryDoc({ hid, timeStamp }, (doc) => {
                setLoading(false);
                setDoc(doc);
            }, 'editor'));
        }
    }, [dispatch, hid, timeStamp]);


    if (loading) {
        return <div style={styles.container}>
            <CircularProgress />
        </div>
    }

    return <div id="container" style={styles.container}>

        {
            doc && doc.id != 'initDoc' &&
            <EditorCore
                editorId={hid + '_' + timeStamp}
                content={doc.blocks}
                hid={hid}
                readOnly={true}
            />
        }
    </div>;
};

const styles = {
    container: {
        display: 'flex',
        width: '100%',
        height: '100%',
        alignItems: 'center',
        justifyContent: 'center'
    }
};

export default HistoryDocViewer;