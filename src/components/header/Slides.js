import * as React from 'react';
import { useIntl } from 'react-intl';
import { Divider, FormControl, ListItemText, Menu, MenuItem, Select, Switch, TextField, IconButton, Button, Tooltip, Popover } from '@mui/material';
import { Share as ShareIcon } from '@styled-icons/fluentui-system-regular/Share';
import { Slideshow } from '@styled-icons/boxicons-regular/Slideshow';
import { get_server_host } from '../../utils/serverAPIUtil';
import { AI_ASSISTANT_DIALOG } from 'src/constants/actionTypes';
import { useDispatch } from 'react-redux';
import { Magic } from '@styled-icons/bootstrap';

export default function Slides({ doc }) {
    const intl = useIntl();
    const [anchorEl, setAnchorEl] = React.useState(null);
    const [server_host, setServer_host] = React.useState();
    const dispatch = useDispatch();

    React.useEffect(() => {
        get_server_host().then((value) => setServer_host(value));
    }, []);

    const handleClick = (event) => {
        setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
        setAnchorEl(null);
    };

    return (
        <div>
            <Tooltip title={intl.formatMessage({ id: 'doc_play' })} placement="top">
                <IconButton color="primary"
                    onClick={(e) => {
                        handleClick(e);
                    }}
                >
                    <Slideshow size={20} />
                </IconButton>
            </Tooltip>

            <Popover
                open={Boolean(anchorEl)}
                onClose={handleClose}

                anchorEl={anchorEl}
                anchorOrigin={{
                    vertical: 'bottom',
                    horizontal: 'right',
                }}
                transformOrigin={{
                    vertical: 'top',
                    horizontal: 'left',
                }}
                style={{
                    display: 'flex',
                    flexDirection: 'row',
                    alignItems: 'center',
                    backgroundColor: 'transparent'
                }}
            >
                {/* <MenuItem onClick={() => {
                    window.open(`${server_host}present.html?hid=${doc.hid}&doctype=doc`);
                    handleClose();
                }}>
                    <div style={styles.menuItem}>
                        {
                            intl.formatMessage({ id: 'slides_show' })
                        }
                    </div>
                </MenuItem>

                <MenuItem onClick={() => {
                    window.open(`${server_host}seminar.html?hid=${doc.hid}&doctype=doc`);
                    handleClose();
                }}>
                    <div style={styles.menuItem}>
                        {
                            intl.formatMessage({ id: 'slides_seminar' })
                        }
                    </div>
                </MenuItem>

                <MenuItem onClick={() => {
                    window.open(`${server_host}editor.html?hid=${doc.hid}&doctype=doc`);
                    handleClose();
                }}>
                    <div style={styles.menuItem}>
                        {
                            intl.formatMessage({ id: 'slides_editor' })
                        }
                    </div>
                </MenuItem> */}

                <MenuItem onClick={() => {

                    dispatch({
                        type: AI_ASSISTANT_DIALOG,
                        value: { visible: true, trigger: 'doctool', action: 'xSlides', objType: 'doc', hid: doc.hid }
                    });
                    handleClose();
                }}>
                    <div style={styles.menuItem}>
                        <Magic color='dodgerblue' size={18} />
                        {
                            intl.formatMessage({ id: 'slides_ai_converter' })
                        }
                    </div>
                </MenuItem>
            </Popover>
        </div>
    );
}

const styles = {
    menuItem: {
        display: 'flex',
        width: '160px',
        flexDirection: 'row',
        // justifyContent: 'space-between',
        columnGap: '8px',
        alignContent: 'center',
        alignItems: 'center'
    }
}