/* @flow */

import React, { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useHistory, Link } from 'react-router-dom';

import { LIST_ITEM_SELECTED } from "src/constants/actionTypes";
import { formatDateTime } from 'src/utils/timeFormater';


const HistoryDocItem = ({ item, onClick }) => {
  const itemSelected = useSelector(state => state.uiState.list_item_selected);
  const pressed = item._id === itemSelected;


  const dispatch = useDispatch();

  const history = useHistory();

  return (
    <div
      style={Object.assign({}, styles.container, pressed && styles.pressedItem)}
      onClick={() => {
        onClick(item);
        dispatch({ type: LIST_ITEM_SELECTED, value: item._id });
      }}
    >
      <div style={styles.itemContent}>
        {
          formatDateTime(item.timeStamp)
        }
        <span style={styles.user}>
          {
            '@' + item.eventLog.user.nickname
          }
        </span>
      </div>
      <div style={styles.seperator} />
    </div>
  )
}

const styles = {
  container: {
    backgroundColor: "transparent",
    alignContent: 'center',
    paddingLeft: '6px',
    paddingRight: '4px',

    cursor: 'pointer',
  },
  itemContent: {
    display: 'flex',
    flexDirection: 'column',
    fontSize: '15px',
    paddingTop: '6px',
    paddingBottom: '6px',
  },
  pressedItem: {
    backgroundColor: '#FcFcFc',
  },
  user: {
    fontSize: '13px',
    color: '#666',
    marginTop: '2px',
  },
  barIcon: {
    color: 'dodgerblue'
  },
  seperator: {
    height: 1,
    backgroundColor: 'lightgray',
  },

};

export default HistoryDocItem
