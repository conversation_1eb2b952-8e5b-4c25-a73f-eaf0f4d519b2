import { Box, Dialog, DialogTitle, DialogContent, TextField, Button, DialogActions, Chip, Divider, ListItem, ListItemText, List } from '@mui/material';
import * as React from 'react';
import { useIntl } from 'react-intl';
import { useDispatch, useSelector } from 'react-redux';
import { INVITE_DOC_DIALOG } from 'src/constants/actionTypes';
import { validateEmail, validatePhone } from 'src/utils/validator';

const InviteToDoc = ({ workingSpace, groups, grantPermTo }) => {
  const intl = useIntl();
  const dialogState = useSelector(state => state.uiState.inviteToDocDialog);
  const dispatch = useDispatch();

  const [selectedItems, setSelectedItems] = React.useState([]);
  const [searchText, setSearchText] = React.useState('');
  const [itemTargeted, setItemTargeted] = React.useState(0);
  const [grantToValid, setGrantToValid] = React.useState(true);

  React.useEffect(() => {
    setSelectedItems([]);
  }, [dialogState.visible]);

  const handleDelete = (chipToDelete) => () => {
    setSelectedItems(selectedItems.filter(chip => chip._id !== chipToDelete._id));
  }

  const handleClose = () => {
    setGrantToValid(true);
    dispatch({ type: INVITE_DOC_DIALOG, value: { visible: false } });
  }

  const handleChange = (item) => {
    if (!selectedItems.find(i => i._id === item._id)) {
      setSelectedItems([...selectedItems, item]);
      setItemTargeted(0);
    }
  }

  function handleInputChange(event) {
    setSearchText(event.target.value);
  }

  const onKeyPress = (event) => {
    if (event.key === 'Enter') {
      if (itemTargeted < 0 || itemTargeted >= filteredUsers.length) {
        return;
      }

      if (itemTargeted >= 0 && selectedItems.indexOf(filteredUsers[itemTargeted].user) === -1) {
        setSelectedItems([...selectedItems, filteredUsers[itemTargeted].user]);
        setItemTargeted(0);
      }

      setSearchText("");
    }
  }

  const onKeyDown = (event) => {
    if (event.key === 'Backspace') {
      if (searchText.length === 0) {
        setSelectedItems(selectedItems.slice(0, selectedItems.length - 1));
      }
    } else if (event.key === 'ArrowDown') {
      if (itemTargeted < filteredUsers.length - 1) {
        setItemTargeted(itemTargeted + 1);
      } else {
        setItemTargeted(0);
      }
    } else if (event.key === 'ArrowUp') {
      if (itemTargeted > 0) {
        setItemTargeted(itemTargeted - 1);
      } else {
        setItemTargeted(filteredUsers.length - 1);
      }
    }
  }

  const [invitableUsers, setInvitableUsers] = React.useState([]);
  const [filteredUsers, setFilteredUsers] = React.useState([]);

  React.useEffect(() => {
    if (!dialogState.visible) {
      return;
    }

    const invitedIds = dialogState.grantedTo ? dialogState.grantedTo.map(item => item.objId) : [];
    const selectedIds = selectedItems.map(item => item._id);

    setInvitableUsers(workingSpace.users.concat(groups.map(group => {
      return {
        user: group,
        role: 'group'
      }
    })).filter(item => {
      return item.user && invitedIds.indexOf(item.user._id) === -1 && selectedIds.indexOf(item.user._id) === -1;
    }));
  }, [workingSpace.users, groups, dialogState.grantedTo, selectedItems]);

  React.useEffect(() => {
    const usersFiltered = invitableUsers.filter(({ user }) => {
      return (user.username || user.nickname || user.name).toLowerCase().includes(searchText.toLowerCase());
    });

    setFilteredUsers(usersFiltered);

    if (usersFiltered.length === 0 && (validateEmail(searchText) || validatePhone(searchText))) {
      setFilteredUsers([{ user: { username: searchText, email: validateEmail(searchText) ? searchText : null, type: 'toInvite' }, role: 'guest' }]);
    }

  }, [searchText, invitableUsers]);

  const elmRefs = React.useRef({});

  // Effect that scrolls active element when it changes
  React.useLayoutEffect(() => {
    elmRefs[itemTargeted] && elmRefs[itemTargeted].current.scrollIntoView();
    const selectedItem = elmRefs.current[itemTargeted];
    selectedItem && selectedItem.scrollIntoView();
  }, [itemTargeted])


  return (
    <Dialog
      open={!!dialogState && dialogState.visible}
      onClose={handleClose}
      scroll='paper'
      aria-labelledby="scroll-dialog-title"
      aria-describedby="scroll-dialog-description"
      onKeyDown={onKeyDown}
      onKeyPress={onKeyPress}
    >
      <DialogTitle id="scroll-dialog-title" style={{ display: 'flex', justifyContent: 'space-between' }}>
        <span>{intl.formatMessage({ id: 'invite' })}</span>
      </DialogTitle>
      <DialogContent dividers={true} style={{ height: '60vh', overflowY: 'hidden', display: 'flex', flexDirection: 'column' }}>
        <TextField
          autoComplete='off'
          required
          id="invite"
          label="phone, email, user, groups..."
          // helperText="select a person from list"
          style={{ width: '480px' }}
          value={searchText}
          InputProps={{
            startAdornment: <Box sx={{ display: 'flex', flexWrap: 'wrap' }}> {selectedItems.map((item, index) => (
              <Chip
                key={index}
                tabIndex={-1}
                label={item.name || item.nickname || item.username}
                onDelete={handleDelete(item)}
              />
            ))}
            </Box>,
            onChange: event => {
              handleInputChange(event);
            },
            autoFocus: true,
            error: !grantToValid && selectedItems.length === 0
          }}
        />
        <div style={{ color: 'gray' }}>{intl.formatMessage({ id: 'select_person' })}</div>
        <div style={{ display: 'flex', flexDirection: 'column', marginTop: '10px', overflowY: 'scroll' }}>
          {
            filteredUsers.map((item, index) => {
              const { user, role } = item;
              return (<ListItem
                key={user.username}
                ref={(ref) => {
                  elmRefs.current = { ...elmRefs.current, [index]: ref };
                }}
                button onClick={() => handleChange(user)}
                style={{ backgroundColor: index === itemTargeted ? 'lightgray' : '#eeeeee' }}
              >
                <ListItemText style={{ width: '320px' }} primary={user.name || user.nickname || user.username} secondary={(user.username || user.name) + ' @' + role} />
              </ListItem>)
            })
          }
        </div>
      </DialogContent>
      <DialogActions>
        <Button onClick={handleClose}>{intl.formatMessage({ id: 'cancel' })}</Button>
        <Button variant='contained' onClick={() => {
          const items = [...selectedItems];

          if (searchText && validateEmail(searchText) || validatePhone(searchText)) {
            items.push(filteredUsers[0].user);
          }

          if (items.length === 0) {
            setGrantToValid(false);
            return;
          }
          grantPermTo(items);
          handleClose();
        }}>{intl.formatMessage({ id: 'confirm' })}</Button>
      </DialogActions>
    </Dialog>
  )
}


export default InviteToDoc;
