import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { fetchDocHistories, } from 'src/actions/ticketAction';
import ChangeRILTitleModal from 'src/components/rils/ChangeRILTitleModal';

import { LIST_KEYS } from 'src/constants/actionTypes';
import { getState, getStateByUser } from 'src/reducers/listReducer';

import CommonList from 'src/components/CommonList';
import HistoryDocItem from './HistoryDocItem';


const DocHistoryList = ({ hid, onItemClicked }) => {

  const data_list = useSelector(state => getState(state.doc_history_lists, hid));

  const refreshRils = useSelector(state => state.uiState.refreshRils);

  const pageBy = 'timeStamp';
  const [fetcherProps, setFetcherProps] = useState({
    list_key: hid,
    data_fetcher: fetchDocHistories,
    fetch_params: {
      hid,
      pageBy,
    }
  });

  useEffect(() => {
      setFetcherProps(prevState => {
        return {
          ...prevState,
          invalidate: true,
          fetch_params: !hid? null : {
            hid,
            pageBy
          }
        }
      })
  }, [hid]);

  // useEffect(() => {
  //   setFetcherProps(prevState=> {
  //     return {
  //       ...prevState,
  //       fetch_params: {
  //         hid,
  //         pageBy
  //       }, 
  //       invalidate: true
  //     }
  //   })
  // }, [refreshRils]);

  return <div style={{ display: 'flex', flexDirection: 'column', width: '100%' }}>
    <CommonList
      list_data={data_list}
      fetcherProps={fetcherProps}
      itemRender={(item) => <HistoryDocItem key={item._id} item={item} onClick={onItemClicked} />}
    />
  </div>;
};


export default DocHistoryList
