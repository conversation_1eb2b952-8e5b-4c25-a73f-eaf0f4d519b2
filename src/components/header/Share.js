import * as React from 'react';
import { useIntl } from 'react-intl';
import { Divider, FormControl, ListItemText, Menu, MenuItem, Select, Switch, TextField, IconButton, Button, Tooltip, Popover } from '@mui/material';
import { useDispatch, useSelector } from 'react-redux';
import { fetchOrgs, grantPermission, grantPermissionTo } from 'src/actions/ticketAction';
import { DOC_PERMISSION, DOC_PERMISSION_ARRAY, getConstantKey } from 'src/constants/constants';
import { getState, getStateByUser } from 'src/reducers/listReducer';
import { DOC_ACTIONS, INVITE_DOC_DIALOG, OPERATION_SUCCESS, PRIVATE_DOCS_ACTIONS, SHARED_DOCS_ACTIONS, WORKSPACE_DOCS_ACTIONS } from 'src/constants/actionTypes';
import InviteToDoc from './InviteToDoc';
import { Share as ShareIcon } from '@styled-icons/fluentui-system-regular/Share';
import { Link45deg } from '@styled-icons/bootstrap/Link45deg';
import { getMainDomain } from '@/utils/constants';

export default function Share({ item = {}, fromSpace }) {
    const dispatch = useDispatch();
    const intl = useIntl();
    const [anchorEl, setAnchorEl] = React.useState(null);
    const [openToPulbic, setOpenToPublic] = React.useState(false);
    const [workspacePermission, setWorkspacePermission] = React.useState();
    const [oldSpace, setOldSpace] = React.useState();

    const loginUser = useSelector(state => state.loginIn.user);
    const orgs = useSelector(state => getStateByUser(state.org_lists, loginUser));
    const workingSpace = orgs.items.find(org => org._id === loginUser.workingOrgId) || {};
    const groups = useSelector(state => getState(state.group_lists, loginUser.workingOrgId).items);

    React.useEffect(() => setOldSpace(fromSpace), [fromSpace]);

    const publicPermission = item.grantedTo && item.grantedTo.find(g => g.objId === 'public');
    const grantedGroups = groups && groups.filter(group => !!item.grantedTo.find(g => g.objId === group._id));
    const grantedUsers = workingSpace.users && workingSpace.users.filter(({ user }) => user && !!item.grantedTo.find(g => g.objId === user._id));

    React.useEffect(() => {
        setOpenToPublic(!!publicPermission && !!publicPermission.permission);
        setWorkspacePermission(item.grantedTo && item.grantedTo.find(g => g.objId === loginUser.workingOrgId));
    }, [item.grantedTo, loginUser.workingOrgId]);

    const handleClick = (event) => {
        setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
        setAnchorEl(null);
    };

    const open = Boolean(anchorEl);

    const grantPermTo = (objs) => {
        dispatch(grantPermissionTo({ hid: item.hid, objs }, (doc) => {
            dispatch(fetchOrgs());
            granted(doc);
        }, 'share'))
    }

    const grantPerm = (action, objId, permission) => {
        dispatch(grantPermission({ action, hid: item.hid, objId, permission }, granted, 'share'))
    };

    const granted = (doc) => {
        dispatch({
            type: DOC_ACTIONS.grantedPermission,
            hid: doc.hid,
            grantedTo: doc.grantedTo
        });

        if (doc.newSpace && doc.newSpace != oldSpace) {
            const SPACE_ACTIONS = {
                workspace: WORKSPACE_DOCS_ACTIONS,
                private: PRIVATE_DOCS_ACTIONS,
                shared: SHARED_DOCS_ACTIONS
            };

            !!oldSpace && dispatch({
                type: SPACE_ACTIONS[oldSpace].deleted,
                _id: doc._id,
                item: doc,
                params: { pageBy: 'orderFactor' },
                key: workingSpace._id
            });

            dispatch({
                type: SPACE_ACTIONS[doc.newSpace].added,
                _id: doc._id,
                item: doc,
                params: { pageBy: 'orderFactor' },
                key: workingSpace._id
            });

            return setOldSpace(doc.newSpace);
        }
    }

    const getDocLink = () => {
        if (item.type === 'db') {
            return `${window.location.origin}/#/embed/db?hid=${item.hid}`;
        } else if (item.type === 'slides') {
            return `https://service.${getMainDomain()}/present.html?hid=${item.hid}`;
        }

        return `${window.location.origin}/#/embed/editor?hid=${item.hid}`;
    }

    if (!workingSpace) {
        return null;
    }

    const RemoveMenuItem = ({ objId }) => <div
        className='hoverStand'
        style={styles.menuItem}
        onClick={() => grantPerm('remove', objId, null)}
    >
        Remove
    </div>;

    return (
        <div>
            <Tooltip title={intl.formatMessage({ id: 'share' })} placement="top">
                <IconButton color="primary"
                    onClick={handleClick}
                >
                    <ShareIcon size={22} />
                </IconButton>
            </Tooltip>
            <Popover
                open={open}
                onClose={handleClose}

                anchorEl={anchorEl}
                anchorOrigin={{
                    vertical: 'bottom',
                    horizontal: 'right',
                }}
                transformOrigin={{
                    vertical: 'top',
                    horizontal: 'left',
                }}
                style={{
                    display: 'flex',
                    flexDirection: 'row',
                    alignItems: 'center',
                    backgroundColor: 'transparent'
                }}
            >

                <div
                    className='hoverStand'
                    style={styles.menuItem}
                    onClick={() => {
                        grantPerm(openToPulbic ? 'remove' : 'upsert', 'public', DOC_PERMISSION.view);
                    }}>
                    <div style={styles.menuContent}>
                        {
                            intl.formatMessage({ id: 'open_to_public' })
                        }
                        <Switch checked={openToPulbic} />
                    </div>
                </div>
                {
                    openToPulbic &&
                    <div
                        className='hoverStand'
                        style={styles.menuItem}
                        onClick={() => {
                            grantPerm('upsert', 'public', !!publicPermission && publicPermission.permission >= DOC_PERMISSION.edit ? DOC_PERMISSION.comment : DOC_PERMISSION.edit);
                        }}>
                        <div style={{ ...styles.menuContent, paddingLeft: '20px', paddingRight: '8px' }}>
                            {intl.formatMessage({ id: 'edit' })}
                            <Switch size='small' checked={!!publicPermission && publicPermission.permission >= DOC_PERMISSION.edit} />
                        </div>
                    </div>
                }
                {/* {
                    openToPulbic &&
                    <div
                        className='hoverStand'
                        style={styles.menuItem}
                        onClick={() => {
                            grantPerm('upsert', 'public', !!publicPermission && publicPermission.permission >= DOC_PERMISSION.comment ? DOC_PERMISSION.view : DOC_PERMISSION.comment);
                        }}>
                        <div style={{ ...styles.menuContent, paddingLeft: '20px', paddingRight: '8px' }}>
                            {intl.formatMessage({ id: 'comment' })}
                            <Switch size='small' checked={!!publicPermission && publicPermission.permission >= DOC_PERMISSION.comment} />
                        </div>
                    </div>
                } */}

                <div style={{ width: '100%', height: '0px', borderBottom: '1px solid #ddd' }}></div>
                <div
                    className='hoverStand'
                    style={styles.menuItem}
                    onClick={() => {
                        dispatch({
                            type: INVITE_DOC_DIALOG,
                            value: {
                                visible: true,
                                grantedTo: item.grantedTo,
                            }
                        })
                    }}>
                    <div style={styles.menuContent}>
                        {/* <TextField disabled size='small' placeholder='groups, members, email, mobile' fullWidth id="fullWidth" /> */}
                        <div style={{ border: '1px solid #ccc', borderRadius: '5px', padding: '4px', paddingLeft: '8px', marginTop: '4px', marginBottom: '4px', flex: 1, color: 'gray' }}>groups, members, email, mobile</div>
                        <div style={{ width: 10 }} />
                        <Button size='small' variant='contained'>{intl.formatMessage({ id: 'invite' })}</Button>
                    </div>
                </div>
                <div style={{ width: '100%', height: '0px', borderBottom: '1px solid #ddd' }}></div>
                <div
                    className='hoverStand'
                    style={styles.menuItem}
                >
                    <div style={{ ...styles.menuContent, height: '64px', alignItems: 'center' }}>
                        <div style={{ display: 'flex', flexDirection: 'column' }}>
                            {workingSpace.name}
                            <div style={{ color: 'grey', fontSize: 12 }}>{intl.formatMessage({ id: 'everyone_at_org' })}</div>
                        </div>
                        <FormControl variant="standard">
                            <Select
                                labelId="permission-workspace"
                                id="permission-workspace"
                                value={!!workspacePermission && workspacePermission.permission || DOC_PERMISSION.no_access}
                                onChange={(event) => {
                                    grantPerm('upsert', workingSpace._id, event.target.value);
                                }}
                            >
                                {DOC_PERMISSION_ARRAY.map((item, index) => (
                                    <MenuItem
                                        key={index}
                                        value={item.value}
                                    >
                                        <ListItemText size={'small'} primary={item.text} secondary={item.discription} />
                                    </MenuItem>
                                ))}
                            </Select>
                        </FormControl>
                    </div>
                </div>
                {
                    grantedGroups && grantedGroups.length > 0 &&
                    <div style={{ width: '100%', height: '0px', borderBottom: '1px solid #ddd' }}></div>
                }
                {
                    grantedGroups && grantedGroups.map(group => {
                        const permission = item.grantedTo.find(g => g.objId === group._id).permission;
                        return <div
                            className='hoverStand'
                            style={styles.menuItem}
                            key={group._id}>
                            <div style={{ ...styles.menuContent, height: '64px', alignItems: 'center' }}>
                                <div style={{ display: 'flex', flexDirection: 'column' }}>
                                    {group.name}
                                    <div style={{ color: 'grey', fontSize: 12 }}>{intl.formatMessage({ id: 'everyone_at_group' })}</div>
                                </div>
                                <FormControl variant="standard">
                                    <Select
                                        labelId="permission-workspace"
                                        id="permission-workspace"
                                        value={permission}
                                        onChange={(event) => {
                                            grantPerm('upsert', group._id, event.target.value);
                                        }}
                                    >
                                        {DOC_PERMISSION_ARRAY.map((item, index) => (
                                            <MenuItem
                                                key={index}
                                                value={item.value}
                                            >
                                                <ListItemText primary={item.text} secondary={item.discription} />
                                            </MenuItem>
                                        ))}
                                        <RemoveMenuItem objId={group._id} />
                                    </Select>
                                </FormControl>
                            </div>
                        </div>
                    })
                }
                {
                    grantedUsers && grantedUsers.length > 0 &&
                    <div style={{ width: '100%', height: '0px', borderBottom: '1px solid #ddd' }}></div>
                }
                {
                    grantedUsers && grantedUsers.map(({ user, role }) => {
                        const permission = item.grantedTo.find(g => g.objId === user._id).permission;
                        return <div
                            className='hoverStand'
                            style={styles.menuItem}
                            key={user._id}>
                            <div style={{ ...styles.menuContent, height: '64px', alignItems: 'center' }}>
                                <div style={{ display: 'flex', flexDirection: 'column' }}>
                                    {user.nickname}
                                    <div style={{ color: 'grey', fontSize: 12 }}>{`@${role}`}</div>
                                </div>
                                <FormControl variant="standard">
                                    <Select
                                        labelId="permission-workspace"
                                        id="permission-workspace"
                                        value={permission}
                                        onChange={(event) => {
                                            grantPerm('upsert', user._id, event.target.value);
                                        }}
                                    >
                                        {DOC_PERMISSION_ARRAY.map((item, index) => (
                                            <MenuItem
                                                key={index}
                                                value={item.value}
                                            >
                                                <ListItemText primary={item.text} secondary={item.discription} />
                                            </MenuItem>
                                        ))}
                                        <RemoveMenuItem objId={user._id} />
                                    </Select>
                                </FormControl>
                            </div>
                        </div>
                    })
                }
                <div style={{ width: '100%', height: '0px', borderBottom: '1px solid #ddd' }}></div>
                <div
                    className='hoverStand'
                    style={styles.menuItem}

                    onClick={() => {
                        navigator.clipboard.writeText(getDocLink()).then(() => {
                            dispatch({
                                type: OPERATION_SUCCESS,
                                message: intl.formatMessage({ id: 'copied' }),
                            });
                        }, () => {
                            dispatch({
                                type: OPERATION_SUCCESS,
                                message: intl.formatMessage({ id: 'copy_failed' }),
                            });
                        });
                    }}
                >
                    <div
                        className="hoverStand"
                        style={{
                            padding: '10px 0px',
                            columnGap: '8px',
                        }}
                    >
                        {/* <div style={styles.menuContent}> */}
                        <Link45deg size={18} style={styles.icon} />
                        {
                            intl.formatMessage({ id: 'copy_link' })
                        }
                        {/* </div> */}
                    </div>
                </div>
            </Popover>
            <InviteToDoc workingSpace={workingSpace} groups={groups} grantPermTo={grantPermTo} />
        </div>
    );
}

const styles = {
    menuContent: {
        display: 'flex',
        width: '460px',
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignContent: 'center',
        alignItems: 'center'
    },

    menuItem: {
        padding: '8px',
        paddingLeft: '16px',
        paddingRight: '16px',
        fontSize: 15
    }
}