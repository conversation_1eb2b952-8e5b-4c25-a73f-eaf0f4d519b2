import * as React from 'react';
import { useIntl } from 'react-intl';
import { Menu, MenuItem, IconButton, Tooltip, ListItemText, Popover } from '@mui/material';
import { useDispatch } from 'react-redux';

import { MoreHoriz } from '@styled-icons/material/MoreHoriz';
import { EXPORT_DOC_DIALOG, IMPORT_DOC_DIALOG } from 'src/constants/actionTypes';
import ExportModal from './ExportModal';

export default function MoreMenu({ doc }) {
    const dispatch = useDispatch();
    const intl = useIntl();
    const [anchorEl, setAnchorEl] = React.useState(null);

    const handleClick = (event) => {
        setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
        setAnchorEl(null);
    };

    const open = Boolean(anchorEl);


    return (
        <div>
            <Tooltip title={intl.formatMessage({ id: 'more_menu' })} placement="top">
                <IconButton color="primary"
                    onClick={handleClick}
                >
                    <MoreHoriz size={22} />
                </IconButton>
            </Tooltip>

            <Popover
                open={open}
                onClose={handleClose}

                anchorEl={anchorEl}
                anchorOrigin={{
                    vertical: 'bottom',
                    horizontal: 'right',
                }}
                transformOrigin={{
                    vertical: 'top',
                    horizontal: 'left',
                }}
                style={{
                    display: 'flex',
                    flexDirection: 'row',
                    alignItems: 'center',
                    backgroundColor: 'transparent'
                }}
            >
                <MenuItem
                    style={styles.menuItem}
                    onClick={() => {
                        handleClose();
                        dispatch({
                            type: IMPORT_DOC_DIALOG,
                            value: {
                                visible: true
                            }
                        })
                    }}>
                    <ListItemText
                        primary={
                            intl.formatMessage({ id: 'import' })
                        }
                    />
                </MenuItem>

                <MenuItem
                    style={styles.menuItem}
                    onClick={() => {
                        handleClose();
                        dispatch({
                            type: EXPORT_DOC_DIALOG,
                            value: {
                                visible: true
                            }
                        })
                    }}>
                    <ListItemText
                        primary={
                            intl.formatMessage({ id: 'export' })
                        }
                        secondary={
                            intl.formatMessage({ id: `export_formats_${doc?.type}` })
                        }
                    />
                </MenuItem>
            </Popover>

            <ExportModal doc_to_export={doc} />
        </div>
    );
}

const styles = {
    menuItem: {
        display: 'flex',
        width: '200px',
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignContent: 'center',
        alignItems: 'center'
    }
}