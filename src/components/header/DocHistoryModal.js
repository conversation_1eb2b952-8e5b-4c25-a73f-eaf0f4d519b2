import * as React from 'react';
import { DOC_ACTIONS, DOC_HISTORY_DIALOG, REFRESH_EDITOR_CONTENT, LIST_ITEM_SELECTED } from 'src/constants/actionTypes';
import { useDispatch, useSelector } from 'react-redux';
import { DialogContent, Dialog, Button, DialogTitle, Tooltip } from '@mui/material';
// import { getStateByUser } from 'src/reducers/listReducer';
import { useIntl } from 'react-intl';
import DocHistoryList from './DocHistoryList';
import HistoryDocViewer from './HistoryDocViewer';
import { restoreDocFromHistory } from 'src/actions/ticketAction';
import { Link, useHistory, useLocation } from 'react-router-dom';


const drawerWidth = 240;

const DocHistoryModal = () => {
    const dialogState = useSelector(state => state.uiState.docHistoryDialog) || { visible: false };
    const dispatch = useDispatch();
    const intl = useIntl();

    const history = useHistory();
    const currentLocation = useLocation();
    const params = new Proxy(new URLSearchParams(currentLocation.search), {
        get: (searchParams, prop) => searchParams.get(prop),
    });

    const handleClose = () => {
        setCurrentPage(null);
        dispatch({ type: LIST_ITEM_SELECTED, value: null });
        dispatch({ type: DOC_HISTORY_DIALOG, value: { visible: false } });
    }

    const [currentPage, setCurrentPage] = React.useState();

    const handleRestore = () => {
        if (!currentPage) {
            return;
        }

        dispatch(restoreDocFromHistory({ hid: currentPage.hid, timeStamp: currentPage.timeStamp, space: params.space }, (item) => {
            dispatch({ type: DOC_ACTIONS.updated, item });

            setTimeout(() => {
                dispatch({ type: REFRESH_EDITOR_CONTENT, value: currentPage.hid });
                handleClose();
            }, 1000);
        }, 'history'));
    }

    return (
        <Dialog
            open={!!dialogState && dialogState.visible}
            onClose={handleClose}
            scroll='paper'
            aria-labelledby="scroll-dialog-title"
            aria-describedby="scroll-dialog-description"
            maxWidth='lg'
        >
            <div style={{ width: '100%', display: 'flex', flexDirection: 'row', justifyContent: 'space-between' }}>
                <div style={{ padding: '8px', fontWeight: 500, color: 'gray' }}>{intl.formatMessage({ id: 'history' })}</div>
                <Button variant='text' onClick={handleClose}>{intl.formatMessage({ id: 'close' })}</Button>
            </div>
            <DialogContent dividers={true} style={{ display: 'flex', flexDirection: 'row', height: 720, width: 1080, padding: 0 }}>
                <div
                    style={{
                        width: drawerWidth,
                        backgroundColor: '#f0f0f0',
                        flexShrink: 0,
                        height: '100%',
                        overflowY: 'auto',
                    }}

                >
                    <DocHistoryList hid={dialogState.hid} onItemClicked={(item) => {
                        setCurrentPage(item);
                    }} />
                </div>
                <div
                    style={{ flexGrow: 1, height: '100%', width: '100%', padding: 0, overflowY: 'auto', paddingLeft: '30px' }}
                >
                    {
                        currentPage &&
                        <HistoryDocViewer item={currentPage} />
                    }
                    {
                        !currentPage &&
                        <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', height: '100%' }}>
                            <div style={{ fontSize: '30px', fontWeight: 500, color: 'gray' }}>{intl.formatMessage({ id: 'no_history_doc_selected' })}</div>
                        </div>
                    }
                </div>
                {
                    currentPage &&
                    <div style={{ position: 'absolute', bottom: 0, right: 10, width: '100%', display: 'flex', flexDirection: 'row-reverse' }}>
                        <div style={{ display: 'flex', flexDirection: 'row', alignItems: 'center' }}>
                            <div style={{ color: '#888', fontSize: '12px', padding: '8px' }}>{intl.formatMessage({ id: 'restore_doc_tip' })}</div>
                            <Tooltip title={intl.formatMessage({ id: 'restore_doc_tooltip' })} placement="top">
                                <Button onClick={handleRestore}>{intl.formatMessage({ id: 'restore' })}</Button>
                            </Tooltip>
                        </div>
                    </div>
                }
            </DialogContent>
        </Dialog>
    );
}

export default DocHistoryModal;
