import React, { useState, useEffect } from 'react'
import { App<PERSON>ontent, AppS<PERSON>bar, AppHeader, AppBreadcrumb } from '../components/index'
import GlobalDndContext from '../components/DndContext';
import AppListContainer from 'src/components/MainListContainer';
import AddToRILModal from 'src/components/rils/AddToRILModal';
import SpinnerAndToast from 'src/components/SpinnerAndToast';
import { useLocation, useHistory } from 'react-router-dom';
import { fetchOrgs, fetchToComfirmOrgs, switchWorkspace, getAppConfig, getLngSetting, getServingPrivilege } from '../actions/ticketAction';
import { getStateByUser } from '../reducers/listReducer';
import { useSelector, useDispatch } from 'react-redux';
import { CircularProgress } from '@mui/material';
import { QuestionFab } from './QuestionFab';
import FeedbacksModal from 'src/components/feedbacks/FeedbacksModal';
import { AI_SETTINGS, APP_CONFIG, OCCUPATION_DIALOG } from 'src/constants/actionTypes';
import AIModal from 'src/components/ai/AIModal';
import ConfirmDialog from 'src/components/ConfirmDialog';
import AlertDialog from 'src/components/AlertDialog';
import InviteFriendsModal from '@/components/feedbacks/InviteFriendsModal';
import AppUpgradeModal from 'src/components/feedbacks/AppUpgradeModal';
import { ImageUploadModal } from 'src/components/common/ImageUploadModal';
import { PromptModal } from '@/components/settings/PromptModal';
import PromptsModal from '@/components/settings/PromptsModal';
import { LlmAPIKeyModal } from '../components/settings/LlmAPIKeyModal';
import ReactGA from 'react-ga4';
import SettingsModal from '../components/SettingsModal';
import { PromoteModal } from '../components/common/PromoteModal';
import OccupationDialog from '../components/OccupationDialog';

const DefaultLayout = () => {
  const [collapsed, setCollapsed] = useState(false);
  const [toggled, setToggled] = useState(false);
  const appList = useSelector(state => state.uiState.appList);
  const loginUser = useSelector(state => state.loginIn.user);
  const showAppList = useSelector(state => state.uiState.showAppList);
  const aiSettings = useSelector(state => state.uiState.aiSettings) || {};

  const location = useLocation();

  const dispatch = useDispatch();
  const history = useHistory();
  const orgs = useSelector(state => getStateByUser(state.org_lists, loginUser));

  if (!loginUser) {
    history.push('/login');;
  }

  useEffect(() => {
    // ReactGA.initialize(GOOGLE_ANALYTICS_TRACKID);
    ReactGA.send({ hitType: "pageview", page: "/funblocks", title: "FunBlocks" });
  }, [])

  useEffect(() => {
    dispatch(getAppConfig({ service: 'funblocks' }, (data) => {
      dispatch({
        type: APP_CONFIG,
        value: data
      })
    }));

    dispatch(fetchOrgs(items => {
      if (!items || !items.length) {
        history.push('/newworkspace');
      }
    }));

    dispatch(fetchToComfirmOrgs(items => {
      if (items && items.length) {
        history.push('/newworkspace');
      }
    }));

    dispatch(getLngSetting({}));

    dispatch(getServingPrivilege({ privilege: 'usePrivateAIApi' }, (privileged) => {
      if (!privileged) {
        dispatch({
          type: AI_SETTINGS,
          value: {
            ...aiSettings,
            aiProvider: 'funblocks'
          }
        })
      }
    }))

  }, [dispatch]);

  // Check user occupation after login
  useEffect(() => {
    if (loginUser && loginUser._id && !loginUser.occupation) {
      // Show occupation dialog if user doesn't have occupation set
      dispatch({
        type: OCCUPATION_DIALOG,
        value: {
          visible: true,
          allowClose: false, // Don't allow closing without selection
          onSuccess: (user) => {
            // User occupation updated successfully
          }
        }
      });
    }
  }, [loginUser, dispatch]);


  if (!orgs || !orgs.items || !orgs.items.length) {
    return <CircularProgress />
  }

  const workingSpace = orgs.items.find(org => org._id === loginUser.workingOrgId);
  if (!workingSpace) {
    dispatch(switchWorkspace({ orgId: orgs.items[0]._id }, null, 'defaultLayout'));
  }

  const handleCollapsedChange = () => {
    setCollapsed(!collapsed);
  };

  const handleToggleSidebar = (value) => {
    setToggled(value);
  };

  return (
    <div className='full-height' style={{ display: "flex", flexDirection: 'row' }}>
      <GlobalDndContext>
        <div className='sidebar-fixed'>
          <AppSidebar
            collapsed={collapsed}
            toggled={toggled}
            handleToggleSidebar={handleToggleSidebar}
          />
        </div>
        {
          showAppList && (loginUser.rilBindedOrgId === loginUser.workingOrgId) &&
          <AppListContainer pathname={appList} />
        }
        <div className="app-main">
          {
            ((!location.state || !location.state.hideHeader) && !['/flow', '/slidesEditor'].includes(location.pathname)) &&
            <AppHeader
              handleCollapseSidebar={handleCollapsedChange}
            >
              <AppBreadcrumb />
            </AppHeader>
          }
          <AppContent />

          <QuestionFab />
          <FeedbacksModal />
          <InviteFriendsModal />
          <AppUpgradeModal />
          <AIModal />
          <LlmAPIKeyModal />
          <ConfirmDialog />
          <SettingsModal />
          <PromoteModal />
          <AlertDialog />
          <ImageUploadModal />
          <PromptModal />
          <PromptsModal />
          <OccupationDialog />
        </div>
        <AddToRILModal />
      </GlobalDndContext>
      <SpinnerAndToast />
    </div>
  )
}

export default DefaultLayout
