import { useEffect, useState } from 'react';
import { useIntl } from 'react-intl';
import { Fab, MenuItem, Popover } from '@mui/material';
import { Trophy } from "@styled-icons/bootstrap/Trophy";
import { Close } from '@styled-icons/material/Close';
import { useDispatch } from 'react-redux';
import { FEEDBACKS_DIALOG, INVITE_FRIENDS_DIALOG } from 'src/constants/actionTypes';
import { useLocation } from 'react-router-dom';
import { getMainDomain } from '@/utils/constants';

export const QuestionFab = (props) => {
    const intl = useIntl();
    const [anchorEl, setAnchorEl] = useState(null);
    const dispatch = useDispatch();
    const currentLocation = useLocation();

    const handleClose = () => {
        setAnchorEl(null);
    }

    return (
        <div style={{
            position: 'absolute',
            right: ['/slidesViewer', '/slidesEditor'].includes(currentLocation.pathname) ? '18px' : '24px',
            bottom: ['/slidesViewer', '/slidesEditor'].includes(currentLocation.pathname) ? '80px' : '24px',
            zIndex: 1000,
            display: ['/brainstorming', '/flow', '/slidesEditor'].includes(currentLocation.pathname)? "none" : "block"
        }}>
            <Fab style={{ backgroundColor: 'white', color: 'gray' }} size='small' aria-label="add"
                onClick={(e) => setAnchorEl(e.currentTarget)}
            >
                {
                    !anchorEl &&
                    <Trophy size={20} color='goldenrod' />
                }
                {
                    anchorEl &&
                    <Close size={24} />

                }
            </Fab>

            <Popover
                open={Boolean(anchorEl)}
                onClose={handleClose}

                anchorEl={anchorEl}
                anchorOrigin={{
                    vertical: 'top',
                    horizontal: 'left',
                }}
                transformOrigin={{
                    vertical: 'bottom',
                    horizontal: 'left',
                }}
                style={{
                    display: 'flex',
                    flexDirection: 'row',
                    alignItems: 'center',
                }}
            >
                 <MenuItem
                    onClick={() => {
                        dispatch({ type: INVITE_FRIENDS_DIALOG, value: { visible: true } });
                        handleClose();
                    }}
                    style={{display: 'flex', flexDirection: 'column', alignItems: 'start', width: '100%'}}
                >
                    <div>
                        {intl.formatMessage({ id: 'invite_friends' })}
                    </div>
                    <div style={{ fontSize: '12px', color: 'goldenrod' }}>
                        {intl.formatMessage({ id: 'invite_friends_desc' })}
                    </div>
                </MenuItem>
                <MenuItem
                    onClick={() => {
                        dispatch({ type: FEEDBACKS_DIALOG, value: { visible: true } });
                        handleClose();
                    }}
                >
                    <div>
                        {intl.formatMessage({ id: 'feedback' })}
                    </div>
                </MenuItem>

                <MenuItem
                    onClick={() => {
                        window.open(`https://${getMainDomain()}`, '_blank');
                        handleClose();
                    }}
                >
                    <div>
                        {intl.formatMessage({ id: 'homepage' })}
                    </div>
                </MenuItem>
            </Popover>
        </div>
    );
}