import React from "react";
import PropertyMenu from "src/components/dbeditor/PropertyMenu";
import styles from "./task-list-header.module.css";

export const TaskListHeaderDefault: React.FC<{
  headerHeight: number;
  rowWidth: number;
  fontFamily: string;
  fontSize: string;
  paddingLeft: number;
  view: {
    _id: string;
    dataSource: string;
  };
  titlePropertyId: string;
}> = ({ headerHeight, fontFamily, fontSize, rowWidth, paddingLeft, view, titlePropertyId }) => {
  return (
    <div
      className={styles.ganttTable}
      style={{
        fontFamily: fontFamily,
        fontSize: fontSize,
        minWidth: rowWidth - paddingLeft,
        paddingLeft: paddingLeft,
      }}
    >
      <div
        className={styles.ganttTable_Header}
        style={{
          height: headerHeight,
        }}
      >
        <div
          className={styles.ganttTable_HeaderItem}
          style={{
          }}
        >
          <PropertyMenu
            viewId={view._id}
            columnId={titlePropertyId} 
            hid={view.dataSource} 
            showPlace={'timeline'} 
            onHideProperty={undefined} 
            style={undefined}   
            trigger={undefined}       
          />
        </div>
      </div>
    </div>
  );
};
