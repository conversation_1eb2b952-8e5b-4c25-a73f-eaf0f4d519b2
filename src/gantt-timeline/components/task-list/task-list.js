import React, { useEffect, useRef, useState } from "react";
import { BarTask } from "../../types/bar-task";
import { Task } from "../../types/public-types";
import DBList from "../../../components/dbeditor/DBList";
import { useSelector } from "react-redux";

export const TaskList = ({
  headerHeight,
  fontFamily,
  fontSize,
  rowWidth,
  rowHeight,
  scrollY,
  tasks,
  setTasks,
  setProperties,
  selectedTask,
  visibleDateRange,
  setSelectedTask,
  onExpanderClick,
  locale,
  ganttHeight,
  taskListRef,
  horizontalContainerClass,
  TaskListHeader,
  TaskListTable,
  taskListWidth,

  view,
}) => {
  const horizontalContainerRef = useRef(null);
  useEffect(() => {
    if (horizontalContainerRef.current) {
      horizontalContainerRef.current.scrollTop = scrollY;
    }
  }, [scrollY]);

  const [listData, setListData] = useState({});
  const [collapses, setCollapses] = useState([]);
  const [lineBy, setLineBy] = React.useState(null);

  const hid = view.dataSource;
  const doc = useSelector(state => state.docs.byId[hid]);

  useEffect(() => {
    if (!doc || !doc.meta || !doc.meta.properties || !view.timeline) {
      return;
    }

    let useTimePair = view.timeline && view.timeline.useTimePair;
    let range = !useTimePair && doc.meta.properties.find(p => p.name === (view.timeline && view.timeline.range));
    let start = useTimePair && doc.meta.properties.find(p => p.name === (view.timeline && view.timeline.start));
    let end = useTimePair && doc.meta.properties.find(p => p.name === (view.timeline && view.timeline.end));

    if(!range && !start && !end) {
      return;
    }

    setLineBy({
      useTimePair,
      range,
      start,
      end,
      color: view.timeline.color,
    });
  }, [doc, view.timeline]);

  // const makeTimeLineData = useCallback(() => {
  //   const 

  const makeTimeLineData = (lane, tasks, collapses) => {
    if (lane.id) {
      tasks.push({
        id: lane.id,
        name: lane.title,
        type: 'group',
      })
    }

    if (collapses.includes(lane.id)) {
      return;
    }

    lane.cards && lane.cards.forEach(card => {
      const data = card.metadata.data || {};
      let startDate, endDate;

      if (lineBy.useTimePair && lineBy.start && lineBy.end) {
        startDate = new Date(data[lineBy.start.name]);
        endDate = new Date(data[lineBy.end.name]);
      } else {
        let lineData = data[lineBy.range.name];
        if (lineData) {
          startDate = new Date(lineData[0]);
          endDate = new Date(lineData[1]);
        }
      }

      let cardData = {
        id: card.id,
        group: lane.id,

        type: 'task',
        name: card.title,
        backgroundColor: lineBy.color,
        metadata: {
          ...card.metadata,
        }
      }

      if(startDate && (startDate !== 'Invalid Date' && !isNaN(startDate)) || endDate && (endDate !== 'Invalid Date' && !isNaN(endDate))) {
        if(startDate && (startDate !== 'Invalid Date' && !isNaN(startDate))) {
          cardData.start = startDate;
        }

        if(endDate && (endDate !== 'Invalid Date' && !isNaN(endDate))) {
          cardData.end = endDate;
        } 

        if(!cardData.start) {
          cardData.start = cardData.end;
        }

        if(!cardData.end) {
          cardData.end = cardData.start;
        }
      }

      tasks.push(cardData);
    });

    lane.lanes && lane.lanes.forEach(l => {
      makeTimeLineData(l, tasks, collapses);
    });
  }


  useEffect(() => {
    if(!lineBy) {
      return;
    }

    let tasks = [];
    makeTimeLineData(listData, tasks, collapses);

    setTasks(tasks);
  }, [listData, lineBy, collapses, setTasks]);

  const [titlePropertyId, setTitlePropertyId] = useState(null);

  const headerProps = {
    headerHeight,
    fontFamily,
    fontSize,
    rowWidth,
    taskListWidth,
    titlePropertyId,
    view: view,
    paddingLeft: 44
  };

  return (
    <div ref={taskListRef}
      style={{
        borderRight: "1px solid #e6e4e4",
        zIndex: 3,
        backgroundColor: "#fff",
        taskListWidth
      }}
    >
      <TaskListHeader {...headerProps} />

      <div
        ref={horizontalContainerRef}
        className={horizontalContainerClass}
        style={ganttHeight ? { height: ganttHeight } : {}}
      >
        <DBList
          view={view}
          listData={listData}
          setListData={setListData}
          setProperties={setProperties}
          showItemDivider={true}
          listWidth={taskListWidth}
          noExtraPaddingForGroupDivider={true}
          collapses={collapses}
          setCollapses={setCollapses}
          onlyTitle={true}
          titleEditable={true}
          setTitlePropertyId={setTitlePropertyId}
        />
      </div>
    </div>
  );
};
