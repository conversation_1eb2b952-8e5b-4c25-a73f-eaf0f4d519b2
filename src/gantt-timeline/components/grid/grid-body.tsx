import React, { ReactChild } from "react";
import { Task, ViewMode } from "../../types/public-types";
import { addToDate } from "../../helpers/date-helper";
import styles from "./grid.module.css";

export type GridBodyProps = {
  tasks: Task[];
  dates: Date[];
  svgWidth: number;
  rowHeight: number;
  columnWidth: number;
  todayColor: string;
  viewMode: ViewMode;
  rtl: boolean;
};
export const GridBody: React.FC<GridBodyProps> = ({
  tasks,
  dates,
  rowHeight,
  svgWidth,
  columnWidth,
  todayColor,
  viewMode,
  rtl,
}) => {
  let y = 0;
  const rowLines: ReactChild[] = [
    <line
      key="RowLineFirst"
      x="0"
      y1={0}
      x2={svgWidth}
      y2={0}
      className={styles.gridRowLine}
    />,
  ];
  for (const task of tasks) {
    // rowLines.push(
    //   <line
    //     key={"RowLine" + task.id + y}
    //     x="0"
    //     y1={y + rowHeight}
    //     x2={svgWidth}
    //     y2={y + rowHeight}
    //     className={styles.gridRowLine}
    //   />
    // );
    y += rowHeight;
  }

  const now = new Date();
  let tickX = 0;
  const ticks: ReactChild[] = [];
  const weekends: ReactChild[] = [];
  let today: ReactChild = <rect />;
  let currentLine: ReactChild = <line />;
  let currentPoint: ReactChild = <circle />;

  for (let i = 0; i < dates.length; i++) {
    const date = dates[i];

    if (viewMode !== ViewMode.Day) {
      const dayOfWeek = date.getDay();
      const isWeekend = (dayOfWeek === 6) || (dayOfWeek === 0);

      if (isWeekend) {
        weekends.push(<rect
          key={`Weekend${i}`}
          x={tickX}
          y={0}
          width={columnWidth}
          height={y}
          className={styles.weekend}
          fill={'#f5f5f5'}
        />);
      }
    }

    if (now > date && i < dates.length - 1 && now < dates[i + 1]) {
      let x = tickX + ((now.getTime() - date.getTime()) / (dates[i + 1].getTime() - date.getTime())) * columnWidth;
      currentLine = <line
          key={'currentline'}
          x1={x}
          y1={0}
          x2={x}
          y2={y}
          className={styles.currentLine}
        />

      currentPoint = <circle
          key={'currentpoint'}
          cx={x}
          cy={0}
          r={6}
          fill={'dodgerblue'}
        />
    }

    ticks.push(
      <line
        key={date.getTime()}
        x1={tickX}
        y1={0}
        x2={tickX}
        y2={y}
        className={styles.gridTick}
      />
    );

    if (
      (i + 1 !== dates.length &&
        date.getTime() < now.getTime() &&
        dates[i + 1].getTime() >= now.getTime()) ||
      // if current date is last
      (i !== 0 &&
        i + 1 === dates.length &&
        date.getTime() < now.getTime() &&
        addToDate(
          date,
          date.getTime() - dates[i - 1].getTime(),
          "millisecond"
        ).getTime() >= now.getTime())
    ) {
      today = (
        <rect
          x={tickX}
          y={0}
          width={columnWidth}
          height={y}
          fill={todayColor}
        />
      );
    }
    // rtl for today
    if (
      rtl &&
      i + 1 !== dates.length &&
      date.getTime() >= now.getTime() &&
      dates[i + 1].getTime() < now.getTime()
    ) {
      today = (
        <rect
          x={tickX + columnWidth}
          y={0}
          width={columnWidth}
          height={y}
          fill={todayColor}
        />
      );
    }
    tickX += columnWidth;
  }
  return (
    <g className="gridBody">
      {/* <g className="rowLines">{rowLines}</g> */}
      <g className="ticks">{ticks}</g>
      <g className="weekends">{weekends}</g>
      <g className="current">{currentLine}</g>
      <g className="current">{currentPoint}</g>
    </g>
  );
};
