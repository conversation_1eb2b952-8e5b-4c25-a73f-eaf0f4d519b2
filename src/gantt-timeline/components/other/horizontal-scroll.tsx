import React, { SyntheticEvent, useRef, useEffect } from "react";
import styles from "./horizontal-scroll.module.css";

export const HorizontalScroll: React.FC<{
  scroll: number;
  svgWidth: number;
  taskListWidth: number;
  rtl: boolean;
  onScroll: (event: SyntheticEvent<HTMLDivElement>) => void;
  containerScrollState: {
    scrollLeft: number;
    scrollWidth: number;
    clientWidth: number;
    clientX: number;
  }
  setContainerScrollState: (containerScrollState: object) => void;
  setContainerEdgeState: (containerEdgeState: object) => void;
}> = ({ scroll, svgWidth, taskListWidth, rtl, onScroll, containerScrollState, setContainerScrollState, setContainerEdgeState }) => {
  const scrollRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (scrollRef.current) {
      scrollRef.current.scrollLeft = scroll;
    }
  }, [scroll]);

  useEffect(() => {
    if (scrollRef.current) {
      setContainerScrollState({
        ...containerScrollState,
        scrollLeft: scrollRef.current.scrollLeft,
        scrollWidth: scrollRef.current.scrollWidth,
        clientWidth: scrollRef.current.clientWidth,
        clientX: scrollRef.current.getBoundingClientRect().x,
      });

      // console.log('scroll ref..............', scrollRef.current.scrollWidth, scrollRef.current.clientWidth, scrollRef.current.getBoundingClientRect().x);
    }
  }, [scrollRef]);

  const handleScrollToEdge = (e) => {
    if (e.target.id!="horizontalScrollContainer") {
        return;
    }

    const scrollLeft = e.target.scrollLeft;
    const clientWidth = e.target.clientWidth;
    const scrollWidth = e.target.scrollWidth;

    let scrolledLeftest = false;
    let scrolledRightest = false;

    if (scrollLeft < 20) {
        scrolledLeftest = true;
    } else {
        scrolledLeftest = false;
    }

    if (scrollWidth - scrollLeft - clientWidth < 20) {
        scrolledRightest = true;
    } else {
        scrolledRightest = false;
    }

    setContainerEdgeState({ scrolledLeftest, scrolledRightest });
}

  return (
    <div
      dir="ltr"
      id="horizontalScrollContainer"
      style={{
        margin: rtl
          ? `0px ${taskListWidth}px 0px 0px`
          : `0px 0px 0px ${taskListWidth}px`,
        zIndex: 5
      }}
      className={styles.scroll}
      onScroll={onScroll}
      onMouseUp={handleScrollToEdge}
      onMouseDown={() =>{
        setContainerEdgeState(({ scrolledLeftest: false, scrolledRightest: false }));
      }}
      ref={scrollRef}
    >
      <div style={{ width: svgWidth, height: 1 }} />
    </div>
  );
};
