import { ArrowLeftSquareFill } from '@styled-icons/bootstrap/ArrowLeftSquareFill';
import { ArrowRightSquareFill } from '@styled-icons/bootstrap/ArrowRightSquareFill';
import { useRef, useEffect } from 'react';
import { TaskItem } from '../gantt/TaskItem';

export const TaskPinpoint = ({ tasks, direction, x, cellHeight, visibleDateRange, pointTo, scrollY, marginTop, ganttHeight, properties, titleProperty }) => {
    const iconBtnSize = cellHeight;

    const containerRef = useRef(null);

    useEffect(() => {
        if (containerRef.current) {
            containerRef.current.scrollTop = scrollY;
        }
    }, [scrollY]);

    return (<div
        style={{
            ...styles.container,
            [direction]: x,
            marginTop,
            zIndex: 4,
            width: 26,
        }}
    >
        <div
            ref={containerRef}

            style={{
                height: ganttHeight,
                overflowY: 'hidden',
            }}>
            {
                tasks.map((t, index) => {
                    const btnStyle = {
                        ...styles.arrow,
                        height: iconBtnSize,
                    };

                    if (direction === 'left' && t.start && t.start <= visibleDateRange.leftest) {
                        const top = index * cellHeight - (scrollY > 0 ? scrollY : 0) + (cellHeight - 22) / 2;

                        return <div
                            key={index}
                            style={btnStyle}
                            onClick={() => pointTo(t, direction)}
                        >
                            <div style={{ color: '#aaa', backgroundColor: 'white', height: 16, display: 'flex', alignItems: 'center' }}><ArrowLeftSquareFill size={16} /> </div>
                            {
                                t.end > visibleDateRange.leftest && top >= 0 &&
                                <TaskItem
                                    task={t}
                                    key={t.id + index}
                                    properties={properties}
                                    titleProperty={titleProperty}
                                    style={{
                                        position: 'absolute',
                                        width: 'max-content',
                                        whiteSpace: 'nowrap',
                                        left: 20,
                                        top,
                                    }}
                                />
                            }
                        </div>

                    }

                    if (direction === 'right' && t.end && t.end > visibleDateRange.rightest) {

                        return <div
                            key={index}
                            style={btnStyle}
                            onClick={() => pointTo(t, direction)}
                        >
                            <div style={{ color: '#aaa', backgroundColor: 'white', height: 16, display: 'flex', alignItems: 'center' }}><ArrowRightSquareFill size={16} /> </div>
                        </div>
                    }

                    return <div
                        key={index}
                        style={btnStyle}
                    />
                })

            }
        </div>
    </div>)
}

const styles = {
    container: {
        position: 'absolute',
        height: '-webkit-fill-available',
    },
    arrow: {
        cursor: "pointer",
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'flex-start',
        display: 'flex'
    }
}
