import React, { ReactChild } from "react";
import { ViewMode } from "../../types/public-types";
import { TopPartOfCalendar } from "./top-part-of-calendar";
import {
  getCachedDateTimeFormat,
  getDaysInMonth,
  getLocalDayOfWeek,
  getLocaleMonth,
  getWeekNumberISO8601,
} from "../../helpers/date-helper";
import { DateSetup } from "../../types/date-setup";
import styles from "./calendar.module.css";
import { useIntl } from "react-intl";

export type CalendarProps = {
  dateSetup: DateSetup;
  locale: string;
  viewMode: ViewMode;
  rtl: boolean;
  headerHeight: number;
  columnWidth: number;
  fontFamily: string;
  fontSize: string;
  middleDayRef: React.RefObject<SVGTextElement>;
};

export const Calendar: React.FC<CalendarProps> = ({
  dateSetup,
  locale,
  viewMode,
  rtl,
  headerHeight,
  columnWidth,
  fontFamily,
  fontSize,
  middleDayRef,
}) => {
  const intl = useIntl();

  const bottom_y = headerHeight * 0.8;
  const topDefaultHeight = headerHeight * 0.5;
  const topTextY = topDefaultHeight * 0.8;

  const getCalendarValuesForYear = () => {
    const topValues: ReactChild[] = [];
    const bottomValues: ReactChild[] = [];
    for (let i = 0; i < dateSetup.dates.length; i++) {
      const date = dateSetup.dates[i];
      const bottomValue = getLocaleMonth(date, locale);
      bottomValues.push(
        <text
          ref={date == dateSetup.middleDay ? middleDayRef : undefined}
          key={bottomValue + date.getFullYear()}
          y={bottom_y}
          x={columnWidth * i + columnWidth * 0.5}
          className={styles.calendarBottomText}
        >
          {bottomValue}
        </text>
      );
      if (
        i === 0 ||
        date.getFullYear() !== dateSetup.dates[i - 1].getFullYear()
      ) {
        const topValue = date.getFullYear().toString();
        let xText: number;
        if (rtl) {
          xText = (6 + i + date.getMonth() + 1) * columnWidth;
        } else {
          xText = (6 + i - date.getMonth()) * columnWidth;
        }
        topValues.push(
          <TopPartOfCalendar
            key={topValue}
            value={topValue}
            x1Line={columnWidth * i}
            y1Line={0}
            y2Line={topDefaultHeight}
            xText={xText}
            yText={topTextY}
          />
        );
      }
    }
    return [topValues, bottomValues];
  };

  const getCalendarValuesForMonth = () => {
    const topValues: ReactChild[] = [];
    const bottomValues: ReactChild[] = [];
    const dates = dateSetup.dates;
    const now = new Date();
    for (let i = 0; i < dates.length; i++) {
      const date = dates[i];

      const isToday = date < now && dates[i + 1] > now;
      if (isToday) {
        bottomValues.push(
          <circle cx={columnWidth * i + columnWidth * 0.5} cy={bottom_y - 4} r="14px" fill="dodgerblue" />
        )
      }

      const bottomValue = date.getDate().toString();
      bottomValues.push(
        <text
          key={date.getTime()}
          y={bottom_y}
          x={columnWidth * i + columnWidth * 0.5}
          className={styles.calendarBottomText}
          stroke={isToday && "white"}
        >
          {bottomValue}
        </text>
      );


      if (
        i + 1 !== dates.length &&
        date.getMonth() !== dates[i + 1].getMonth()
      ) {
        const topValue = intl.formatDate(date, {
          month: 'short',
        });
        topValues.push(
          <TopPartOfCalendar
            key={topValue + date.getFullYear()}
            value={topValue}
            x1Line={columnWidth * (i + 1)}
            y1Line={0}
            y2Line={topDefaultHeight}
            xText={
              columnWidth * (i + 1) -
              getDaysInMonth(date.getMonth(), date.getFullYear()) *
              columnWidth *
              0.5
            }
            yText={topTextY}
          />
        );
      }
    }
    return [topValues, bottomValues];
  };

  const getCalendarValuesForWeek = () => {
    const topValues: ReactChild[] = [];
    const bottomValues: ReactChild[] = [];
    let weeksCount: number = 1;
    const dates = dateSetup.dates;
    const now = new Date();
    for (let i = dates.length - 1; i >= 0; i--) {
      const date = dates[i];

      const isToday = date < now && dates[i + 1] > now;
      if (isToday) {
        bottomValues.push(
          <rect
            key={date.getTime() + "today"}
            y={bottom_y - 16}
            x={columnWidth * i + columnWidth * 0.5 -34 }
            width={68}
            height={22}
            rx="10"
            ry="10"
            fill="dodgerblue"
          />
        )
      }

      const bottomValue = intl.formatDate(date, {
        weekday: 'short',
        day: 'numeric',
      });

      bottomValues.push(
        <text
          key={date.getTime()}
          y={bottom_y}
          x={columnWidth * i + columnWidth * 0.5}
          className={styles.calendarBottomText}
          stroke={isToday && "white"}
        >
          {bottomValue}
        </text>
      );

      if (
        i + 1 !== dates.length &&
        getWeekNumberISO8601(date) !== getWeekNumberISO8601(dates[i + 1])
      ) {
        const topValue = getWeekNumberISO8601(date);

        topValues.push(
          <TopPartOfCalendar
            key={topValue + date.getFullYear()}
            value={`W${topValue}`}
            x1Line={columnWidth * (i + 1)}
            y1Line={0}
            y2Line={topDefaultHeight}
            xText={
              columnWidth * (i + 1) -
              7 *
              columnWidth *
              0.5
            }
            yText={topTextY}
          />
        );
      }


      // let topValue = "";
      // if (i === 0 || date.getMonth() !== dates[i - 1].getMonth()) {
      //   // top
      //   topValue = `${getLocaleMonth(date, locale)}, ${date.getFullYear()}`;
      // }
      // // bottom
      // const bottomValue = `W${getWeekNumberISO8601(date)}`;

      // bottomValues.push(
      //   <text
      //     key={date.getTime()}
      //     y={headerHeight * 0.8}
      //     x={columnWidth * (i + +rtl)}
      //     className={styles.calendarBottomText}
      //   >
      //     {bottomValue}
      //   </text>
      // );

      // if (topValue) {
      //   // if last day is new month
      //   if (i !== dates.length - 1) {
      //     topValues.push(
      //       <TopPartOfCalendar
      //         key={topValue}
      //         value={topValue}
      //         x1Line={columnWidth * i + weeksCount * columnWidth}
      //         y1Line={0}
      //         y2Line={topDefaultHeight}
      //         xText={columnWidth * i + columnWidth * weeksCount * 0.5}
      //         yText={topDefaultHeight * 0.9}
      //       />
      //     );
      //   }
      //   weeksCount = 0;
      // }
      // weeksCount++;
    }
    return [topValues, bottomValues];
  };

  const getCalendarValuesForPartOfDay = () => {
    const topValues: ReactChild[] = [];
    const bottomValues: ReactChild[] = [];
    const ticks = viewMode === ViewMode.HalfDay ? 2 : 4;
    const dates = dateSetup.dates;
    for (let i = 0; i < dates.length; i++) {
      const date = dates[i];
      const bottomValue = getCachedDateTimeFormat(locale, {
        hour: "numeric",
      }).format(date);

      bottomValues.push(
        <text
          key={date.getTime()}
          y={bottom_y}
          x={columnWidth * (i + +rtl)}
          className={styles.calendarBottomText}
          fontFamily={fontFamily}
        >
          {bottomValue}
        </text>
      );
      if (i === 0 || date.getDate() !== dates[i - 1].getDate()) {
        const topValue = `${getLocalDayOfWeek(
          date,
          locale,
          "short"
        )}, ${date.getDate()} ${getLocaleMonth(date, locale)}`;
        topValues.push(
          <TopPartOfCalendar
            key={topValue + date.getFullYear()}
            value={topValue}
            x1Line={columnWidth * i + ticks * columnWidth}
            y1Line={0}
            y2Line={topDefaultHeight}
            xText={columnWidth * i + ticks * columnWidth * 0.5}
            yText={topTextY}
          />
        );
      }
    }

    return [topValues, bottomValues];
  };

  const getCalendarValuesForDay = () => {
    const topValues: ReactChild[] = [];
    const bottomValues: ReactChild[] = [];
    const dates = dateSetup.dates;
    const now = new Date();

    for (let i = 0; i < dates.length; i++) {
      const date = dates[i];

      const isToday = date < now && dates[i + 1] > now;
      if (isToday) {
        bottomValues.push(
          <circle cx={columnWidth * (i + +rtl)} cy={bottom_y - 4} r="14px" fill="dodgerblue" />
        )
      }

      const bottomValue = getCachedDateTimeFormat(locale, {
        hour: "numeric",
      }).format(date);

      bottomValues.push(
        <text
          key={date.getTime()}
          y={bottom_y}
          x={columnWidth * (i + +rtl)}
          className={styles.calendarBottomText}
          fontFamily={fontFamily}
          stroke={isToday && "white"}
        >
          {bottomValue}
        </text>
      );
      if (i === 0 || date.getDate() !== dates[i - 1].getDate()) {
        const topValue = intl.formatDate(date, {
          day: 'numeric',
        });
        const topPosition = (date.getHours() - 24) / 2;
        topValues.push(
          <TopPartOfCalendar
            key={topValue + date.getFullYear()}
            value={topValue}
            x1Line={columnWidth * i}
            y1Line={0}
            y2Line={topDefaultHeight}
            xText={columnWidth * (i + topPosition)}
            yText={topTextY}
          />
        );
      }
    }

    return [topValues, bottomValues];
  };

  let topValues: ReactChild[] = [];
  let bottomValues: ReactChild[] = [];
  switch (dateSetup.viewMode) {
    case ViewMode.Month:
      [topValues, bottomValues] = getCalendarValuesForMonth();
      break;
    case ViewMode.Week:
      [topValues, bottomValues] = getCalendarValuesForWeek();
      break;
    case ViewMode.Day:
      [topValues, bottomValues] = getCalendarValuesForDay();
      break;

  }
  return (
    <g className="calendar" fontSize={fontSize} fontFamily={fontFamily}>
      <rect
        x={0}
        y={0}
        width={columnWidth * dateSetup.dates.length}
        height={headerHeight}
        className={styles.calendarHeader}
      />
      {bottomValues} {topValues}
    </g>
  );
};
