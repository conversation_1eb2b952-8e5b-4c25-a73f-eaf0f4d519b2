import { Selector } from "src/components/common/Selector"
import { ViewMode } from "src/gantt-timeline/types/public-types"
import { ViewConstants } from "./ViewConstants"
import { useIntl } from "react-intl"

import { IosArrowLeft } from '@styled-icons/fluentui-system-filled/IosArrowLeft';
import { IosArrowRight } from '@styled-icons/fluentui-system-filled/IosArrowRight';

export const ViewController = ({ viewMode, setViewMode, navigateToDate, naviStep }) => {
    const intl = useIntl();
    const options = Object.keys(ViewConstants).map(key => {
        return {
            value: key,
            label: intl.formatMessage({ id: `view_timeline_by_${ViewConstants[key].name.toLowerCase()}` }),
        }
    });
    return (
        <div style={{
            position: "absolute",
            right: '26px',
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            marginTop: '6px',
            backgroundColor: 'white'
        }}>
            <div style={{
                width: 30,
                marginLeft: -30,
                height: '22px',
                backgroundImage: 'linear-gradient(to left, white 20%, rgba(255, 255, 255, 0) 100%)',
            }} />
            <Selector
                inputStyle={{
                    border: 'none',
                    fontSize: '14px',
                    color: 'gray'
                }}
                options={options}
                value={viewMode || ViewMode.Day}
                onChange={(value) => {
                    setViewMode(value)
                }}
            />

            <div
                className="hoverStand"
                style={{
                    display: 'flex',
                    flexDirection: 'row',
                    alignItems: 'center',
                }}
                onClick={() => {
                    naviStep('back');
                }}
            >
                <IosArrowLeft size={14} style={{ color: 'gray' }} />
            </div>
            <div
                className="hoverStand"
                style={{
                    fontSize: '15px',
                    padding: '2px'
                }}
                onClick={() => navigateToDate(new Date())}
            >
                {
                    intl.formatMessage({ id: 'today' })
                }
            </div>

            <div
                className="hoverStand"
                style={{
                    display: 'flex',
                    flexDirection: 'row',
                    alignItems: 'center',
                }}
                onClick={() => {
                    naviStep('forward');
                }}
            >
                <IosArrowRight size={14} style={{ color: 'gray' }} />
            </div>
            <div style={{
                width: 30,
                marginRight: -30,
                height: '22px',
                backgroundImage: 'linear-gradient(to right, white 20%, rgba(255, 255, 255, 0) 100%)',
            }} />
        </div>
    )
}
