import { CellView } from "src/components/dbeditor/CellView";
import { useIntl } from "react-intl";

export const TaskItem = ({ task, titleProperty, properties, style }) => {
    const metadata = task.metadata;
    const intl = useIntl();

    let titleElement = <CellView
        property={titleProperty}
        value={task.name || intl.formatMessage({ id: 'newpage' })}
        hasDocAttached={false}
        alwaysShowDocIcon={false}
        style={{
            maxWidth: undefined
        }}
        aliginLeft={undefined} />;

    let bodyElements = null;
    if (properties && metadata && metadata.data) {
        bodyElements = properties.map(p => {
            if (p.type === 'Title' || !metadata.data[p.name]) {
                return null;
            }

            return <CellView
                key={p.name}
                property={p}
                value={metadata.data[p.name]}
                hasDocAttached={false}
                style={undefined}
                aliginLeft={undefined}
                alwaysShowDocIcon={undefined} />
        }).filter(e => e);
    }


    return <div
        style={{
            ...style,
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'flex-start',
            flex: 1,
            columnGap: '10px',
        }}
    >
        {titleElement}
        {
            bodyElements && bodyElements.length > 0 &&
            <div
                style={{
                    display: 'flex',
                    flexDirection: 'row',
                    alignItems: 'center',
                    columnGap: '12px',
                    fontSize: '0.7rem',
                }}>
                {
                    bodyElements
                }
            </div>
        }
    </div>
}