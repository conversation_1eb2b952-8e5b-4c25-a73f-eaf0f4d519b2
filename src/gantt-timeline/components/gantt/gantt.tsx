import React, {
  useState,
  SyntheticEvent,
  useRef,
  useEffect,
  useMemo,
} from "react";
import { ViewMode, GanttProps, Task } from "../../types/public-types";
import { GridProps } from "../grid/grid";
import { ganttDateRange, seedDates, moveMiddleDay } from "../../helpers/date-helper";
import { CalendarProps } from "../calendar/calendar";
import { TaskGanttContentProps } from "./task-gantt-content";
import { TaskListHeaderDefault } from "../task-list/task-list-header";
import { TaskListTableDefault } from "../task-list/task-list-table";
import { StandardTooltipContent, Tooltip } from "../other/tooltip";
import { VerticalScroll } from "../other/vertical-scroll";
import { TaskList } from "../task-list/task-list";
import { TaskGantt } from "./task-gantt";
import { BarTask } from "../../types/bar-task";
import { convertToBarTasks } from "../../helpers/bar-helper";
import { GanttEvent } from "../../types/gantt-task-actions";
import { DateSetup } from "../../types/date-setup";
import styles from "./gantt.module.css";
import { HorizontalScroll } from "../other/horizontal-scroll";
import { removeHiddenTasks, sortTasks } from "../../helpers/other-helper";
import { ViewController } from "../calendar/ViewController";
import { TaskPinpoint } from '../other/TaskPinpoint';
import moment from "moment";
import { ViewConstants } from "../calendar/ViewConstants";
import { useIntl } from "react-intl";
import { TaskItem } from "./TaskItem";

export const Gantt: React.FunctionComponent<GanttProps> = ({
  view,
  headerHeight = 68,
  listCellWidth = 300,
  rowHeight = 33,
  ganttHeight = 400,
  // viewMode = ViewMode.Day,
  locale = "en-GB",
  barFill = 90,
  barCornerRadius = 3,
  barProgressColor = "#a3a3ff",
  barProgressSelectedColor = "#8282f5",
  barBackgroundColor = "#b8c2cc",
  barBackgroundSelectedColor = "#aeb8c2",
  projectProgressColor = "#7db59a",
  projectProgressSelectedColor = "#59a985",
  projectBackgroundColor = "#fac465",
  projectBackgroundSelectedColor = "#f7bb53",
  milestoneBackgroundColor = "#f1c453",
  milestoneBackgroundSelectedColor = "#f29e4c",
  rtl = false,
  handleWidth = 8,
  timeStep = 300000,
  arrowColor = "grey",
  fontFamily = "Arial, Roboto, Oxygen, Ubuntu, Cantarell, Fira Sans, Droid Sans, Helvetica Neue",
  fontSize = "14px",
  arrowIndent = 20,
  todayColor = "rgba(252, 248, 227, 0.5)",
  viewDate,
  TooltipContent = StandardTooltipContent,
  TaskListHeader = TaskListHeaderDefault,
  TaskListTable = TaskListTableDefault,

  onCreateTask,
  onDateChange,
  onProgressChange,
  onDoubleClick,
  onDelete,
  onSelect,
  onExpanderClick,
}) => {
  const [viewMode, setViewMode] = useState<ViewMode>(ViewMode.Week);
  const viewConstants = useMemo(() => ViewConstants[viewMode], [viewMode]);
  const wrapperRef = useRef<HTMLDivElement>(null);
  const taskListRef = useRef<HTMLDivElement>(null);
  const middleDayRef = useRef<SVGTextElement>(null);
  const intl = useIntl();

  const [tasks, setTasks] = useState<Task[]>([]);
  const [properties, setProperties] = useState([]);

  const titleProperty = useMemo(() => {
    return properties.find((p) => p.type === "Title");
  }, [properties]);

  const [dateSetup, setDateSetup] = useState<DateSetup>(() => {
    const [startDate, endDate] = ganttDateRange(new Date(), viewMode);
    return { viewMode, middleDay: moment(new Date()).hours(0).minutes(0).seconds(0).milliseconds(0).toDate(), dates: seedDates(startDate, endDate, viewMode) };
  });

  const [visibleDateRange, setVisibleDateRange] = useState({
    leftest: new Date(),
    rightest: new Date(),
    leftestIndex: 0,
    rightestIndex: 0,
  })

  const [containerEdgeState, setContainerEdgeState] = useState({ scrolledLeftest: false, scrolledRightest: false });
  const [containerScrollState, setContainerScrollState] = useState({
    scrollTop: 0,
    scrollLeft: 0,
    scrollWidth: 0,
    scrollHeight: 0,
    clientWidth: 0,
    clientHeight: 0,
    clientX: 0,
    clientY: 0,
  });

  useEffect(() => {
    if (wrapperRef.current) {
      setContainerScrollState({
        scrollTop: wrapperRef.current.scrollTop,
        scrollLeft: wrapperRef.current.scrollLeft,
        scrollWidth: wrapperRef.current.scrollWidth,
        scrollHeight: wrapperRef.current.scrollHeight,
        clientWidth: wrapperRef.current.clientWidth,
        clientHeight: wrapperRef.current.clientHeight,
        clientX: 0,
        clientY: 0
      });
    }
  }, [wrapperRef.current]);

  useEffect(() => {
    const { scrolledLeftest, scrolledRightest } = containerEdgeState;
    let middleDay = dateSetup.middleDay;

    if (!scrolledLeftest && !scrolledRightest) {
      return;
    }

    middleDay = moveMiddleDay(dateSetup.middleDay, viewMode, scrolledLeftest && "left" || scrolledRightest && "right");

    const [startDate, endDate] = ganttDateRange(middleDay, viewMode);
    const dates = seedDates(startDate, endDate, viewMode);

    setDateSetup(() => {
      return { viewMode, middleDay, dates };
    });

    setTimeout(() => {
      navigateToColumn(scrolledLeftest ? viewConstants.step : (dates.length - 1 - viewConstants.step));
    }, 100);

  }, [containerEdgeState.scrolledLeftest, containerEdgeState.scrolledRightest]);


  useEffect(() => {
    if (dateSetup.viewMode === viewMode) {
      return;
    }

    let middleDay = dateSetup.middleDay;
    const [startDate, endDate] = ganttDateRange(middleDay, viewMode);
    const dates = seedDates(startDate, endDate, viewMode);

    setDateSetup({
      viewMode, middleDay, dates
    });

    const pinTo = new Date((visibleDateRange.leftest.getTime() + visibleDateRange.rightest.getTime()) / 2);


    setTimeout(() => {
      navigateToDate(pinTo, dates);
    }, 100);

  }, [viewMode, visibleDateRange]);

  useEffect(() => {
    if (!dateSetup || !dateSetup.dates || !dateSetup.dates.length) return;
    const dates = dateSetup.dates;

    const { scrollLeft, clientWidth } = containerScrollState;

    const leftest = ~~(scrollLeft / columnWidth);
    const rightest = ~~((scrollLeft + clientWidth) / columnWidth);

    setVisibleDateRange({
      leftest: dates[leftest],
      rightest: dates[rightest],
      leftestIndex: leftest,
      rightestIndex: rightest,
    })
  }, [containerScrollState, viewMode, dateSetup.dates]);

  const [currentViewDate, setCurrentViewDate] = useState<Date | undefined>(
    undefined
  );

  const [taskListWidth, setTaskListWidth] = useState(listCellWidth);
  const [svgContainerWidth, setSvgContainerWidth] = useState(0);
  const [svgContainerHeight, setSvgContainerHeight] = useState(ganttHeight);
  const [barTasks, setBarTasks] = useState<BarTask[]>([]);
  const [ganttEvent, setGanttEvent] = useState<GanttEvent>({
    action: "",
  });
  const taskHeight = useMemo(
    () => (rowHeight * barFill) / 100,
    [rowHeight, barFill]
  );

  const [selectedTask, setSelectedTask] = useState<BarTask>();
  const [failedTask, setFailedTask] = useState<BarTask | null>(null);

  let columnWidth = viewConstants.columnWidth;
  const [svgWidth, setSvgWidth] = useState(0);

  useEffect(() => {

    setSvgWidth(dateSetup.dates.length * columnWidth);
  }, [dateSetup.dates.length]);

  const ganttFullHeight = barTasks.length * rowHeight;

  const [scrollY, setScrollY] = useState(0);
  const [scrollX, setScrollX] = useState(-1);
  const [ignoreScrollEvent, setIgnoreScrollEvent] = useState(false);
  const setTimeBtnRef = useRef<HTMLDivElement>(null);
  const hoveredRowRef = useRef<HTMLDivElement>(null);
  // const setTimeTextRef = useRef<HTMLDivElement>(null);

  // task change events
  useEffect(() => {
    let filteredTasks: Task[];
    if (onExpanderClick) {
      filteredTasks = removeHiddenTasks(tasks);
    } else {
      filteredTasks = tasks;
    }
    filteredTasks = filteredTasks.sort(sortTasks);
    // const [startDate, endDate] = ganttDateRange(middleDay, viewMode);
    // let newDates = seedDates(startDate, endDate, viewMode);
    // if (rtl) {
    //   newDates = newDates.reverse();
    //   if (scrollX === -1) {
    //     setScrollX(newDates.length * columnWidth);
    //   }
    // }
    // setDateSetup({ dates: newDates, viewMode });

    // console.log("task change.........", newDates);

    setBarTasks(
      convertToBarTasks(
        filteredTasks,
        dateSetup.dates,
        columnWidth,
        rowHeight,
        taskHeight,
        barCornerRadius,
        handleWidth,
        rtl,
        barProgressColor,
        barProgressSelectedColor,
        barBackgroundColor,
        barBackgroundSelectedColor,
        projectProgressColor,
        projectProgressSelectedColor,
        projectBackgroundColor,
        projectBackgroundSelectedColor,
        milestoneBackgroundColor,
        milestoneBackgroundSelectedColor
      )
    );
  }, [
    tasks,
    viewMode,
    rowHeight,
    barCornerRadius,
    columnWidth,
    taskHeight,
    handleWidth,
    barProgressColor,
    barProgressSelectedColor,
    barBackgroundColor,
    barBackgroundSelectedColor,
    projectProgressColor,
    projectProgressSelectedColor,
    projectBackgroundColor,
    projectBackgroundSelectedColor,
    milestoneBackgroundColor,
    milestoneBackgroundSelectedColor,
    rtl,
    scrollX,
    onExpanderClick,
    dateSetup.dates,
  ]);

  useEffect(() => {
    if (
      viewMode === dateSetup.viewMode &&
      ((viewDate && !currentViewDate) ||
        (viewDate && currentViewDate?.valueOf() !== viewDate.valueOf()))
    ) {
      const dates = dateSetup.dates;
      const index = dates.findIndex(
        (d, i) =>
          viewDate.valueOf() >= d.valueOf() &&
          i + 1 !== dates.length &&
          viewDate.valueOf() < dates[i + 1].valueOf()
      );
      if (index === -1) {
        return;
      }
      setCurrentViewDate(viewDate);
      setScrollX(columnWidth * index);
    }
  }, [
    viewDate,
    columnWidth,
    dateSetup.dates,
    viewMode,
    currentViewDate,
    setCurrentViewDate,
  ]);

  useEffect(() => {
    const { changedTask, action } = ganttEvent;
    if (changedTask) {
      if (action === "delete") {
        setGanttEvent({ action: "" });
        setBarTasks(barTasks.filter(t => t.id !== changedTask.id));
      } else if (
        action === "move" ||
        action === "end" ||
        action === "start" ||
        action === "progress"
      ) {
        const prevStateTask = barTasks.find(t => t.id === changedTask.id);
        if (
          prevStateTask &&
          (prevStateTask.start.getTime() !== changedTask.start.getTime() ||
            prevStateTask.end.getTime() !== changedTask.end.getTime() ||
            prevStateTask.progress !== changedTask.progress)
        ) {
          // actions for change
          const newTaskList = barTasks.map(t =>
            t.id === changedTask.id ? changedTask : t
          );
          setBarTasks(newTaskList);
        }
      }
    }
  }, [ganttEvent, barTasks]);

  useEffect(() => {
    if (failedTask) {
      setBarTasks(barTasks.map(t => (t.id !== failedTask.id ? t : failedTask)));
      setFailedTask(null);
    }
  }, [failedTask, barTasks]);

  useEffect(() => {
    if (!listCellWidth) {
      setTaskListWidth(0);
    }
    if (taskListRef.current) {
      setTaskListWidth(taskListRef.current.offsetWidth);
    }
  }, [taskListRef, listCellWidth]);

  useEffect(() => {
    if (wrapperRef.current) {
      setSvgContainerWidth(wrapperRef.current.offsetWidth - taskListWidth);
    }
  }, [wrapperRef, taskListWidth]);

  useEffect(() => {
    if (ganttHeight) {
      setSvgContainerHeight(ganttHeight + headerHeight);
    } else {
      setSvgContainerHeight(tasks.length * rowHeight + headerHeight);
    }
  }, [ganttHeight, tasks]);

  // scroll events
  useEffect(() => {
    const handleWheel = (event: WheelEvent) => {
      if (event.shiftKey || event.deltaX) {
        const scrollMove = event.deltaX ? event.deltaX : event.deltaY;
        let newScrollX = scrollX + scrollMove;
        if (newScrollX < 0) {
          newScrollX = 0;
        } else if (newScrollX > svgWidth) {
          newScrollX = svgWidth;
        }
        setScrollX(newScrollX);
        event.preventDefault();
      } else if (ganttHeight) {
        let newScrollY = scrollY + event.deltaY;
        if (newScrollY < 0) {
          newScrollY = 0;
        } else if (newScrollY > ganttFullHeight - ganttHeight) {
          newScrollY = ganttFullHeight - ganttHeight;
        }
        if (newScrollY !== scrollY) {
          setScrollY(newScrollY);
          event.preventDefault();
        }
      }

      setIgnoreScrollEvent(true);
    };

    // subscribe if scroll is necessary
    if (wrapperRef.current) {
      wrapperRef.current.addEventListener("wheel", handleWheel, {
        passive: false,
      });
    }
    return () => {
      if (wrapperRef.current) {
        wrapperRef.current.removeEventListener("wheel", handleWheel);
      }
    };
  }, [wrapperRef.current, scrollY, scrollX, ganttHeight, svgWidth, rtl]);

  const handleScrollY = (event) => {
    setContainerScrollState(prev => {
      return {
        ...prev,
        scrollTop: event.target.scrollTop > 0 ? event.target.scrollTop : 0,
        scrollHeight: event.target.scrollHeight,
        clientHeight: event.target.clientHeight,
        clientY: event.target.getBoundingClientRect().y,
      }
    })

    if (scrollY !== event.currentTarget.scrollTop && !ignoreScrollEvent) {
      setScrollY(event.currentTarget.scrollTop);
    }
    setIgnoreScrollEvent(false);
  };

  const handleScrollX = (event) => {
    setContainerScrollState(prev => {
      return {
        ...prev,
        scrollLeft: event.target.scrollLeft,
        scrollWidth: event.target.scrollWidth,
        clientWidth: event.target.clientWidth,
        clientX: event.target.getBoundingClientRect().x,
      }
    })

    if (scrollX !== event.currentTarget.scrollLeft && !ignoreScrollEvent) {
      setScrollX(event.currentTarget.scrollLeft);
    }
    setIgnoreScrollEvent(false);
  };

  /**
   * Handles arrow keys events and transform it to new scroll
   */
  const handleKeyDown = (event: React.KeyboardEvent<HTMLDivElement>) => {
    // event.preventDefault();
    let newScrollY = scrollY;
    let newScrollX = scrollX;

    let isX = true;
    switch (event.key) {
      case "Down": // IE/Edge specific value
      case "ArrowDown":
        newScrollY += rowHeight;
        isX = false;
        break;
      case "Up": // IE/Edge specific value
      case "ArrowUp":
        newScrollY -= rowHeight;
        isX = false;
        break;
      case "Left":
      case "ArrowLeft":
        newScrollX -= columnWidth;
        break;
      case "Right": // IE/Edge specific value
      case "ArrowRight":
        newScrollX += columnWidth;
        break;
    }
    if (isX) {
      if (newScrollX < 0) {
        newScrollX = 0;
      } else if (newScrollX > svgWidth) {
        newScrollX = svgWidth;
      }
      setScrollX(newScrollX);
    } else {
      if (newScrollY < 0) {
        newScrollY = 0;
      } else if (newScrollY > ganttFullHeight - ganttHeight) {
        newScrollY = ganttFullHeight - ganttHeight;
      }
      setScrollY(newScrollY);
    }
    setIgnoreScrollEvent(true);
  };

  /**
   * Task select event
   */
  const handleSelectedTask = (taskId: string) => {
    const newSelectedTask = barTasks.find(t => t.id === taskId);
    const oldSelectedTask = barTasks.find(
      t => !!selectedTask && t.id === selectedTask.id
    );
    if (onSelect) {
      if (oldSelectedTask) {
        onSelect(oldSelectedTask, false);
      }
      if (newSelectedTask) {
        onSelect(newSelectedTask, true);
      }
    }
    setSelectedTask(newSelectedTask);
  };
  const handleExpanderClick = (task: Task) => {
    if (onExpanderClick && task.hideChildren !== undefined) {
      onExpanderClick({ ...task, hideChildren: !task.hideChildren });
    }
  };

  const navigateToTask = (task: Task, direction: string) => {
    const dates = dateSetup.dates;

    if (task.start > dates[0] && task.end < dates[dates.length - 1]) {
      const dateDelta = dates[1].getTime() - dates[0].getTime();

      const x = direction === 'left'
        ? ((~~((task.start.getTime() - dates[0].getTime()) / dateDelta) - 1) * columnWidth || 0)
        : ((~~((task.end.getTime() - dates[0].getTime()) / dateDelta) + 1) * columnWidth - containerScrollState.clientWidth);
      setScrollX(x);
    } else {
      let middleDay = task.start;

      const [startDate, endDate] = ganttDateRange(middleDay, viewMode);
      const dates = seedDates(startDate, endDate, viewMode);

      setDateSetup(() => {
        return { viewMode, middleDay, dates };
      });
    }
  }

  const navigateToColumn = (column: number) => {
    const x = (column * columnWidth - containerScrollState.clientWidth / 2);
    setScrollX(x);
  }

  const navigateToDate = (date: Date, dates) => {
    if (!dates) {
      dates = dateSetup.dates;
    }

    if (date > dates[0] && date < dates[dates.length - 1]) {
      const dateDelta = dates[1].getTime() - dates[0].getTime();
      const column = ~~((date.getTime() - dates[0].getTime()) / dateDelta);

      navigateToColumn(column);
    } else {
      let middleDay = date;

      const [startDate, endDate] = ganttDateRange(middleDay, viewMode);
      const dates = seedDates(startDate, endDate, viewMode);

      setDateSetup(() => {
        return { viewMode, middleDay, dates };
      });

      navigateToDate(date, dates);
    }
  }

  const naviStep = (direction: string) => {
    let middleDay = new Date((visibleDateRange.rightest.getTime() + visibleDateRange.leftest.getTime()) / 2);
    middleDay = direction === 'back' ? moment(middleDay).subtract(viewConstants.naviStep, viewConstants.columnUnit).toDate() : moment(middleDay).add(viewConstants.naviStep + 1, viewConstants.columnUnit).toDate();

    navigateToDate(middleDay, dateSetup.dates);
  }


  const [setTimeBtnContent, setTimeBtnContentState] = useState('');

  const handleMouseMove = (event: React.MouseEvent<HTMLDivElement>) => {
    const { id } = event.target as HTMLElement;
    if (!id) {
      return;
    }

    const offsetX = event.nativeEvent.offsetX;
    const { scrollLeft, scrollTop } = containerScrollState;
    const { clientHeight } = wrapperRef.current;
    const columnNum = Math.floor(offsetX / columnWidth);
    const rowNum = Number(id);

    const bottomRowNum = Math.floor((scrollTop + clientHeight) / rowHeight);

    const task = barTasks[rowNum];

    if (!task || task.x1 > -1 || task.type === 'group' || bottomRowNum - 1 < rowNum) {
      return;
    }

    hoveredRowRef.current.style.display = 'flex';
    setTimeBtnRef.current.style.display = "flex";

    const top = (rowNum - ~~(scrollTop / rowHeight)) * rowHeight
      - scrollTop % rowHeight
      + headerHeight;
    const left = (columnNum - ~~(scrollLeft / columnWidth)) * columnWidth
      - scrollLeft % columnWidth
      + taskListWidth;

    const date = dateSetup.dates[columnNum];

    hoveredRowRef.current.style.top = `${top}px`;
    hoveredRowRef.current.style.left = `${0}px`;

    setTimeBtnRef.current.style.left = `${left}px`;
    setTimeBtnRef.current.style.top = `${top + 2}px`;
    setTimeBtnRef.current.style.width = `${columnWidth * viewConstants.setTaskBtnUnits - 2}px`;

    let content;
    if (task.id === 'add') {
      content = 
      <div style={{
        width: 'max-content',
        whiteSpace: 'nowrap',
        marginLeft: '5px',
        color: '333',
        fontSize: '14px',
        fontWeight: 'bold',
      }}> {
        intl.formatMessage({ id: "newpage" })
      }</div>;
    } else {
      content = <TaskItem
        task={task}
        key={task.id}
        properties={properties}
        titleProperty={titleProperty}
        style={{
          width: 'max-content',
          whiteSpace: 'nowrap',
          marginLeft: '5px',
        }}
      />;

    }
    setTimeBtnContentState(content);

    setTimeBtnRef.current.onclick = () => {
      if (task.id === 'add') {
        if (onCreateTask) {
          onCreateTask({
            start: date,
            end: moment(date).add(viewConstants.setTaskBtnUnits, viewConstants.columnUnit).toDate(),
            group: task.group,
          });
        }
      } else {
        const newTask = { ...task, start: date, end: moment(date).add(viewConstants.setTaskBtnUnits, viewConstants.columnUnit).toDate() };
        onDateChange(newTask, []);
        setBarTasks(() => {
          return barTasks.map(t => {
            if (t.id === task.id) {
              return newTask;
            }
            return t;
          });
        });
      }

      setTimeBtnRef.current.style.display = "none";
    }
  }

  const gridProps: GridProps = {
    columnWidth,
    svgWidth,
    tasks: tasks,
    rowHeight,
    dates: dateSetup.dates,
    todayColor,
    viewMode,
    rtl,
  };
  const calendarProps: CalendarProps = {
    dateSetup,
    locale,
    viewMode,
    headerHeight,
    columnWidth,
    fontFamily,
    fontSize,
    rtl,
    middleDayRef,
  };
  const barProps: TaskGanttContentProps = {
    tasks: barTasks,
    dates: dateSetup.dates,
    ganttEvent,
    selectedTask,
    rowHeight,
    taskHeight,
    columnWidth,
    arrowColor,
    timeStep,
    fontFamily,
    fontSize,
    arrowIndent,
    svgWidth,
    rtl,
    setGanttEvent,
    setFailedTask,
    setSelectedTask: handleSelectedTask,
    onDateChange,
    onProgressChange,
    onDoubleClick,
    onDelete,
  };

  const tableProps = {
    rowHeight,
    rowWidth: listCellWidth,
    fontFamily,
    fontSize,
    tasks: barTasks,
    setTasks,
    setProperties,
    locale,
    headerHeight,
    scrollY,
    ganttHeight,
    horizontalContainerClass: styles.horizontalContainer,
    selectedTask,
    taskListRef,
    setSelectedTask: handleSelectedTask,
    onExpanderClick: handleExpanderClick,
    TaskListHeader,
    TaskListTable,
    visibleDateRange,
    taskListWidth,

    view,
  };

  return (
    <div style={{
      position: 'relative',
    }}>
      <div
        className={styles.wrapper}
        tabIndex={0}
        ref={wrapperRef}
        onKeyDown={handleKeyDown}
      >
        {listCellWidth && <TaskList {...tableProps} />}
        <TaskPinpoint
          tasks={tasks}
          direction={'left'}
          x={taskListWidth + 6}
          pointTo={navigateToTask}
          cellHeight={rowHeight}
          visibleDateRange={visibleDateRange}
          scrollY={scrollY}
          marginTop={headerHeight}
          ganttHeight={ganttHeight}

          properties={properties}
          titleProperty={titleProperty}
        />
        <TaskGantt
          gridProps={gridProps}
          calendarProps={calendarProps}
          barProps={barProps}
          properties={properties}
          ganttHeight={ganttHeight}
          scrollY={scrollY}
          scrollX={scrollX}
          visibleDateRange={visibleDateRange}
          onMouseMove={handleMouseMove}
          pointTo={navigateToTask}
        />
        <ViewController
          viewMode={viewMode}
          setViewMode={setViewMode}
          navigateToDate={navigateToDate}
          naviStep={naviStep}
        />
        <div
          style={{
            position: "absolute",
            left: listCellWidth,
            margin: '2px',
            padding: '8px 0px 0px 10px',
            backgroundColor: '#fff',
            color: '#333',
            fontWeight: 'bold',
            display: 'flex',
            flexDirection: 'row',
          }}
        >
          {
            intl.formatDate(visibleDateRange.leftest, {
              year: 'numeric',
              month: 'short',
            })
          }
          <div style={{
            width: 30,
            marginRight: -30,
            height: '22px',
            backgroundImage: 'linear-gradient(to right, white 20%, rgba(255, 255, 255, 0) 100%)',
          }} />
        </div>
        <div
          ref={hoveredRowRef}
          style={{
            width: '-webkit-fill-available',
            height: rowHeight - 2,
            border: '1px solid #ddd',
            position: 'absolute',
          }}

          onMouseLeave={() => {
            hoveredRowRef.current.style.display = "none";
          }}
        />
        <div
          ref={setTimeBtnRef}
          style={{
            position: "absolute",
            cursor: "crosshair",
            width: columnWidth - 2,
            height: rowHeight - 6,
            display: "flex",
            alignItems: "center",
            border: "1px solid #ddd",
            backgroundColor: "#ddd",
            opacity: 0.7,
            borderRadius: "5px",
          }}
          onMouseLeave={() => {
            setTimeBtnRef.current.style.display = "none";
            hoveredRowRef.current.style.display = "none";
          }}
        > 
        {
          setTimeBtnContent
        }
        </div>
       
        <TaskPinpoint
          tasks={barTasks}
          direction={'right'}
          x={6}
          pointTo={navigateToTask}
          cellHeight={rowHeight}
          visibleDateRange={visibleDateRange}
          scrollY={scrollY}
          marginTop={headerHeight}
          ganttHeight={ganttHeight}

          properties={properties}
          titleProperty={titleProperty}
        />
        {ganttEvent.changedTask && (
          <Tooltip
            arrowIndent={arrowIndent}
            rowHeight={rowHeight}
            svgContainerHeight={svgContainerHeight}
            svgContainerWidth={svgContainerWidth}
            fontFamily={fontFamily}
            fontSize={fontSize}
            scrollX={scrollX}
            scrollY={scrollY}
            task={ganttEvent.changedTask}
            headerHeight={headerHeight}
            taskListWidth={taskListWidth}
            TooltipContent={TooltipContent}
            rtl={rtl}
            svgWidth={svgWidth}
          />
        )}
        <VerticalScroll
          ganttFullHeight={ganttFullHeight}
          ganttHeight={ganttHeight}
          headerHeight={headerHeight}
          scroll={scrollY}
          onScroll={handleScrollY}
          rtl={rtl}
        />
      </div>
      <HorizontalScroll
        svgWidth={svgWidth}
        taskListWidth={taskListWidth}
        scroll={scrollX}
        rtl={rtl}
        onScroll={handleScrollX}
        setContainerEdgeState={setContainerEdgeState}
        setContainerScrollState={setContainerScrollState}
        containerScrollState={containerScrollState}
      />
    </div>
  );
};
