import React, { useRef, useEffect, useState, useMemo } from "react";
import { GridProps, Grid } from "../grid/grid";
import { CalendarProps, Calendar } from "../calendar/calendar";
import { TaskGanttContentProps, TaskGanttContent } from "./task-gantt-content";
import { ArrowLeftSquareFill } from '@styled-icons/bootstrap/ArrowLeftSquareFill';
import { ArrowRightSquareFill } from '@styled-icons/bootstrap/ArrowRightSquareFill';
import styles from "./gantt.module.css";
import { Task } from "src/gantt-timeline/types/public-types";
import { TaskItem } from "./TaskItem";

export type TaskGanttProps = {
  gridProps: GridProps;
  calendarProps: CalendarProps;
  barProps: TaskGanttContentProps;
  properties: [];
  ganttHeight: number;
  scrollY: number;
  scrollX: number;
  visibleDateRange: {
    leftest: Date;
    rightest: Date;
    leftestIndex: number;
    rightestIndex: number;
  };
  onMouseMove: (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => void;
  pointTo: (task: Task, direction: string) => void;
};
export const TaskGantt = ({
  gridProps,
  calendarProps,
  barProps,
  properties,
  ganttHeight,
  scrollY,
  scrollX,
  visibleDateRange,
  onMouseMove,
  pointTo,
}) => {
  const ganttSVGRef = useRef<SVGSVGElement>(null);
  const horizontalContainerRef = useRef<HTMLDivElement>(null);
  const verticalGanttContainerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (horizontalContainerRef.current) {
      horizontalContainerRef.current.scrollTop = scrollY;
    }
  }, [scrollY]);

  useEffect(() => {
    if (verticalGanttContainerRef.current) {
      verticalGanttContainerRef.current.scrollLeft = scrollX;
    }
  }, [scrollX]);

  const barHeight = barProps.rowHeight * 0.8;
  const [barXPositions, setBarXPositions] = useState([]);

  useEffect(() => {
    if (barProps && barProps.tasks) {
      const xPositions = barProps.tasks.map(task => {
        return [task.x1, task.x2];
      });
      setBarXPositions(xPositions);
    }
  }, [barProps]);

  const handler = (mouseDownEvent, index, startXPos) => {
    console.log('handler', mouseDownEvent, index, startXPos);
    const startPoint = { x: mouseDownEvent.pageX, y: mouseDownEvent.pageY };

    function onMouseMove(mouseMoveEvent) {
      const newXPos = [
        startXPos[0] - startPoint.x + mouseMoveEvent.pageX,
        startXPos[1] - startPoint.x + mouseMoveEvent.pageX,
      ];

      const newXPositions = [...barXPositions];
      newXPositions[index] = newXPos;
      setBarXPositions(newXPositions);
    }

    function onMouseUp(mouseMoveEvent) {
      document.body.removeEventListener("mousemove", onMouseMove);
      changeDate(barProps.tasks[index], index,
        [startXPos[0] - startPoint.x + mouseMoveEvent.pageX,
        startXPos[1] - startPoint.x + mouseMoveEvent.pageX,]);
    }

    document.body.addEventListener("mousemove", onMouseMove);
    document.body.addEventListener("mouseup", onMouseUp, { once: true });
  };

  const getDateFromX = (x: number) => {
    const dates = barProps.dates;

    const flooredIndex = Math.floor(x / barProps.columnWidth);
    const roundedIndex = x / barProps.columnWidth - flooredIndex > 0.5 ? flooredIndex + 1 : flooredIndex

    return {
      date: dates[roundedIndex],
      x: roundedIndex * barProps.columnWidth
    };
  }

  const changeDate = (task, index, xPos) => {
    let start = getDateFromX(xPos[0]);
    let end = getDateFromX(xPos[1]);

    barProps.onDateChange({
      ...task,
      start: start.date,
      end: end.date
    }, []);

    const newXPositions = [...barXPositions];
    newXPositions[index] = [start.x, start.x];
    setBarXPositions(newXPositions);
  }

  const titleProperty = useMemo(() => {
    return properties.find((p) => p.type === "Title");
  }, [properties]);

  return (
    <div
      className={styles.ganttVerticalContainer}
      ref={verticalGanttContainerRef}
      dir="ltr"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={gridProps.svgWidth}
        height={calendarProps.headerHeight}
        fontFamily={barProps.fontFamily}
      >
        <Calendar {...calendarProps} />
      </svg>
      <div
        ref={horizontalContainerRef}
        className={styles.horizontalContainer}
        style={
          ganttHeight
            ? { height: ganttHeight, width: gridProps.svgWidth, backgroundColor: '#fcfcfc', position: 'relative' }
            : { width: gridProps.svgWidth, backgroundColor: '#fcfcfc' }
        }

        onMouseMove={onMouseMove}
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width={gridProps.svgWidth}
          height={barProps.rowHeight * barProps.tasks.length}
          fontFamily={barProps.fontFamily}
          ref={ganttSVGRef}
        >
          <Grid {...gridProps} />
        </svg>

        <div
          style={{
            position: "absolute",
            top: 0,
            left: 0,
            width: '-webkit-fill-available',
            height: '-webkit-fill-available',
          }}
        >
          {
            barProps.tasks.map((task, index) => {
              const xPos = barXPositions[index];
              return (
                <div
                  key={task.id + index}
                  id={index + ''}
                  style={{
                    position: 'relative',
                    height: barProps.rowHeight,
                    width: '-webkit-fill-available',
                    fontSize: '12px',
                    backgroundColor: 'transparent',
                    display: 'flex',
                    flexDirection: 'row',
                    alignItems: 'center',
                  }}>
                  {
                    xPos && xPos[0] > -1 &&
                    <div
                      style={{
                        position: 'absolute',
                        left: xPos[0],
                        width: xPos[1] - xPos[0] - 2,
                        height: barHeight,
                        backgroundColor: task.backgroundColor || 'white',
                        border: '1px solid #ccc',
                        borderRadius: '5px',
                        display: 'flex',
                        alignItems: 'center',
                        flexDirection: 'row',
                        alignSelf: 'center',
                        justifyContent: 'space-between',
                      }}

                    >
                      <ResizeHandler
                        startPos={{ x: xPos[0] }}
                        rePos={({ x }) => {
                          let newXPos = [x, xPos[1]];
                          setBarXPositions(prev => {
                            return [...prev.slice(0, index), newXPos, ...prev.slice(index + 1)];
                          });
                        }}
                        actionDone={(x) => {
                          changeDate(task, index, [x, xPos[1]]);
                        }}
                      />
                      <div
                        style={{
                          width: '-webkit-fill-available',
                          zIndex: 2,
                          height: '-webkit-fill-available',
                          cursor: 'grab',
                        }}

                        onMouseDown={(event) => handler(event, index, xPos)}
                      />
                      <ResizeHandler
                        startPos={{ x: xPos[1] }}
                        rePos={({ x }) => {

                          let newXPos = [xPos[0], x];
                          setBarXPositions(prev => {
                            return [...prev.slice(0, index), newXPos, ...prev.slice(index + 1)];
                          });
                        }}
                        actionDone={(x) => {
                          changeDate(task, index, [xPos[0], x]);
                        }}
                      />
                    </div>
                  }
                  {
                    xPos && xPos[0] > -1 && visibleDateRange.leftest < task.start &&
                    <TaskItem
                      task={task}
                      key={task.id + index}
                      properties={properties}
                      titleProperty={titleProperty}
                      style={{
                        position: 'absolute',
                        left: xPos[0] + 5,
                        height: '100%'
                      }}
                    />
                  }
                </div>
              )
            }
            )}
        </div>
      </div>
    </div>
  );
};

const ResizeHandler = ({ startPos, rePos, actionDone }) => {
  const [hovered, setHovered] = React.useState(false);

  const handler = (mouseDownEvent) => {
    const startPoint = { x: mouseDownEvent.pageX, y: mouseDownEvent.pageY };

    function onMouseMove(mouseMoveEvent) {
      rePos({
        x: startPos.x - startPoint.x + mouseMoveEvent.pageX,
      });
    }
    function onMouseUp(mouseMoveEvent) {
      document.body.removeEventListener("mousemove", onMouseMove);
      actionDone(startPos.x - startPoint.x + mouseMoveEvent.pageX);
    }

    document.body.addEventListener("mousemove", onMouseMove);
    document.body.addEventListener("mouseup", onMouseUp, { once: true });
  };

  return (
    <div
      style={{
        width: 6,
        height: 24,
        backgroundColor: hovered ? '#999' : 'transparent',
        cursor: 'col-resize',
        zIndex: 5
      }}

      onMouseEnter={() => setHovered(true)}
      onMouseLeave={() => setHovered(false)}

      onMouseDown={handler}
    />
  );
}
