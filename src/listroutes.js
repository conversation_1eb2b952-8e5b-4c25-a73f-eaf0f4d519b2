import React from 'react'

// import RilsHeader from './components/rils/RilsHeader';

const Rils = React.lazy(() => import('./views/pages/Rils'));
const RilsHeader = React.lazy(() => import('./components/rils/RilsHeader'))

// const Slides = React.lazy(() => import('./views/pages/Slides'));
// const SlidesHeader = React.lazy(() => import('./components/slides/SlidesHeader'))

const routes = [
  { path: '/rils', name: 'RIL', component: Rils, header: <RilsHeader/> },
  // { path: '/slides', name: 'Slides', component: Slides, header: <SlidesHeader/> }
]

export default routes
