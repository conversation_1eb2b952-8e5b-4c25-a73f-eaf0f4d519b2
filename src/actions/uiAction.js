import * as types from '../constants/actionTypes';

export const toggleTakeTicketModal = (isVisible, item) => {
  return async function(dispatch) {
    dispatch({type: types.TAKE_TICKET_MODAL, value:{isVisible, item}});
  }
}

export const updateOKRListFitlers = (filters) => dispatch => {
  dispatch({
    type: types.OKR_LIST_FILTERS,
    value: filters,
  });

  return Promise.resolve();
};

export const showMessage = (message) => {
  return async function(dispatch) {
    dispatch({type: types.OPERATION_SUCCESS, value: message});
  }
}
