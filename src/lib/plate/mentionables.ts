import { TComboboxItem } from '@udecode/plate-combobox';

export const MENTIONABLES: TComboboxItem[] = [
  { key: '0', text: '<PERSON><PERSON><PERSON>' },
  { key: '1', text: '<PERSON><PERSON>' },
  {
    key: '2',
    text: 'Admiral <PERSON>',
  },
  {
    key: '3',
    text: 'Admiral <PERSON><PERSON>',
  },
  {
    key: '4',
    text: 'Admiral <PERSON>',
  },
  { key: '5', text: 'Admiral <PERSON>' },
  { key: '6', text: 'Admiral <PERSON>' },
  {
    key: '7',
    text: 'Admiral <PERSON><PERSON>',
  },
  { key: '8', text: '<PERSON>' },
  {
    key: '9',
    text: 'Admiral U.O. Statura',
  },
  { key: '10', text: '<PERSON><PERSON>' },
  { key: '11', text: '<PERSON>' },
  {
    key: '12',
    text: '<PERSON><PERSON> and <PERSON>rit Astarte',
  },
  { key: '13', text: '<PERSON><PERSON> <PERSON>' },
  { key: '14', text: 'Alme<PERSON>' },
  { key: '15', text: '<PERSON><PERSON>' },
  { key: '16', text: 'Amee' },
  { key: '17', text: 'AP-5' },
  { key: '18', text: '<PERSON><PERSON><PERSON>x' },
  { key: '19', text: 'Artoo' },
  { key: '20', text: 'Arvel Crynyd' },
  { key: '21', text: 'Asajj Ventress' },
  { key: '22', text: 'Aurra Sing' },
  { key: '23', text: 'AZI-3' },
  { key: '24', text: 'Bala-Tik' },
  { key: '25', text: 'Barada' },
  { key: '26', text: 'Bargwill Tomder' },
  { key: '27', text: 'Baron Papanoida' },
  { key: '28', text: 'Barriss Offee' },
  { key: '29', text: 'Baze Malbus' },
  { key: '30', text: 'Bazine Netal' },
  { key: '31', text: 'BB-8' },
  { key: '32', text: 'BB-9E' },
  { key: '33', text: 'Ben Quadinaros' },
  { key: '34', text: 'Berch Teller' },
  { key: '35', text: 'Beru Lars' },
  { key: '36', text: 'Bib Fortuna' },
  {
    key: '37',
    text: 'Biggs Darklighter',
  },
  { key: '38', text: 'Black Krrsantan' },
  { key: '39', text: 'Bo-Katan Kryze' },
  { key: '40', text: 'Boba Fett' },
  { key: '41', text: 'Bobbajo' },
  { key: '42', text: 'Bodhi Rook' },
  { key: '43', text: 'Borvo the Hutt' },
  { key: '44', text: 'Boss Nass' },
  { key: '45', text: 'Bossk' },
  {
    key: '46',
    text: 'Breha Antilles-Organa',
  },
  { key: '47', text: 'Bren Derlin' },
  { key: '48', text: 'Brendol Hux' },
  { key: '49', text: 'BT-1' },
  { key: '50', text: 'C-3PO' },
];
