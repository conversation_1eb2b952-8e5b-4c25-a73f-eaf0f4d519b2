<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8" />
  <link rel="icon" href="/favicon.png" />
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1.0, user-scalable=no" />
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
  <meta name="theme-color" content="#000000" />
  <meta name="description"
    content="All-in-One AI Workspace – From Mind Maps to Slides & Docs: Streamline Creative Brainstorming with AI Flow & Mind Maps, Craft Slides Effortlessly with Markdown-AI Integration in xSlide, Boost Writing & Reading Efficiency Everywhere with AI Chrome/Edge Extensions. Experience an AI-Copiloted Notion-like Workspace" />
  <meta name="keywords" ,
    content="funblocks, ai work platform, ai productivity, gpt language model, ai brainstorming, mind mapping, creative thinking, ai slide creation, markdown slides, ai writing assistant, ai reading assistant, chrome extension, edge extension, ai workspace, ai collaboration, notion workspace, Notion AI, ai documents, ai slides, ai mind maps, ai copilot" />
  <link rel="apple-touch-icon" href="/logo192.png" />
  <!--
      manifest.json provides metadata used when your web app is installed on a
      user's mobile device or desktop. See https://developers.google.com/web/fundamentals/web-app-manifest/
    -->
  <link rel="manifest" href="/manifest.json" />
  <!--
      Notice the use of %PUBLIC_URL% in the tags above.
      It will be replaced with the URL of the `public` folder during the build.
      Only files inside the `public` folder can be referenced from the HTML.

      Unlike "/favicon.ico" or "favicon.ico", "%PUBLIC_URL%/favicon.ico" will
      work correctly both with client-side routing and a non-root public URL.
      Learn how to configure a non-root public URL by running `npm run build`.
    -->
  <title>FunBlocks</title>
</head>

<body style="margin: 0; touch-action: none;" >
  <noscript>You need to enable JavaScript to run this app.</noscript>
  <div id="root">
    <div style="height: 100vh; display: flex; align-items: center; justify-content: center;">loading...</div>
  </div>
  <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm start` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
    -->
</body>

</html>