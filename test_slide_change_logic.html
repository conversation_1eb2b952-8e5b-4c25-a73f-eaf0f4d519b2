<!DOCTYPE html>
<html>
<head>
    <title>测试幻灯片切换逻辑</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 10px; border: 1px solid #ccc; }
        .result { background: #f0f0f0; padding: 10px; margin: 10px 0; }
        button { margin: 5px; padding: 10px; }
        .log { background: #fff; border: 1px solid #ddd; padding: 10px; height: 200px; overflow-y: auto; font-family: monospace; font-size: 12px; }
    </style>
</head>
<body>
    <h1>幻灯片切换逻辑测试</h1>
    
    <div class="test-section">
        <h2>模拟幻灯片切换和编辑器变化</h2>
        <button onclick="simulateSlideChange(1, 0)">模拟幻灯片切换到 (1,0)</button>
        <button onclick="simulateDocChange()">模拟编辑器内容变化</button>
        <button onclick="clearLog()">清空日志</button>
        <div id="log" class="log"></div>
    </div>

    <script>
        let lastSlideChangeTime = 0;
        
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        function simulateSlideChange(indexh, indexv) {
            log(`🎯 幻灯片切换到 (${indexh}, ${indexv})`);
            lastSlideChangeTime = Date.now();
            log(`📝 记录幻灯片切换时间: ${lastSlideChangeTime}`);
            
            // 模拟滚动到对应行
            setTimeout(() => {
                log(`📜 编辑器滚动完成`);
            }, 100);
        }
        
        function simulateDocChange() {
            const timeSinceLastSlideChange = Date.now() - lastSlideChangeTime;
            log(`📄 编辑器内容变化，距离上次幻灯片切换: ${timeSinceLastSlideChange}ms`);
            
            if (timeSinceLastSlideChange > 1000) {
                log(`✅ 发送 slide 消息到预览`);
            } else {
                log(`❌ 跳过 slide 消息（距离上次幻灯片切换太近）`);
            }
        }
        
        // 页面加载时的初始化
        window.onload = function() {
            log('🚀 测试页面加载完成');
            log('💡 点击按钮测试幻灯片切换逻辑');
        };
    </script>
</body>
</html>
