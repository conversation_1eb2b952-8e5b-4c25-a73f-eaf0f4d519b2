# Artifact 编辑功能测试指南

## 测试环境准备

1. 确保项目依赖已安装
2. 启动开发服务器
3. 打开 AIFlow 功能

## 测试步骤

### 1. 创建包含 Artifact 的 AINode

**方法一：使用 AI 生成 Mermaid 图表**
1. 创建一个新的 AI 节点
2. 输入提示词，例如：
   ```
   请创建一个简单的流程图，展示用户注册流程
   ```
3. 等待 AI 生成包含 Mermaid 代码的响应

**方法二：使用 AI 生成 SVG 图形**
1. 创建一个新的 AI 节点
2. 输入提示词，例如：
   ```
   请创建一个简单的 SVG 图标，比如一个笑脸
   ```
3. 等待 AI 生成包含 SVG 代码的响应

### 2. 测试编辑按钮显示

1. 当 AI 生成包含 Mermaid 或 SVG 内容的响应后
2. 应该会看到一个 "View Mermaid Content" 或 "View SVG Content" 的按钮
3. 点击该按钮，Artifact 组件会在节点右侧显示
4. 将鼠标悬停在 Artifact 组件上
5. **验证点**：应该在右上角看到编辑按钮（铅笔图标）

### 3. 测试编辑模态框打开

1. 点击编辑按钮
2. **验证点**：应该弹出编辑模态框
3. **验证点**：模态框标题应该显示 "Edit Mermaid Code" 或 "Edit SVG Code"
4. **验证点**：文本框中应该包含当前的代码内容
5. **验证点**：文本框应该使用等宽字体显示

### 4. 测试代码编辑功能

**测试 Mermaid 代码编辑：**
1. 在编辑框中修改 Mermaid 代码，例如：
   ```mermaid
   graph TD
       A[开始] --> B[输入用户名]
       B --> C[输入密码]
       C --> D[验证信息]
       D --> E[注册成功]
   ```
2. 修改为：
   ```mermaid
   graph TD
       A[开始] --> B[输入用户名]
       B --> C[输入密码]
       C --> D[验证信息]
       D --> E[注册成功]
       D --> F[验证失败]
       F --> B
   ```

**测试 SVG 代码编辑：**
1. 在编辑框中修改 SVG 代码，例如改变颜色或大小

### 5. 测试保存功能

**方法一：点击保存按钮**
1. 修改代码后，点击 "Save" 按钮
2. **验证点**：模态框应该关闭
3. **验证点**：Artifact 显示应该更新为新的内容
4. **验证点**：节点内容应该包含更新后的代码

**方法二：使用键盘快捷键**
1. 修改代码后，按 `Ctrl+Enter` (Windows/Linux) 或 `Cmd+Enter` (Mac)
2. **验证点**：应该有相同的保存效果

### 6. 测试取消功能

**方法一：点击取消按钮**
1. 修改代码但不保存
2. 点击 "Cancel" 按钮
3. **验证点**：模态框关闭，内容不应该改变

**方法二：使用 Escape 键**
1. 修改代码但不保存
2. 按 `Escape` 键
3. **验证点**：模态框关闭，内容不应该改变

**方法三：点击关闭按钮**
1. 修改代码但不保存
2. 点击模态框右上角的关闭按钮（X）
3. **验证点**：模态框关闭，内容不应该改变

### 7. 测试拖拽功能

1. 打开编辑模态框
2. 点击并拖拽模态框的标题栏
3. **验证点**：模态框应该可以在屏幕上移动

### 8. 测试多个 Artifact 的情况

1. 创建一个包含多个 Mermaid 或 SVG 代码块的 AI 响应
2. 分别点击不同的 Artifact 按钮
3. 编辑不同的 Artifact
4. **验证点**：每个 Artifact 的编辑应该独立，不互相影响

## 预期结果

### 正常情况
- 编辑按钮正确显示
- 模态框正常打开和关闭
- 代码编辑功能正常
- 保存后内容正确更新
- 取消操作不影响原内容

### 错误处理
- 无效的 Mermaid 代码应该能够保存（由 Mermaid 渲染器处理错误）
- 无效的 SVG 代码应该能够保存（由浏览器处理错误）
- 空内容应该能够正常处理

## 测试用例示例

### Mermaid 测试代码
```mermaid
graph TD
    A[Start] --> B{Is it?}
    B -->|Yes| C[OK]
    C --> D[Rethink]
    D --> B
    B ---->|No| E[End]
```

### SVG 测试代码
```svg
<svg width="100" height="100" xmlns="http://www.w3.org/2000/svg">
  <circle cx="50" cy="50" r="40" stroke="black" stroke-width="3" fill="red" />
  <text x="50" y="55" text-anchor="middle" fill="white" font-size="16">Hi</text>
</svg>
```

## 常见问题排查

1. **编辑按钮不显示**
   - 检查是否正确传递了 `onEdit` 属性
   - 检查 Artifact 组件的条件判断

2. **模态框不打开**
   - 检查 `handleArtifactEdit` 函数是否正确调用
   - 检查状态更新是否正常

3. **保存后内容不更新**
   - 检查 `handleArtifactSave` 函数中的正则表达式
   - 检查 `updateNodeData` 调用是否正确

4. **国际化文本不显示**
   - 检查翻译键是否正确添加
   - 检查 `intl.formatMessage` 调用是否正确

通过以上测试步骤，可以全面验证 Artifact 编辑功能的正确性和用户体验。
