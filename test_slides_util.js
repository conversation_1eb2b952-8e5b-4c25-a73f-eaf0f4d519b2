// 简单的测试脚本来验证 getLineNumberFromSlideIndex 函数

const getLineNumberFromSlideIndex = (content, indexh, indexv) => {
  const lines = content.split('\n');
  let currentH = 0;
  let currentV = 0;

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    
    if (/^--- *$/.test(line)) {
      // 遇到水平分隔符
      currentH++;
      currentV = 0;
      
      // 如果找到了目标幻灯片，返回下一行的行号（分隔符后的第一行）
      if (currentH === indexh && currentV === indexv) {
        return i + 2; // +2 因为要跳过分隔符行，并且行号从1开始
      }
    } else if (/^-- *$/.test(line)) {
      // 遇到垂直分隔符
      currentV++;
      
      // 如果找到了目标幻灯片，返回下一行的行号（分隔符后的第一行）
      if (currentH === indexh && currentV === indexv) {
        return i + 2; // +2 因为要跳过分隔符行，并且行号从1开始
      }
    }
  }

  // 如果是第一张幻灯片 (0,0)，返回第1行
  if (indexh === 0 && indexv === 0) {
    return 1;
  }

  // 如果没找到，返回第1行
  return 1;
};

// 测试数据
const sampleMarkdown = `# 第一张幻灯片
这是第一张幻灯片的内容

---

# 第二张幻灯片
这是第二张幻灯片的内容

--

# 第二张幻灯片的子页面
这是第二张幻灯片的子页面内容

---

# 第三张幻灯片
这是第三张幻灯片的内容`;

// 测试用例
console.log('测试 (0,0):', getLineNumberFromSlideIndex(sampleMarkdown, 0, 0)); // 应该是 1
console.log('测试 (1,0):', getLineNumberFromSlideIndex(sampleMarkdown, 1, 0)); // 应该是 6
console.log('测试 (1,1):', getLineNumberFromSlideIndex(sampleMarkdown, 1, 1)); // 应该是 11
console.log('测试 (2,0):', getLineNumberFromSlideIndex(sampleMarkdown, 2, 0)); // 应该是 16

// 打印每一行的内容和行号来验证
console.log('\n每行内容:');
sampleMarkdown.split('\n').forEach((line, index) => {
  console.log(`${index + 1}: ${line}`);
});
