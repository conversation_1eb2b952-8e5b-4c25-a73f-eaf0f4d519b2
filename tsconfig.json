{
  "compilerOptions": {
    "target": "es5",
    "lib": [
      "dom",
      "dom.iterable",
      "esnext"
    ],
    "allowJs": true,
    "skipLibCheck": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "strict": false,
    "forceConsistentCasingInFileNames": true,
    "noFallthroughCasesInSwitch": true,
    "module": "esnext",
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"]
    }
  },
  "files": ["types/twin.d.ts", "./src/components/flow/PromptNode.js", "./src/components/flow/AIActionList.js", "./src/components/flow/TodoList.js", "./src/components/flow/PromptTypeDropdownAndActionButton.js"],, "./src/components/flow/PromptNode.js"
  "include": [
    "src"
  ],
  "exclude": [
    "node_modules"
  ]
}
