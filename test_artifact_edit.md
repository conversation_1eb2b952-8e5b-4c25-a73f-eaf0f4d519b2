# Artifact 编辑功能实现总结

## 实现的功能

1. **修改了 Artifact 组件**
   - 在 `src/components/flow/Artifact.js` 中修改了编辑按钮的显示条件
   - 原来只在 `inDoc` 为 true 时显示编辑按钮
   - 现在改为 `(inDoc || onEdit)` 时显示编辑按钮
   - 这样在 AINode 中使用 Artifact 时，只要传递了 `onEdit` 属性，就会显示编辑按钮

2. **创建了 ArtifactEditModal 组件**
   - 新建了 `src/components/flow/ArtifactEditModal.js`
   - 使用 Material-UI 的 Dialog 组件创建模态框
   - 支持可拖拽的模态框（使用 react-draggable）
   - 提供代码编辑功能，支持 Mermaid 和 SVG 代码编辑
   - 支持键盘快捷键：Escape 关闭，Ctrl+Enter 保存

3. **修改了 AINode 组件**
   - 在 `src/components/flow/AINode.js` 中添加了编辑功能
   - 添加了状态管理：`editModalVisible` 和 `editingArtifact`
   - 添加了处理函数：
     - `handleArtifactEdit`: 打开编辑模态框
     - `handleArtifactSave`: 保存编辑后的内容并更新节点数据
     - `handleArtifactEditClose`: 关闭编辑模态框
   - 修改了 Artifact 组件的调用，添加了 `onEdit` 属性
   - 在组件末尾添加了 ArtifactEditModal 组件

## 功能流程

1. **用户点击编辑按钮**
   - 在 AINode 中显示的 Artifact 组件现在会显示编辑按钮
   - 点击编辑按钮会调用 `handleArtifactEdit` 函数

2. **打开编辑模态框**
   - `handleArtifactEdit` 设置 `editingArtifact` 和 `editModalVisible` 状态
   - ArtifactEditModal 组件显示，包含当前 artifact 的内容

3. **编辑代码**
   - 用户可以在模态框中的文本框内编辑 Mermaid 或 SVG 代码
   - 支持多行编辑，使用等宽字体
   - 提供适当的占位符文本

4. **保存更改**
   - 用户点击保存按钮或使用 Ctrl+Enter 快捷键
   - `handleArtifactSave` 函数被调用
   - 更新 artifacts 数组中的内容
   - 更新当前选中的 artifact（如果是正在编辑的）
   - 更新节点内容中的相应代码块
   - 调用 `updateNodeData` 更新节点数据

5. **关闭模态框**
   - 保存后或点击取消/关闭按钮时关闭模态框
   - 重置编辑状态

## 技术特点

1. **智能内容更新**
   - 根据 artifact 类型（Mermaid/SVG）使用不同的正则表达式更新内容
   - 确保只更新对应的代码块，不影响其他内容

2. **状态同步**
   - 同时更新 artifacts 数组、selectedArtifact 和节点内容
   - 保证数据一致性

3. **用户体验**
   - 可拖拽的模态框
   - 键盘快捷键支持
   - 适当的占位符和提示文本
   - 响应式设计

4. **代码结构**
   - 使用 React.useCallback 优化性能
   - 清晰的函数职责分离
   - 良好的错误处理

## 使用方式

在 AINode 中，当有 Artifact 内容时：
1. Artifact 组件会显示编辑按钮
2. 点击编辑按钮打开编辑模态框
3. 在模态框中修改 Mermaid 或 SVG 代码
4. 点击保存或使用快捷键保存更改
5. 更改会立即反映在节点内容中

这个实现完全满足了需求：在 AINode 中展现 Artifact 时显示编辑按钮，点击后弹出模态框供用户修改代码，修改后更新节点数据。
